{{#if template}}
<template>
  <div class="{{ properCase name }}">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          :row-class-name="tableRowClassName"
          header-row-class-name="ps-table-header-row"
          class="ps-table-tree"
          :tree-props="{ children: 'children_list', hasChildren: 'has_children' }"
        >
          <el-table-column prop="name" :label="$t('table.organization_name')"></el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>
{{/if}}

{{#if script}}
<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce } from '@/utils'

export default {
  name: '{{ properCase name }}',
  components: {},
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        name: {
          type: 'input',
          label: '',
          value: '',
          placeholder: this.$t('placeholder.role_search')
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {},
    // 节下流咯
    searchHandle: debounce(function() {
      // this.currentPage = 1;
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      // this.currentPage = 1;
      // this.tableData = []
      this.initLoad()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      // this.getAccountList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      // this.getAccountList()
    }
  }
}
</script>
{{/if}}

{{#if style}}
<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>
{{/if}}
