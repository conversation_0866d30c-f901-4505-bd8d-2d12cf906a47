{{#if template}}
<template>
  <div class="{{ className }} container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="align-r">
        <button-icon color="origin" type="add" v-permission="['background.admin.role.list_export']">测试按钮1</button-icon>
        <button-icon color="plain" type="mul">测试按钮2</button-icon>
        <button-icon color="plain" type="export">导出EXCEL</button-icon>
        <button-icon color="plain" @click="gotoPrint">打印</button-icon>
        <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column :index="indexMethod" v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text">操作</el-button>
            </template>
          </table-column>
        </el-table>
        <!--
        <custom-table
          border
          v-loading="isLoading"
          :table-data="tableData"
          :table-setting="currentTableSetting"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        />
        -->
        <!-- table end -->
      </div>
      <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>
{{/if}}

{{#if script}}
<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, deepClone, getRequestParams } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入

export default {
  name: '{{ properCase name }}',
  mixins: [exportExcel, report], // activatedLoadData
  data() {
    return {
      isLoading: false, // 请求数据loading
      // 搜索
      searchFormSetting: {
        category: {
          type: 'select',
          label: '分类',
          value: [],
          placeholder: '请选择分类',
          multiple: true,
          collapseTags: true,
          dataList: [{
            label: '全部',
            value: ''
          }]
        },
        name: {
          type: 'input',
          label: '食材名称',
          value: '',
          placeholder: '请输入食材名称'
        }
      },
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // table数据
      tableSetting: [
        { label: '创建时间', key: 'create_time' },
        { label: '开票金额', key: 'invoice_fee', type: 'money' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "100" }
      ],
      collect: [ // 统计
        { key: 'total_charge_off_money', value: 0, label: '合计冲销金额:￥', type: 'money' }
      ],
      printType: '{{ properCase name }}' // 打印type
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    // 初始化
    initLoad() {
      this.getData()
    },
    // 防抖优化下
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getData()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.searchHandle()
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getReceiptList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
    },
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1)
    },
    // 获取数据
    async getData() {
      // 有请求时跳过
      if (this.isLoading) return
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const [err, res] = await this.$to(this.$apos['test'](params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出弹窗
    gotoExport() {
      const option = {
        type: '{{ properCase name }}',
        xhr: 'apiBackgroundAdminAccountListExportPost',
        params: {}
      }
      this.exportHandle(option)
    },
    // 打印
    gotoPrint() {
      const params = getRequestParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_type: this.printType,
          print_title: 'xxxx表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'xxx', // 请求的api
          show_print_header_and_footer: true, // 打印页头页尾
          show_summary: false, // 合计
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            date_type: this.currentType,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>
{{/if}}

{{#if style}}
<style lang="scss" scoped>
.{{ className }} {}
</style>
{{/if}}
