const { notEmpty } = require('../utils.js')
// const path = require('path')

module.exports = {
  description: '创建页面',
  prompts: [{
    type: 'input', // 命令方式，input-输入，list-选择，confirm-是否
    name: 'viewPath',
    message: '请输入页面存放路径(./views/???)'
    // when: answers => {
    //   return answers.type === 'default'
    // }
  },
  // 当存在多选的情况可以使用checkbox类型
  // {
  //    type: 'checkbox',
  //    name: 'types',
  //    message: 'api types',
  //    choices: () => {
  //        return ['create', 'update', 'get', 'delete', 'check', 'fetchList', 'fetchPage'].map((type) => ({
  //            name: type,
  //            value: type,
  //            checked: true
  //        }))
  //    }
  // },
  {
    type: 'input',
    name: 'name',
    message: '请输入页面名称',
    validate: notEmpty('name')
  },
  {
    type: 'checkbox',
    name: 'blocks',
    message: 'Blocks:',
    choices: [{
      name: '<template>',
      value: 'template',
      checked: true
    },
    {
      name: '<script>',
      value: 'script',
      checked: true
    },
    {
      name: 'style',
      value: 'style',
      checked: true
    }
    ],
    validate(value) {
      if (value.indexOf('script') === -1 && value.indexOf('template') === -1) {
        return 'View require at least a <script> or <template> tag.'
      }
      return true
    }
  }
  ],
  actions: data => {
    // const { fileName, name, from } = data
    const name = '{{name}}'
    const path = (data.type = `views/${data.viewPath}`)
    const actions = [{
      type: 'add',
      path: `src/${path}/${data.name}.vue`,
      templateFile: 'plop-templates/initLoad/index.hbs',
      data: {
        name: name, // 传递的数据给模板文件
        template: data.blocks.includes('template'),
        script: data.blocks.includes('script'),
        style: data.blocks.includes('style')
      }
    }]

    return actions
  }
}
