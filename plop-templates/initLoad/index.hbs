{{#if template}}
<template>
  <div class="{{ properCase name }}"></div>
</template>
{{/if}}

{{#if script}}
<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: '{{ properCase name }}',
  components: [],
  props: {},
  // mixins: [activatedLoadData, exportExcel],
  data() {
    return {}
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {},
    // 导出弹窗
    gotoExport() {
      const option = {
        type: "AccountList",
        xhr: 'apiBackgroundAdminAccountListExportPost',
        params: {}
      }
      this.exportHandle(option)
    }
  }
}
</script>
{{/if}}

{{#if style}}
<style lang="scss" scoped>
</style>
{{/if}}
