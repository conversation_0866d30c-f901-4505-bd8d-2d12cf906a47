import NP from 'number-precision'

export default {
  // 格式化金额并保留两位小数, 不四舍五入
  formatMoneyFloat(val) {
    if (!val) return '0.00'
    if (typeof val === 'number') {
      return formatTwoDecimals(NP.divide(val, 100))
    } else if (typeof val === 'string' && !isNaN(Number(val))) {
      return formatTwoDecimals(NP.divide(val, 100))
    } else {
      return '0.00'
    }
  }
}

// 格式化两位小数，不四舍五入
function formatTwoDecimals(num) {
  // 转为字符串
  let str = num.toString();
  // 获取小数点位置
  let decimalIndex = str.indexOf('.');

  if (decimalIndex === -1) {
    // 如果没有小数点，添加.00
    return str + '.00';
  } else {
    let decimals = str.substring(decimalIndex + 1);
    if (decimals.length >= 2) {
      // 如果小数位超过2位，直接截取前两位
      return str.substring(0, decimalIndex + 3);
    } else if (decimals.length === 1) {
      // 如果只有一位小数，补0
      return str + '0';
    } else {
      // 如果没有小数位，补00
      return str + '00';
    }
  }
}
