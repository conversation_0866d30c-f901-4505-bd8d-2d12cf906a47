
export default {
  // 格式化分数并保留两位小数，进行四舍五入
  formatScore(val) {
    if (!val) return '0.00'
    if (typeof val === 'number') {
      return val.toFixed(2)
    } else if (typeof val === 'string' && !isNaN(Number(val))) {
      return parseFloat(val).toFixed(2)
    } else {
      return '0.00'
    }
  },
  // 格式化分数并保留两位小数，不进行四舍五入
  formatScoreNotRounding(val) {
    if (!val) return '0.00'
    if (typeof val === 'number') {
      return (Math.floor(val * 100) / 100).toFixed(2)
    } else if (typeof val === 'string' && !isNaN(Number(val))) {
      return (Math.floor(parseFloat(val) * 100) / 100).toFixed(2)
    } else {
      return '0.00'
    }
  }
}
