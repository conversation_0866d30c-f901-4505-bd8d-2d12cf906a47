<template>
  <div class="opsDeviceAdmin">
    <div class="table-wrapper">
      <div class="table-header ops-device-admin-header">
        <div class="table-title">
          <el-popover placement="bottom-start" width="500" trigger="click" ref="filterPopover" @after-leave="clickHidePopover">
            <!-- 动态筛选表单 -->
            <el-form ref="formPopover" :model="searchFormSetting" inline label-width="130px" :style="formStyle" size="mini">
              <el-form-item
                v-for="(item, key) in searchFormSetting"
                :key="key"
                :label="item.label"
                :prop="key + '.value'"
                :label-width="item.labelWidth"
              >
                <!-- <div v-if="key === 'date_type' || key === 'select_time'"> -->
                <el-select
                  v-if="item.type === 'select'"
                  v-model="item.value"
                  :placeholder="item.placeholder"
                  :multiple="item.multiple"
                  :collapse-tags="item.collapseTags"
                  :clearable="item.clearable"
                  :style="{ width: item.maxWidth ? item.maxWidth : '300px' }"
                >
                  <el-option
                    v-for="option in item.dataList"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                <el-date-picker
                  v-if="item.type === 'daterange'"
                  v-model="item.value"
                  type="daterange"
                  :clearable="item.clearable"
                  :format="item.format ? item.format : 'yyyy-MM-dd'"
                  :value-format="item.format ? item.format : 'yyyy-MM-dd'"
                  align="left"
                  unlink-panels
                  range-separator="⇀"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :picker-options="item.pickerOptions ? item.pickerOptions : pickerOptions"
                  class="ps-picker"
                  popper-class="ps-poper-picker"
                  :style="{ width: item.maxWidth ? item.maxWidth : '300px' }"
                ></el-date-picker>
                <!-- </div> -->
                <el-input
                  v-if="item.type === 'input'"
                  class="search-item-w ps-input"
                  v-model.trim="item.value"
                  :placeholder="item.placeholder"
                  :clearable="item.clearable"
                  :maxlength="item.maxlength"
                  :style="{ width: item.maxWidth ? item.maxWidth : '300px' }"
                ></el-input>
                <el-radio-group
                  v-if="item.type === 'radio'"
                  v-model="item.value"
                  :style="{ width: item.maxWidth ? item.maxWidth : '300px' }"
                >
                  <el-radio v-for="radio in item.dataList" :key="radio.value" :label="radio.value" :name="radio.label">
                    {{ radio.label }}
                  </el-radio>
                </el-radio-group>
                <el-checkbox-group
                  v-if="item.type === 'checkboxGroup'"
                  v-model="item.value"
                  :style="{ width: item.maxWidth ? item.maxWidth : '300px' }"
                >
                  <el-checkbox
                    class="ps-checkbox"
                    v-for="(checkbox, i) in item.dataList"
                    :key="i"
                    :label="item.listValueKey ? checkbox[item.listValueKey] : checkbox.value"
                    :name="item.listName"
                  >
                    {{ item.listNameKey ? checkbox[item.listNameKey] : checkbox.label }}
                  </el-checkbox>
                </el-checkbox-group>
                <el-radio-group
                  v-if="item.type === 'radioGroup'"
                  v-model="item.value"
                  :style="{ width: item.maxWidth ? item.maxWidth : '300px' }"
                >
                  <el-radio
                    class="ps-radio"
                    v-for="(radio, i) in item.dataList"
                    :key="i"
                    :label="item.listValueKey ? radio[item.listValueKey] : radio.value"
                    :name="item.listName"
                  >
                    {{ item.listNameKey ? radio[item.listNameKey] : radio.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <!-- 底部按钮 -->
            <div style="text-align: right; padding-top: 20px; border-top: 1px solid #ebeef5">
              <el-button type="primary" size="small" @click="applyFilter">确定</el-button>
              <el-button size="small" @click="applyFilter">取消</el-button>
              <el-button size="small" @click="resetFilter">重置</el-button>

              <!-- 展开和收起按钮 -->
              <el-button v-if="shouldShowExpandButton" size="small" type="text" @click="toggleCollapse">
                {{ isCollapse ? '收起' : '展开' }}
                <i v-if="isCollapse" class="el-icon-caret-top el-icon--right"></i>
                <i v-else class="el-icon-caret-bottom el-icon--right"></i>
              </el-button>
            </div>
            <el-button size="small" type="primary" style="width: 80px" slot="reference">
              筛选
              <i class="el-icon-caret-bottom el-icon--right"></i>
            </el-button>
          </el-popover>
        </div>
        <div class="align-r">
          <el-select v-model="offlineStatus" placeholder="请选择" filterable clearable :style="{ width: '150px' }">
            <!-- 选中离线 当前筛选离线  所有设备离线-->
            <el-option :key="'选中离线'" :label="'选中离线'" :value="'offline'" />
            <el-option :key="'当前筛选离线'" :label="'当前筛选离线'" :value="'currentOffline'" />
            <el-option :key="'所有设备离线'" :label="'所有设备离线'" :value="'allOffline'" />
          </el-select>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
          row-key="id"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #qrcode="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="openDialog('code', row)">查看</el-button>
            </template>
            <template #status="{ row }">
              <el-switch v-model="row.is_enable" @change="changeStatusModify(row)" />
            </template>
            <template #deviceStatus="{ row }">
              <span :style="{ color: row.device_status === 'ONLINE' ? '#56ba58' : 'red' }">在线</span>
            </template>
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right">
          <pagination
            :onPaginationChange="onPaginationChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :layout="'total, prev, pager, next,sizes, jumper'"
            :total="totalCount"
          ></pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <custom-drawer
      :show.sync="dialogVisible"
      :title="dialogTitle"
      :confirmShow="false"
      @close="dialogVisible = false"
      @cancel="dialogVisible = false"
    >
      <div class="p-20">
        <img
          style="width: 250px; height: 250px"
          src="https://1.s91i.faiusr.com/4/AFsIpOukARAEGAAg1tC88wUol6SrjQQw2AQ42AQ!800x800.png?_tm=3&v=1602813637858"
          alt=""
        />
      </div>
    </custom-drawer>
  </div>
</template>

<script>
import { deepClone } from '@/utils'
export default {
  data() {
    return {
      // 筛选表单配置 - 按照您提供的格式
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 'create_time',
          label: '',
          labelWidth: '0px',
          maxWidth: '120px',
          dataList: [
            { label: '创建时间', value: 'create_time' },
            { label: '开始日期', value: 'start_time' },
            { label: '结束日期', value: 'end_time' }
          ]
        },
        select_time: {
          type: 'daterange',
          maxWidth: '300px',
          label: '',
          clearable: false,
          value: []
        },
        project_id: {
          type: 'select',
          label: '所属项目',
          value: '',
          placeholder: '请选择项目',
          dataList: [
            { label: '项目1', value: '1' },
            { label: '项目2', value: '2' },
            { label: '项目3', value: '3' }
          ]
        },
        organization_id: {
          type: 'select',
          label: '所属组织',
          value: '',
          placeholder: '请选择组织',
          clearable: true,
          dataList: [
            { label: '组织1', value: '1' },
            { label: '组织2', value: '2' },
            { label: '组织3', value: '3' }
          ]
        },
        device_type: {
          type: 'select',
          label: '设备类型',
          value: '',
          placeholder: '请选择设备类型',
          clearable: true,
          dataList: [
            { label: '类型1', value: '1' },
            { label: '类型2', value: '2' },
            { label: '类型3', value: '3' }
          ]
        },
        device_name: {
          type: 'input',
          label: '设备名称',
          value: '',
          placeholder: '请输入设备名称',
          clearable: true
        },
        use_status: {
          type: 'radio',
          label: '使用状态',
          value: '',
          dataList: [
            { label: '全部', value: 'all' },
            { label: '启用', value: 'enabled' },
            { label: '禁用', value: 'disabled' }
          ]
        },
        activation_code: {
          type: 'input',
          label: '激活码',
          value: '',
          placeholder: '请输入激活码',
          clearable: true
        },
        activation_status: {
          type: 'radio',
          label: '激活码状态',
          value: '',
          dataList: [
            { label: '全部', value: '' },
            { label: '已激活', value: 'activated' },
            { label: '未激活', value: 'unactivated' }
          ]
        },
        code_valid_status: {
          type: 'radio',
          label: '激活码状态',
          value: '',
          dataList: [
            { label: '全部', value: '' },
            { label: '生效', value: 'valid' },
            { label: '失效', value: 'invalid' }
          ]
        },
        mac_address: {
          type: 'input',
          label: 'Mac地址',
          value: '',
          placeholder: '请输入Mac地址',
          clearable: true
        },
        sn_code: {
          type: 'input',
          label: 'SN码',
          value: '',
          placeholder: '请输入SN码',
          clearable: true
        },
        device_version: {
          type: 'input',
          label: '设备版本号',
          value: '',
          placeholder: '请输入设备版本号',
          clearable: true
        },
        offline_activation_code: {
          type: 'input',
          label: '离线人脸激活码',
          value: '',
          placeholder: '请输入离线人脸激活码',
          clearable: true
        },
        near_expiration: {
          type: 'checkboxGroup',
          label: '临近失效',
          value: [],
          placeholder: '请选择',
          dataList: [{ label: '是', value: 'yes' }]
        },
        // sort_type: {
        //   type: 'radioGroup',
        //   label: '排序方式',
        //   value: 'asc',
        //   dataList: [
        //     { label: '升序', value: 'asc' },
        //     { label: '降序', value: 'desc' }
        //   ]
        // }

        sort_type: {
          type: 'radioGroup',
          label: '排序方式',
          value: '',
          dataList: [
            { label: '升序', value: 'asc' },
            { label: '降序', value: 'desc' }
          ]
        }
      },
      pickerOptions: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now();
        // },
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      offlineStatus: '',
      isLoading: false,
      dialogVisible: false,
      dialogType: '',
      dialogTitle: '',
      dialogData: null,

      // 展开收起相关
      isCollapse: false, // 是否展开状态
      formHeight: 0, // 表单实际高度

      tableSetting: [
        { label: '', key: 'selection', type: 'selection', reserveSelection: true },
        { label: '所属项目', key: '1' },
        { label: '所属组织', key: '2' },
        { label: '适用分组', key: '3' },
        { label: '设备名称', key: '4' },
        { label: '设备类型', key: '5' },
        { label: '设备型号', key: '6' },
        { label: '设备ID', key: '7' },
        { label: '版本号', key: '8' },
        { label: 'SN码', key: '9' },
        { label: 'MAC地址', key: '10' },
        { label: '激活码', key: '11' },
        { label: '激活二维码', key: 'qrcode', type: 'slot', slotName: 'qrcode' },
        { label: '激活码状态', key: '13' },
        { label: '有效期', key: '14' },
        { label: '使用状态', key: 'status', type: 'slot', slotName: 'status' },
        { label: '设备状态', key: '16', type: 'slot', slotName: 'deviceStatus' },
        { label: '离线人脸激活码', key: '16', width: '150px' },
        { label: '创建时间', key: '17' },
        { label: '操作人', key: '18' }
      ],
      tableData: [
        {
          1: '2',
          is_enable: false
        }
      ],
      orderIds: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0
    }
  },
  computed: {
    // 表单样式 - 根据是否展开和实际高度动态设置
    formStyle() {
      // 如果还没有计算高度，先不设置样式，避免闪烁
      if (this.formHeight === 0) {
        return {}
      }

      if (this.formHeight <= 600) {
        // 如果内容高度不超过570px，不设置固定高度
        return {}
      } else {
        // 如果内容高度超过570px，根据展开状态设置高度
        return {
          height: this.isCollapse ? 'auto' : '460px',
          overflow: 'hidden', // 不使用滚动条，直接隐藏超出部分
          transition: 'height 0.3s ease' // 添加平滑过渡效果
        }
      }
    },

    // 是否显示展开按钮 - 只有当内容高度超过570px时才显示
    shouldShowExpandButton() {
      return this.formHeight > 570
    }
  },
  watch: {
    // 监听表单配置变化，重新计算高度
    searchFormSetting: {
      handler() {
        this.$nextTick(() => {
          this.calculateFormHeight()
        })
      },
      deep: true
    }
  },
  mounted() {
    // 预先计算表单高度，避免首次显示时闪烁
    this.preCalculateFormHeight()
  },
  methods: {
    clickHidePopover() {
      this.isCollapse = false
    },
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      // this.getDraftBoxList()
    },
    // 修改状态
    changeStatusModify(data) {
      this.$confirm('确定启用该状态？', '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            // let params = {}
            // this.getCouponManageModify(params)
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
              data.is_enable = !data.is_enable
            }
          }
        }
      })
        .then(() => {})
        .catch(() => {})
    },
    openDialog(type, data) {
      this.dialogData = data
      this.dialogType = type
      switch (type) {
        case 'code':
          this.dialogTitle = '查看二维码'
          break
      }
      this.dialogVisible = true
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      console.log(val)
      var selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        selectListId.push(item.id)
      })
      this.orderIds = deepClone(selectListId)
    },
    // 搜索处理
    searchHandler(searchType) {
      console.log('搜索参数:', searchType)
      console.log('当前筛选条件:', this.searchFormSetting)
      // 这里可以调用API获取筛选后的数据
      // this.getTableData()
    },
    // 重置处理
    resetHandler() {
      console.log('重置筛选条件')
      // 重置后可以重新获取数据
      // this.getTableData()
    },
    // 应用筛选
    applyFilter() {
      console.log('应用筛选')
      // 获取当前表单的值
      const filterData = {}
      Object.keys(this.searchFormSetting).forEach(key => {
        filterData[key] = this.searchFormSetting[key].value
      })
      console.log('筛选数据:', filterData)

      // 这里可以调用API获取筛选后的数据
      // this.getTableData(filterData)

      // 关闭弹窗
      this.$refs.filterPopover.doClose()
    },
    // 重置筛选
    resetFilter() {
      // 调用search-form组件的重置方法
      if (this.$refs.searchRef) {
        this.$refs.searchRef.resetForm()
      }
      console.log('重置筛选')
    },
    // 导出功能
    gotoExport() {
      console.log('导出数据')
    },

    // 预先计算表单高度（在mounted时调用，避免闪烁）
    preCalculateFormHeight() {
      // 根据表单项数量预估高度
      const formItemCount = Object.keys(this.searchFormSetting).length
      // 每个表单项大约45px高度，加上边距
      const estimatedHeight = formItemCount * 45 + 60
      this.formHeight = estimatedHeight
      console.log('预估高度:', estimatedHeight, '表单项数量:', formItemCount)
    },

    // 计算表单实际高度（在popover显示时调用，获取精确高度）
    calculateFormHeight() {
      // 如果还没有预估高度，先使用预估值避免闪烁
      if (this.formHeight === 0) {
        this.preCalculateFormHeight()
      }

      // 使用较短的延迟获取精确高度
      this.$nextTick(() => {
        setTimeout(() => {
          const formEl = this.$refs.formPopover?.$el
          if (formEl) {
            // 临时移除高度限制以获取真实高度
            const originalHeight = formEl.style.height
            const originalOverflow = formEl.style.overflow

            formEl.style.height = 'auto'
            formEl.style.overflow = 'visible'

            // 获取实际高度
            let actualHeight = formEl.scrollHeight || formEl.offsetHeight || formEl.clientHeight

            // 如果获取到有效高度，更新formHeight
            if (actualHeight > 0) {
              console.log('精确高度:', actualHeight, '之前预估:', this.formHeight)
              this.formHeight = actualHeight
            }

            // 恢复原始样式
            formEl.style.height = originalHeight
            formEl.style.overflow = originalOverflow
          }
        }, 50) // 减少延迟时间到50ms
      })
    },

    // 切换展开收起状态
    toggleCollapse() {
      this.isCollapse = !this.isCollapse
    }
  }
}
</script>

<style lang="scss" scoped>
.opsDeviceAdmin {
  padding: 20px;
  .table-wrapper {
    padding: 20px;
    background-color: #fff;
    border-radius: 20px;
  }
  .ops-device-admin-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }
}

// 筛选弹窗样式
:deep(.el-popover) {
  .search-form-wrapper {
    .search-header {
      margin-bottom: 10px;
    }

    .collapse-wrapper {
      max-height: 400px;
      overflow-y: auto;
    }

    .search-form-collapse {
      .el-form-item {
        margin-bottom: 0px;
        margin-right: 20px;

        .el-form-item__label {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
