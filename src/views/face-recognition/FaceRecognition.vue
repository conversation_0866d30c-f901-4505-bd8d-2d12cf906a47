<template>
  <div class="face-recognition-container">
    <!-- 顶部标签页 -->
    <div class="tab-header">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="扫脸" name="scan"></el-tab-pane>
        <el-tab-pane label="查找用户" name="search"></el-tab-pane>
        <el-tab-pane label="人脸检索" name="retrieve"></el-tab-pane>
      </el-tabs>
    </div>

    <!-- 扫脸页面 -->
    <div v-if="activeTab === 'scan'" class="scan-content">
      <div class="face-upload-section">
        <div class="upload-area">
          <div class="face-upload-title">拍摄人脸</div>
          <div class="face-upload-box">
            <img v-if="currentFaceImage" :src="currentFaceImage" class="current-face-img" />
            <div v-else class="upload-placeholder">
              <i class="el-icon-camera"></i>
              <div>在线拍照</div>
            </div>
          </div>
        </div>

        <div class="current-selection">
          <div class="selection-title">当前选择</div>
          <div class="selection-content">
            <img v-if="selectedFaceImage" :src="selectedFaceImage" class="selected-face-img" />
            <div v-else class="no-selection">当前订单无拍照人脸</div>
          </div>
        </div>
      </div>

      <!-- 校验结果表格 -->
      <div class="verification-results">
        <div class="table-title">校验结果</div>
        <el-table
          :data="verificationData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="rank" label="排序" width="80" align="center"></el-table-column>
          <el-table-column prop="avatar" label="近似人脸" width="120" align="center">
            <template slot-scope="scope">
              <img :src="scope.row.avatar" class="face-img" />
            </template>
          </el-table-column>
          <el-table-column prop="name" label="姓名" align="center"></el-table-column>
          <el-table-column prop="userId" label="人员编号" align="center"></el-table-column>
          <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
          <el-table-column prop="score" label="识别分数" align="center"></el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" class="ps-text" @click="selectUser(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 查找用户页面 -->
    <div v-if="activeTab === 'search'" class="search-content">
      <div class="search-section">
        <div class="search-title">查询条件</div>
        <el-form :model="searchForm" label-width="100px" class="search-form">
          <el-form-item label="动账组织">
            <el-select v-model="searchForm.organization" placeholder="紫竹、必达" style="width: 200px">
              <el-option label="紫竹" value="zizhu"></el-option>
              <el-option label="必达" value="bida"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户姓名">
            <el-input v-model="searchForm.userName" placeholder="请输入" style="width: 200px"></el-input>
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="searchForm.phone" placeholder="请输入" style="width: 200px"></el-input>
          </el-form-item>
          <el-form-item label="人员编号">
            <el-input v-model="searchForm.userId" placeholder="请输入" style="width: 200px"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 搜索结果 -->
      <div class="search-results">
        <div class="result-item" v-for="user in searchResults" :key="user.id" :class="{ 'selected': selectedUser && selectedUser.id === user.id }" @click="selectSearchUser(user)">
          <img :src="user.avatar" class="user-avatar" />
          <div class="user-info">
            <div class="user-name">{{ user.name }} ({{ user.userId }})</div>
            <div class="user-phone">{{ user.phone }}</div>
            <div class="user-group">{{ user.group }}</div>
          </div>
          <div class="user-balance">
            <div class="balance-item">
              <span class="label">储值余额</span>
              <span class="value">¥ {{ user.balance }}</span>
            </div>
            <div class="balance-item">
              <span class="label">补贴余额</span>
              <span class="value">¥ {{ user.subsidy }}</span>
            </div>
            <div class="balance-item">
              <span class="label">赠送余额</span>
              <span class="value">¥ {{ user.gift }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 人脸检索页面 -->
    <div v-if="activeTab === 'retrieve'" class="retrieve-content">
      <div class="retrieve-message">当前订单无拍照人脸</div>
    </div>

    <!-- 扣款方式 -->
    <div class="payment-method">
      <div class="method-title">扣款方式</div>
      <el-radio-group v-model="paymentMethod">
        <el-radio label="balance">重新扣款</el-radio>
        <el-radio label="original">原价扣款</el-radio>
      </el-radio-group>
    </div>

    <!-- 底部按钮 -->
    <div class="footer-buttons">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确认并扣款</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FaceRecognition',
  data() {
    return {
      activeTab: 'scan',
      currentFaceImage: '',
      selectedFaceImage: '',
      selectedUser: null,
      paymentMethod: 'balance',
      searchForm: {
        organization: '',
        userName: '',
        phone: '',
        userId: ''
      },
      verificationData: [
        {
          rank: 1,
          avatar: 'https://via.placeholder.com/60x60',
          name: '张三',
          userId: 'A01',
          phone: '15986684278',
          score: '92.43'
        },
        {
          rank: 2,
          avatar: 'https://via.placeholder.com/60x60',
          name: '李四',
          userId: 'A02',
          phone: '15986684276',
          score: '85.01'
        },
        {
          rank: 3,
          avatar: 'https://via.placeholder.com/60x60',
          name: '王五',
          userId: 'A03',
          phone: '15986684276',
          score: '80.27'
        },
        {
          rank: 4,
          avatar: 'https://via.placeholder.com/60x60',
          name: '赵六',
          userId: 'A04',
          phone: '15986684276',
          score: '78.90'
        },
        {
          rank: 5,
          avatar: 'https://via.placeholder.com/60x60',
          name: '孙琦',
          userId: 'A05',
          phone: '15986684276',
          score: '75.90'
        }
      ],
      searchResults: [
        {
          id: 1,
          avatar: 'https://via.placeholder.com/60x60',
          name: '张三',
          userId: '100001',
          phone: '18278236636',
          group: 'A分组',
          balance: '100.00',
          subsidy: '10.00',
          gift: '0.00'
        }
      ]
    }
  },
  methods: {
    handleTabClick(tab) {
      console.log('Tab clicked:', tab.name)
    },
    selectUser(user) {
      this.selectedUser = user
      this.$message.success(`已选择用户：${user.name}`)
    },
    selectSearchUser(user) {
      this.selectedUser = user
    },
    handleSearch() {
      // 模拟搜索
      console.log('Search params:', this.searchForm)
    },
    handleCancel() {
      this.$emit('close')
    },
    handleConfirm() {
      if (!this.selectedUser) {
        this.$message.warning('请先选择用户')
        return
      }
      this.$message.success('确认成功')
      this.$emit('confirm', this.selectedUser)
    }
  }
}
</script>
