<template>
  <div class="ps-el-drawer">
    <el-drawer
      title="详情"
      :visible="drawerShow"
      :show-close="false"
      @open="handleOpen"
      size="50%"
    >
      <div class="p-20" v-if="infoData">
        <el-form ref="drawerFormRef" :model="drawerForm" label-width="140px" label-position="right">
          <el-form-item label="公司名称" >
            {{ infoData.name || '--' }}
          </el-form-item>
          <el-form-item :label="'统一社会信用代码'" prop="name">
            {{ infoData.uscc || '--' }}
          </el-form-item>
          <el-form-item :label="'创建组织'" prop="name">
            {{ infoData.org_name || '--' }}
          </el-form-item>
          <el-form-item :label="'创建时间'" prop="address">
            {{ infoData.create_time || '--' }}
          </el-form-item>
        </el-form>
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          row-key="phone"
          stripe
          header-row-class-name="ps-table-header-row"
          class="ps-table-tree"
        >
          <table-column v-for="(item, i) in tableSetting" :key="i" :col="item">
            <template #address="{ row }">
              <el-tooltip v-if="row.address" class="item" effect="dark" :content="row.address" placement="top">
                <div>{{ truncateText(row.address) }}</div>
              </el-tooltip>
              <span v-else>--</span>
            </template>
          </table-column>
        </el-table>
        <div class="ps-el-drawer-footer">
          <el-button type="primary" class="w-100" @click="cancelHandle">关 闭</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  computed: {
    drawerShow: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '所属项目', key: 'organization_name' },
        { label: '应用组织', key: 'company_name' },
        { label: '负责人', key: 'head_person' },
        { label: '联系电话', key: 'phone' },
        { label: '公司地址', key: 'address', type: 'slot', slotName: 'address' }
      ],
      drawerForm: {} // 详情信息
    }
  },
  methods: {
    handleOpen() {
      let param = { uscc: this.infoData.uscc }
      this.$apis.apiBackgroundAdminCateringCompanyDetails(param).then(res => {
        if (res.code === 0) {
          this.tableData = res.data || []
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 取消弹窗
    cancelHandle() {
      this.drawerShow = false
    },
    truncateText(text, maxLength = 7) {
      if (!text) return '--'
      return text.length > maxLength
        ? text.substring(0, maxLength) + '...'
        : text
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
