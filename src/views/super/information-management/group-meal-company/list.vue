<template>
  <div class="ChannelManagement container-wrapper">
    <!--团餐管理 头部-->
    <refresh-tool @refreshPage="refreshHandler" />
    <!--团餐管理 搜索层-->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      :autoSearch="false"
      @reset="resetHandle"
      labelWidth="150px"
    ></search-form>
    <!--团餐管理 表格数据层-->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          row-key="id"
          stripe
          header-row-class-name="ps-table-header-row"
          class="ps-table-tree"
          :tree-props="{ children: 'children_list', hasChildren: 'hasChildren' }"
        >
          <table-column v-for="(item, i) in tableSetting" :key="i" :col="item">
            <template #level="{ row }">
              {{ row.level | formatLevel }}
            </template>
            <template #operate="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="showDrawer(row)">详情</el-button>
              <el-button v-permission="['background.admin.catering_company.modify']" type="text" size="small" class="ps-text" @click="modifyName(row)">编辑</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination-theme" style="text-align: right; padding: 0 20px 20px 0">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[5, 10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select-theme"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 团餐公司详情弹窗 -->
    <detail-dialog :show.sync="drawerShowDetail" :infoData="infoData" />
  </div>
</template>

<script>
import { deepClone, debounce } from '@/utils/index'
import detailDialog from './components/detailDialog'
export default {
  components: {
    detailDialog
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '公司名称', key: 'name' },
        { label: '统一社会信用代码', key: 'uscc' },
        { label: '创建时间', key: 'create_time' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operate', width: '200', fixed: 'right' }
      ],
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '创建时间',
          value: []
        },
        name: {
          type: 'input',
          value: '',
          label: '公司名称',
          placeholder: '请输入',
          clearable: true
        },
        uscc: {
          type: 'input',
          value: '',
          label: '统一社会信用代码',
          placeholder: '请输入',
          clearable: true
        }
      },
      page: 1,
      pageSize: 10,
      totalCount: 0,
      addAndEditDrawerShow: false,
      drawerShowDetail: false,
      infoData: {},
      newCompanyName: '' // 存储输入的新公司名称
    }
  },
  created() {
    // this.getDataList()
  },
  methods: {
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.getDataList()
    },
    /**
     * 刷新页面
     */
    refreshHandler() {
      this.page = 1
      this.getDataList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    resetHandle() {
      this.currentPage = 1
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 获取数据
    getDataList() {
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.page,
        page_size: this.pageSize
      }
      this.$apis.apiBackgroundAdminGroupMealCompanyList(params).then(res => {
        this.isLoading = false
        if (res.code === 0) {
          this.tableData = deepClone(res.data.results)
          this.totalCount = res.data.count
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 修改团餐公司名称
    modifyName(row) {
      this.$prompt('<span style="color: red">* </span>公司名称', '修改公司名称', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        dangerouslyUseHTMLString: true, // 关键：允许HTML渲染
        inputValue: row.name,
        inputValidator: (value) => {
          if (!value || value.trim() === '') {
            return '公司名称不能为空'
          }
          return true
        },
        inputErrorMessage: '公司名称不能为空'
      }).then(({ value }) => {
        const param = {
          id: row.id,
          name: value
        }
        this.$apis.apiBackgroundAdminGroupMealCompanyModify(param).then(res => {
          if (res.code === 0) {
            this.$message.success('编辑成功')
          } else {
            this.$message.error(res.msg)
          }
          this.getDataList()
        })
      }).catch(() => {})
    },
    // 打开添加团餐公司
    showDrawer(row) {
      this.infoData = row
      this.drawerShowDetail = true
    }
  }
}
</script>

<style lang="scss" scoped>
.ps-confirm-input {
  .el-message-box__message {
    padding: 10px 0;
  }
  .mt-10 {
    margin-top: 10px;
  }
}
</style>
