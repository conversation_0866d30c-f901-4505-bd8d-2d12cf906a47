<template>
  <div class="AddSupplierManagement form-container">
    <h3>基本信息</h3>
    <el-form v-loading="isLoading" ref="formRef" :model="formData" :rules="formRuls" label-width="142px" label-position="right" size="small" class="m-t-10">
      <div class="m-l-20">
        <el-form-item label="供应商名称" prop="name">
          <el-input v-model="formData.name" :maxlength="20" class="w-200" :disabled="type === 'modify'"></el-input>
        </el-form-item>
        <el-form-item label="负责人姓名">
          <el-input v-model="formData.contactName" class="w-200" :disabled="type === 'modify'"></el-input>
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="formData.contactPhone" class="w-200" :disabled="type === 'modify'"></el-input>
        </el-form-item>
        <el-form-item label="所在地址">
          <el-input v-model="formData.address" :maxlength="50" class="w-200" :disabled="type === 'modify'" ></el-input>
        </el-form-item>
        <el-form-item label="统一社会信用代码" prop="creditCode">
          <el-input v-model="formData.creditCode" :disabled="type === 'modify'" class="w-200"></el-input>
        </el-form-item>
        <el-form-item label="类别" prop="supplyCategory">
          <el-input v-model="formData.supplyCategory" class="w-200" :disabled="type === 'modify'"></el-input>
        </el-form-item>
        <div class="m-b-30">
          <div class="aptitude-management m-b-10 font-size-14">
            <span class="font-weight-600">资质管理</span>
            <span v-if="type === 'modify'"><el-button size="small" class="ps-origin-btn" @click="clickRowHandle('add')" >新增</el-button></span>
          </div>
          <el-table
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
            size="small"
          >
            <table-column v-for="item in tableSettings" :key="item.key" :col="item">
              <template #imgUrl="{ row }">
                <el-image
                  v-for="(img, index) in row.imgUrl"
                  :key="index"
                  :preview-src-list="row.imgUrl"
                  :initial-index="index"
                  class="file-img m-r-6"
                  :src="img"
                  fit="contain"
                  style="width: 120px;height: 150px"
                ></el-image>
              </template>
              <template #aptitude="{ row }">
                {{ computedFileType(row.aptitude) }}
              </template>
              <template #expiry_date="{ row }">
                <span>{{ row.expiry_date[0]}} - {{ row.expiry_date[1] }}</span>
              </template>
              <template #operation="{ row }">
                <el-button type="text" size="small" class="ps-text" @click="clickRowHandle('detail', row)" >详情</el-button>
                <el-button type="text" size="small" class="ps-text" @click="clickRowHandle('modify', row)" >编辑</el-button>
                <el-button v-if="type === 'add'" type="text" size="small" class="ps-text"  @click="deleteHandle(row)">删除</el-button>
              </template>
            </table-column>
          </el-table>
        </div>
      </div>
      <div class="m-l-40 m-t-60">
        <el-button class="ps-origin-btn w-130" size="medium" @click="cancelHandle">取消</el-button>
        <el-button class="ps-origin-btn w-130" size="medium" @click="submitFormHandle()">保存</el-button>
      </div>
    </el-form>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <addAptitudeDialog :showdialog.sync="showAptitudeDialog" @clickConfirm="setTableData" :infoData="addAptitudeDialogInfoData" :type="addAptitudeDialogType" @showImgViewer="handleClick"></addAptitudeDialog>

     <!-- 图片预览 -->
     <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSuffix, getToken, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import { validateTelphone } from '@/assets/js/validata'
import addAptitudeDialog from '@/components/AddAptitudeDialog/index.vue'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

export default {
  name: 'AddSupplierManagement',
  mixins: [exportExcel],
  components: { addAptitudeDialog, ElImageViewer },
  data() {
    return {
      type: 'add',
      isLoading: false, // 刷新数据
      // form表单数据
      formData: {
        id: '',
        name: '', // 供应商名称
        creditCode: '', // 工商营业执照（统一社会信用代码）
        contactName: '', // 联系人
        contactPhone: '', // 手机
        address: '', // 地址
        supplyCategory: ''
      },
      formRuls: {
        name: [{ required: true, message: '请输入供应商名称', trigger: 'change' }],
        creditCode: [{ required: true, message: '工商营业执照', trigger: 'change' }],
        contactName: [{ required: true, message: '请输入负责人姓名', trigger: 'change' }],
        contactPhone: [
          { required: true, message: '请输入联系电话', trigger: 'change' },
          { validator: validateTelphone, trigger: 'change' }
        ],
        supplyCategory: [{ required: true, message: '请选择供应商类型', trigger: 'change' }],
        address: [{ required: true, message: '请输入所在地址', trigger: 'change' }],
      },
      uploadParams: {
        prefix: 'inventoryImage'
      },
      actionUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      infoData: {},
      // 图片放大查看
      dialogImageUrl: [],
      dialogVisible: false,
      showAptitudeDialog: false,
      tableData: [],
      tableSettings: [
        { label: '图片', key: 'imgUrl', type: "slot", slotName: "imgUrl" },
        { label: '资质类型', key: 'aptitude', type: 'slot', slotName: 'aptitude' },
        { label: '上传时间', key: 'upLoadTime' },
        { label: '修改时间', key: 'editTime' },
        { label: '有效期', key: 'expiry_date', type: 'slot', slotName: 'expiry_date' },
        { label: '操作人', key: 'operator' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      addAptitudeDialogInfoData: {},
      addAptitudeDialogType: 'add',
      showImagePreview: false,
      previewList: []
    }
  },
  computed: {
    computedFileType() {
      return d => {
        switch (d) {
          case '1':
            return '营业执照'
          case '2':
            return '食品经营许可证'
          case '3':
            return '食品生产许可证'
        }
      }
    }
  },
  created() {
    this.type = this.$route.query.type
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.type === 'modify') {
        this.getProcureDetail(this.$route.query.id)
      }
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 获取列表数据
    async getProcureDetail(id) {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id
      }
      const [err, res] = await to(this.$apis.apiBackgroundAdminSupplierManageDetailsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        // this.formData = res.data
        let infoData = res.data
        this.formData = {
          id: infoData.id,
          name: infoData.name, // 供应商名称
          creditCode: infoData.credit_code, // 工商营业执照（统一社会信用代码）
          contactName: infoData.contact_name, // 联系人
          contactPhone: infoData.contact_phone, // 手机
          supplyCategory: infoData.supply_category,
          address: infoData.address // 地址
        }
        if (infoData.certification_info.length) {
          this.tableData = deepClone(infoData.certification_info)
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    cancelHandle() {
      this.$backVisitedViewsPath(this.$route.path, 'adminSupplierManagement')
    },
    // 保存
    async submitFormHandle(type) {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (this.isLoading) return
          let api
          let params = {
            name: this.formData.name,
            credit_code: this.formData.creditCode,
            contact_name: this.formData.contactName,
            contact_phone: this.formData.contactPhone,
            address: this.formData.address,
            supply_category: this.formData.supplyCategory,
            certification_info: this.tableData
          }
          this.isLoading = true
          params.id = this.formData.id
          api = this.$apis.apiBackgroundAdminSupplierManageInfoModifyPost(params)
          this.sendFormData(api)
        }
      })
    },
    // 发送请求
    async sendFormData(xhr) {
      const [err, res] = await this.$to(xhr)
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('成功')
        this.cancelHandle()
      } else {
        this.$message.error(res.msg)
      }
    },
    // ——————资质相关——————
    clickRowHandle(type, row) {
      this.addAptitudeDialogType = type
      if (type === 'modify' || type === 'add') {
        this.addAptitudeDialogInfoData = type === 'add' ? {} : deepClone(row)
        this.showAptitudeDialog = true
      } else {
        this.addAptitudeDialogInfoData = deepClone(row)
        this.showAptitudeDialog = true
      }
    },
    deleteHandle(row) {
      let index = this.tableData.findIndex(item => item.id === row.id)
      this.tableData.splice(index, 1)
      this.$message.success('删除成功')
    },
    // 将弹窗返回的东西塞进tableData
    setTableData(data, type) {
      console.log('data', data, type)
      // if (type === 'add') {
      //   this.tableData.push(data)
      // } else {

      // }
      if (this.tableData.length > 0) {
        let index = this.tableData.findIndex(v => v.aptitude === data.aptitude)
        if (index > -1) {
          this.tableData.splice(index, 1, data)
        } else {
          this.tableData.push(data)
        }
      } else {
        this.tableData.push(data)
      }
    },
    handleClick(img) {
      this.previewList = [img]
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    // ————图片预览相关————
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    }
  }
}
</script>

<style lang="scss" scoped>
.AddSupplierManagement {
  h3{
    margin: 0;
  }
  .w-200 {
    width: 220px;
  }
  .w-auto{
    width: 300px;
  }
  .error {
    ::v-deep .el-input__inner {
      border-color: red;
    }
  }
  .upload-food{
    overflow: hidden;
    max-height: 830px;
    &.hide-upload{
      .el-upload--picture-card{
        display: none;
      }
    }
    .el-upload--picture-card{
      border: none;
    }
    .el-upload-dragger{
      width: 148px;
      height: 148px;
    }
    .upload-food-img{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 145px;
      height: 145px;
      img{
        max-width: 145px;
        max-height: 145px;
      }
    }
  }
  .aptitude-management {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .font-weight-600 {
      font-weight: 600;
    }
  }
}
</style>
