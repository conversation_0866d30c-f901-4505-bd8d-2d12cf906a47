<template>
  <div class="container-wrapper">
    <!-- 写点东西更新一下 -->
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" @search="searchHandle" :form-setting="searchFormSetting" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="openDrawer('Multiple')">编辑商户</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          ref="tableView"
          :data="tableData"
          v-loading="isLoading"
          stripe
          header-row-class-name="ps-table-header-row"
          :row-key="'report_id'"
          reserve-selection
          @selection-change="handleSelectionChange">
          <table-column  v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #org_name="{ row }">
              <div v-if="row.org_name.length" class="flex flex-center">
                <div v-for="(item, index) in row.org_name" :key="index">{{ item }}{{ index < row.org_name.length - 1 ? ',' : '' }}</div>
              </div>
              <div v-else>{{ "--" }}</div>
            </template>
            <template #operation="{ row }">
              <el-button type="text" class="ps-text m-r-10" size="small" @click="openDrawer('Single', row)">编辑</el-button>
              <el-button type="text" class="ps-black" size="small" @click="historyRecord(row)">历史记录</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>

    <div class="ps-el-drawer">
      <el-drawer
        title="编辑"
        :visible="drawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-transfer
            v-if="drawerShow"
            filterable
            v-model="selectList"
            :data="orgList"
            :titles="['待选', '已选']"
            :button-texts="['往左', '往右']">
          </el-transfer>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100 m-r-10" @click="closeHandle">关闭</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        title="历史记录"
        :visible="historyDrawerShow"
        :show-close="false"
        size="50%">
        <div class="p-20">
          <el-table
            ref="tableView"
            :data="historyTableData"
            v-loading="historyIsLoading"
            stripe
            header-row-class-name="ps-table-header-row"
            :row-key="'report_id'"
            reserve-selection>
            <table-column  v-for="item in historyTableSetting" :key="item.key" :col="item">
            </table-column>
          </el-table>
          <div class="ps-el-drawer-footer">
            <el-button size="small" type="primary" class="w-100 m-r-10" @click="historyDrawerShow = false">关闭</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { debounce, deepClone, to } from '@/utils'
// 更新点东西上去看看

export default {
  name: 'SuperReportApplication',
  data() {
    return {
      isLoading: false,
      searchFormSetting: {
        category_ids: {
          type: 'select',
          label: '文件夹',
          value: '',
          placeholder: '请选择',
          multiple: true,
          collapseTags: true,
          clearable: true,
          dataList: []
        },
        name: {
          type: 'input',
          label: '报表名称',
          value: '',
          placeholder: '请输入报表名称'
        },
        org_ids: {
          type: 'select',
          value: [],
          label: '适用项目',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: [],
          filterable: true,
          multiple: true,
          checkStrictly: true,
          collapseTags: true,
          clearable: true
        }
      },
      tableData: [],
      currentTableSetting: [
        { label: '', key: 'selection', type: 'selection', reserveSelection: true },
        { label: '报表名称', key: 'name' },
        { label: '文件夹', key: 'category_name' },
        { label: '适用项目', key: 'org_name', type: "slot", slotName: "org_name", showTooltip: true },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      // 搜索量
      page: 1,
      pageSize: 10,
      totalCount: 0,
      drawerShow: false,
      selectedRow: {},
      selectedRowList: [],
      selectList: [],
      orgList: [],
      historyDrawerShow: false,
      historyTableData: [],
      historyIsLoading: false,
      historyTableSetting: [
        { label: '操作时间', key: 'create_time' },
        { label: '操作人', key: 'operator_name' },
        { label: '操作', key: 'module' },
        { label: '详情', key: 'detail' }
      ],
      editType: ''
    }
  },
  created() {
    this.getFileNameList()
    this.getOrganizationList()
    this.initLoad()
  },
  methods: {
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          }
        }
      }
      if (this.chooseCategory && this.chooseCategory.length > 0) {
        params.food_sort_list = this.chooseCategory
      }
      console.log('params', params)
      return params
    },
    initLoad() {
      this.isLoading = true
      this.getDataList()
      if (this.$refs.tableView) {
        this.$refs.tableView.clearSelection()
      }
    },
    searchHandle: debounce(function() {
      this.isLoading = true
      this.page = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.isLoading = true
      this.page = 1
      this.initLoad()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.isLoading = true
      this.pageSize = val
      this.page = 1
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.isLoading = true
      this.page = val
      this.pageSize = 10
      this.initLoad()
    },
    async getDataList() {
      const [err, res] = await to(this.$apis.apiBackgroundAdminJmreportListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.page,
        page_size: this.pageSize
      }))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.message)
      }
      this.isLoading = false
    },
    async getFileNameList() {
      const [err, res] = await to(this.$apis.apiBackgroundAdminJmreportCategoryListPost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.category_ids.dataList = res.data.map(item => {
          let obj = {
            value: item.key,
            label: item.name
          }
          return obj
        })
      } else {
        this.$message.error(res.message)
      }
    },
    getOrganizationList() {
      this.$apis.apiBackgroundAdminOrganizationListPost({
        page: 1,
        page_size: 9999,
        parent__is_null: '1',
        status__in: ['enable']
      }).then(res => {
        if (res.code === 0) {
          this.orgList = res.data.results.map(item => {
            let obj = {
              key: item.id,
              label: item.name
            }
            return obj
          })
          this.searchFormSetting.org_ids.dataList = res.data.results.map(item => {
            let obj = {
              id: item.id,
              name: item.name
            }
            return obj
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取历史记录
    getHistoryRecord(row) {
      this.$apis.apiBackgroundAdminJmreportOperationListPost({
        report_id: row.report_id
      }).then(res => {
        if (res.code === 0) {
          this.historyTableData = deepClone(res.data)
          this.historyDrawerShow = true
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleSelectionChange(val) {
      this.selectedRowList = val.map(item => item.report_id)
      console.log('看看多选', this.selectedRowList)
    },
    openDrawer(type, row) {
      this.editType = type
      console.log('看看row', row)
      if (type === 'Single') {
        this.selectedRow = deepClone(row)
        this.selectList = [...row.org_ids]
      } else {
        this.selectList = []
      }
      this.drawerShow = true
    },
    historyRecord(row) {
      this.selectedRow = deepClone(row)
      this.getHistoryRecord(row)
    },
    saveHandle() {
      console.log(this.selectedRowList)
      if (this.selectList.length === 0) {
        this.$message.error('请选择商户')
        return
      }
      this.$apis.apiBackgroundAdminJmreportEditOrgPost({
        report_ids: this.editType === 'Single' ? [this.selectedRow.report_id] : [...this.selectedRowList],
        org_ids: [...this.selectList]
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('保存成功')
          this.drawerShow = false
          this.initLoad()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    closeHandle() {
      this.drawerShow = false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-transfer {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
}
::v-deep .el-transfer-panel {
  width: 400px;
}
::v-deep .el-transfer__buttons {
  // width: 220px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
::v-deep .el-button+.el-button {
  margin-left: 0px;
}
</style>
