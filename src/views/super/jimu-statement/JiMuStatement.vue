<template>
  <div class="jimu-container">
    <div v-if="loading">加载中...</div>
    <iframe v-else :src="reportUrl" frameborder="0" style="width: 100%; height: 100vh;"></iframe>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'SuperJimuStatement',
  data() {
    return {
      loading: true,
      reportUrl: ''
    }
  },
  computed: {
    ...mapGetters(['token'])
  },
  created() {
    console.log('token', this.token)
    this.initLoad()
  },
  methods: {
    initLoad() {
      if (!this.token) {
        this.$message.error('请先登录')
        this.$router.push('/login')
        return
      }

      try {
        console.log('准备加载报表，token:', this.token)
        // 根据环境设置不同的reportUrl
        switch (process.env.NODE_ENV) {
          case 'development':
            this.reportUrl = `https://jmreport.debug.packertec.com/?token=${this.token}`
            break;
          case 'staging':
          case 'production':
            this.reportUrl = `https://jmreport.packertec.com/?token=${this.token}`
            break;
          default:
            this.reportUrl = `https://jmreport.debug.packertec.com/?token=${this.token}`
        }
        this.loading = false

        // 记录加载情况
        setTimeout(() => {
          console.log('报表URL已设置:', this.reportUrl)
        }, 100)
      } catch (error) {
        console.error('加载失败:', error)
        this.$message.error('加载报表失败，请重试')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.jimu-container {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
