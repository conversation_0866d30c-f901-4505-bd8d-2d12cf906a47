<template>
  <div class="container-wrapper has-organization">
    <!--由原来的分类列表MealFoodClassification修改，改得头疼，有时间后续人员优化一下-->
    <refresh-tool @refreshPage="refreshHandle" />
    <div id="meal-food-classification" v-loading="isLoading">
      <div class="organization-tree">
        <div>
          <button-icon color="origin" type="" @click="clickShowDialogClassification('addSort')" class="m-l-15">
            新建
          </button-icon>
          <ul class="infinite-list" :style="{ overflow: 'auto', height: `${classificationBoxHeight}px` }">
            <li v-for="(item, index) in foodFoodSortPrimaryList" :key="item.id">
              <div class="primary-label" :class="{ active: index === articleTagIndex }">
                <span style="cursor: pointer;flex:1;padding:5px 0;" @click="clickArticleTag(item, index)">
                  {{ item.name }}
                </span>
                <div>
                  <el-popover placement="right" title="" width="150" trigger="hover" content=""
                    popper-class="ps-popover">
                    <div slot="reference">
                      <i class="el-icon-more more-tag"></i>
                    </div>
                    <div class="ps-column">
                      <el-button type="text" size="small" class="ps-text"
                        @click="clickShowDialogClassification('editSort', item)">
                        编辑
                      </el-button>
                      <div class="line-hor"></div>
                      <el-button type="text" size="small" class="ps-text"
                        @click="clickShowDialogClassification('addCategory', item)">
                        新建二级
                      </el-button>
                      <div class="line-hor"></div>
                      <el-button type="text" size="small" class="ps-red" @click="deleteHaldler('delSort', item)">
                        删除
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="label-list">
        <div class="table-wrapper">
          <div class="table-header">
            <div class="m-l-20 m-t-10">一级分类：{{ selectTag.name || '无' }}</div>
            <div class="align-r">
              <button-icon color="origin" @click="clickShowDialogClassification('addCategory')">
                新建分类
              </button-icon>
            </div>
          </div>
          <div class="table-content">
            <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" row-key="id" stripe
              header-row-class-name="ps-table-header-row" class="ps-table" @selection-change="handleSelectionChange">
              <el-table-column prop="name" label="二级分类名称" align="center">
                <template slot-scope="scope">
                  <span v-if="scope.row.color" class="status-point"
                    :style="{ backgroundColor: scope.row.color }"></span>
                  <span>{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="food_count" label="关联菜品数" align="center"
                :render-header="renderHeaderMethods"></el-table-column>
              <el-table-column prop="update_time" label="修改时间" align="center"></el-table-column>
              <el-table-column prop="operator_name" label="操作人" align="center">
                <template slot-scope="scope">
                  {{ scope.row.operator_name || '--' }}
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="180" align="center">
                <template slot-scope="scope">
                  <el-button type="text" size="small" class="ps-text"
                    @click="clickShowDialogClassification('editCategory', scope.row)">
                    编辑
                  </el-button>
                  <span style="margin: 0 10px; color: #e2e8f0">|</span>
                  <el-button type="text" size="small" class="ps-red" @click="deleteHaldler('delCategory', scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="currentPage" :page-sizes="[10, 20, 50, 100, 500]" :page-size="pageSize"
              layout="total, prev, pager, next,sizes,jumper" :total="totalCount" background class="ps-text"
              popper-class="ps-popper-select"></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="批量导入" :visible.sync="importShowDialog" width="600px" custom-class="ps-dialog">
      <import-upload-file @publicUrl="publicUrl" uploadFormItemLabel="导入分类" file-type="zip"
        link="https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/9d831119671ac5dd0f34398007cd4b1a1617760590210.zip"></import-upload-file>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="importShowDialog = false">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="mulImortFace">确 定</el-button>
      </span>
    </el-dialog>
    <el-drawer :title="dialogClassificationTitle" :visible.sync="showDialogSort" size="500px"
      custom-class="ps-el-drawer" direction="rtl" :show-close="false">
      <el-form :model="dialogSortForm" @submit.native.prevent status-icon ref="dialogSortForm"
        :rules="dialogSortFormRules" v-loading="formSortLoading" label-width="80px" class="m-l-20 m-t-20 p-b-20">
        <el-form-item label="一级分类" prop="primarySortName">
          <el-input class="ps-input" style="width: 190px" placeholder="请输入一级分类名称" maxlength="15"
            v-model="dialogSortForm.primarySortName"></el-input>
        </el-form-item>
      </el-form>
      <span class="ps-el-drawer-footer m-l-20 m-t-10">
        <el-button @click="closeDialogSort">取 消</el-button>
        <el-button class="ps-btn" type="primary" :loading="formSortLoading" @click="determineSortDialog">
          确 定
        </el-button>
      </span>
    </el-drawer>
    <el-drawer :title="dialogClassificationTitle" :visible.sync="showDialogCategory" size="500px"
      custom-class="ps-el-drawer" direction="rtl" :show-close="false">
      <el-form :model="dialogCategoryForm" @submit.native.prevent status-icon ref="dialogCategoryForm"
        :rules="dialogCategoryRules" v-loading="formCategoryLoading" label-width="150px" class="m-l-20 m-t-20 p-b-20">
        <el-form-item label="一级分类名称" prop="primarySortId">
          <el-select v-model="dialogCategoryForm.primarySortId" placeholder="请选择一级分类" class="ps-select"
            style="width: 190px" disabled>
            <el-option v-for="item in foodFoodSortPrimaryList" :key="item.id" :label="item.name"
              :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="二级分类名称" prop="categoryName">
          <el-input v-model="dialogCategoryForm.categoryName" maxlength="15" placeholder="请输入二级分类名称" class="ps-input"
            style="width: 190px"></el-input>
        </el-form-item>
        <el-form-item label="颜色标记" prop="color">
          <el-color-picker v-model="dialogCategoryForm.color"></el-color-picker>
        </el-form-item>
      </el-form>
      <span class="ps-el-drawer-footer m-l-20 m-t-10">
        <el-button @click="closeDialogCategory">取 消</el-button>
        <el-button class="ps-btn" type="primary" :loading="formCategoryLoading" @click="determineCategoryDialog">
          确 定
        </el-button>
      </span>
    </el-drawer>
  </div>
</template>

<script>
import { debounce, to, deepClone } from '@/utils'
import ImportUploadFile from '@/components/ImportUploadFile'
export default {
  name: 'MealFoodClassificationNew',
  props: {},
  data() {
    return {
      classificationBoxHeight: 'calc(100vh - 210px)',
      isLoading: false,
      pageSize: 10,
      totalCount: 0,
      currentPage: 1,
      tableData: [],
      searchFormSetting: {
        name: {
          type: 'input',
          label: '二级分类',
          value: '',
          placeholder: '请输入二级分类名称'
        }
      },
      primaryName: '',
      importShowDialog: false,
      dialogClassificationTitle: '',
      showDialogClassificationType: '',
      showDialogCategory: false,
      showDialogSort: false,
      selectList: [
        { name: '特价', id: '1' },
        { name: '折扣', id: '2' }
      ],
      dialogCategoryForm: {
        primarySortId: '',
        categoryName: '',
        color: '#409EFF'
      },
      dialogCategoryRules: {
        primarySortId: [{ required: true, message: '请选择一级分类', trigger: 'change' }],
        categoryName: [{ required: true, message: '请输入一级分类名称', trigger: 'blur' }]
      },
      dialogSortForm: {
        primarySortName: ''
      },
      dialogSortFormRules: {
        primarySortName: [{ required: true, message: '请输入一级分类名称', trigger: 'blur' }]
      },
      isLoadingFoodFoodSor: false,
      formSortLoading: false,
      formCategoryLoading: false,
      foodFoodSortPrimaryList: [],
      showDialogClassificationRow: {},
      selectListId: [],
      delType: '',
      articleTagIndex: -1,
      selectTag: {}
    }
  },
  components: { ImportUploadFile },
  created() {
    this.initLoad()
  },
  watch: {
  },
  mounted() { },
  methods: {
    initLoad() {
      this.foodFoodSortList()
    },
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.articleTagIndex = -1
      this.initLoad()
    }, 300),
    refreshHandle() {
      this.currentPage = 1
      this.articleTagIndex = -1
      this.initLoad()
    },
    async foodFoodSortList() {
      this.isLoadingFoodFoodSor = true
      let params = {
        page: 1,
        page_size: 999999
      }
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodSortListPost(params)
      )
      this.isLoadingFoodFoodSor = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.foodFoodSortPrimaryList = res.data.results
        if (this.foodFoodSortPrimaryList && this.foodFoodSortPrimaryList.length > 0) {
          this.clickArticleTag(this.foodFoodSortPrimaryList[0], 0)
        } else {
          this.selectTag = {}
          this.tableData = []
          this.totalCount = 0
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    async foodFoodSortAdd() {
      this.formSortLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodSortAddPost({
          name: this.dialogSortForm.primarySortName
        })
      )
      this.formSortLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.closeDialogSort()
        this.foodFoodSortList()
      } else {
        this.$message.error(res.msg)
      }
    },
    async foodFoodSortModify() {
      this.formSortLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodSortModifyPost({
          name: this.dialogSortForm.primarySortName,
          id: this.showDialogClassificationRow.id
        })
      )
      this.formSortLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.closeDialogSort()
        this.foodFoodSortList()
      } else {
        this.$message.error(res.msg)
      }
    },
    async foodFoodSorttDelete(row) {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodSortDeletePost({
          ids: [row.id]
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.foodFoodSortList()
        if (this.selectTag.id === row.id) {
          this.selectTag = {}
          this.articleTagIndex = -1
          this.tableData = []
          this.totalCount = 0
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    async foodFoodCategoryList() {
      if (!this.selectTag || !this.selectTag.id) {
        this.tableData = []
        this.totalCount = 0
        return
      }
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodCategoryListPost({
          sort_id: this.selectTag.id,
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async foodFoodCategoryAdd() {
      this.formCategoryLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodCategoryAddPost({
          sort_id: this.dialogCategoryForm.primarySortId,
          name: this.dialogCategoryForm.categoryName,
          color: this.dialogCategoryForm.color
        })
      )
      this.formCategoryLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.closeDialogCategory()
        this.foodFoodCategoryList()
      } else {
        this.$message.error(res.msg)
      }
    },
    async foodFoodCategoryModify() {
      this.formCategoryLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodCategoryModifyPost({
          id: this.showDialogClassificationRow.id,
          status: this.showDialogClassificationRow.status,
          organization: this.showDialogClassificationRow.organization,
          sort_id: this.dialogCategoryForm.primarySortId,
          name: this.dialogCategoryForm.categoryName,
          color: this.dialogCategoryForm.color
        })
      )
      this.formCategoryLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.closeDialogCategory()
        this.foodFoodCategoryList()
      } else {
        this.$message.error(res.msg)
      }
    },
    async foodFoodCategoryDelete(row) {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodCategoryDeletePost({
          ids: this.delType === 'delCategory' ? [row.id] : this.selectListId
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.foodFoodCategoryList()
      } else {
        this.$message.error(res.msg)
      }
    },
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val)
      data.map(item => {
        this.selectListId.push(item.id)
      })
    },
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.foodFoodCategoryList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.foodFoodCategoryList()
    },
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if ((rowIndex + 1) % 2 === 0) {
        str += 'table-header-row'
      }
      return str
    },
    addAndEditMealFood() { },
    deleteHaldler(type, row) {
      if (type === 'delBatchCategory' && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      let _this = this
      let delText = ''
      this.delType = type
      switch (type) {
        case 'delSort':
          delText = ''
          break
        case 'delCategory':
          delText = ''
          break
        case 'delBatchCategory':
          delText = '批量'
          break
        default:
          break
      }
      this.$confirm(`是否${delText}删除该分类？`, `${delText}删除`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            switch (type) {
              case 'delSort':
                _this.foodFoodSorttDelete(row)
                break
              default:
                _this.foodFoodCategoryDelete(row)
                break
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
    },
    publicUrl(data) {
      console.log(data)
    },
    mulImortFace() {
      if (!this.uploadUrl) {
        this.$message.error('食材还没上传完毕或未上传')
        return
      }
    },
    clickShowDialogClassification(type, row) {
      this.showDialogClassificationRow = row || this.selectTag
      this.showDialogClassificationType = type

      if (type === 'addSort') {
        this.dialogClassificationTitle = '新增一级分类'
        this.dialogSortForm = { primarySortName: '' }
        this.showDialogSort = true
        this.$nextTick(() => this.$refs.dialogSortForm && this.$refs.dialogSortForm.clearValidate())
      } else if (type === 'editSort') {
        this.dialogClassificationTitle = '编辑一级分类'
        this.dialogSortForm = { primarySortName: row.name }
        this.showDialogSort = true
        this.$nextTick(() => this.$refs.dialogSortForm && this.$refs.dialogSortForm.clearValidate())
      } else if (type === 'addCategory') {
        const selectTag = row ? deepClone(row) : deepClone(this.selectTag)
        console.log("addCategory", selectTag)
        if (!selectTag || !selectTag.id) {
          return this.$message.warning('请先选择一个一级分类')
        }
        this.dialogClassificationTitle = '新增二级分类'
        this.dialogCategoryForm = {
          primarySortId: selectTag.id,
          categoryName: '',
          color: '#409EFF'
        }
        this.showDialogCategory = true
        this.$nextTick(() => this.$refs.dialogCategoryForm && this.$refs.dialogCategoryForm.clearValidate())
      } else if (type === 'editCategory') {
        this.dialogClassificationTitle = '编辑二级分类'
        this.dialogCategoryForm = {
          primarySortId: row.sort,
          categoryName: row.name,
          color: row.color || '#409EFF'
        }
        this.showDialogCategory = true
        this.$nextTick(() => this.$refs.dialogCategoryForm && this.$refs.dialogCategoryForm.clearValidate())
      }
    },
    determineSortDialog() {
      this.$refs.dialogSortForm.validate(valid => {
        if (valid) {
          if (this.showDialogClassificationType === 'addSort') {
            this.foodFoodSortAdd()
          } else if (this.showDialogClassificationType === 'editSort') {
            this.foodFoodSortModify()
          }
        } else {
          return false
        }
      })
    },
    determineCategoryDialog() {
      this.$refs.dialogCategoryForm.validate(valid => {
        if (valid) {
          if (this.showDialogClassificationType === 'addCategory') {
            this.foodFoodCategoryAdd()
          } else if (this.showDialogClassificationType === 'editCategory') {
            this.foodFoodCategoryModify()
          }
        } else {
          return false
        }
      })
    },
    openImport() {
      this.$message.error('暂无导入')
      return
      this.importShowDialog = true
    },
    clickArticleTag(row, index) {
      if (this.articleTagIndex === index) return
      this.articleTagIndex = index
      this.selectTag = row
      this.currentPage = 1
      this.foodFoodCategoryList()
    },
    closeDialogSort() {
      this.showDialogSort = false
      this.dialogSortForm = { primarySortName: '' }
    },
    closeDialogCategory() {
      this.showDialogCategory = false
      this.dialogCategoryForm = { primarySortId: '', categoryName: '', color: '#409EFF' }
    },
    // 提示标签
    renderHeaderMethods(h, { column }) {
      let msg = '超管关联菜品的数量'
      return h('div', [
        h('span', column.label),
        h(
          'el-tooltip',
          {
            undefined,
            props: {
              undefined,
              effect: 'dark',
              placement: 'top',
              content: ''
            }
          },
          [
            h('div', { slot: 'content' }, [msg, h('br')]),
            h('i', {
              undefined,
              class: 'el-icon-question',
              style: 'color:#909399;'
            })
          ]
        )
      ])
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';

#meal-food-classification {
  display: flex;
  height: calc(100vh - 210px);

  .organization-tree {
    width: 240px;
    min-width: 240px;
    border: 1px solid #e0e6eb;
    border-radius: 12px;
    margin-right: 10px;
    padding: 10px 0;
    background-color: #fff;
    overflow-y: auto;
    display: flex;
    flex-direction: column;

    &::-webkit-scrollbar {
      display: none;
    }

    scrollbar-width: none;
    -ms-overflow-style: none;

    .m-l-10 {
      margin-left: 0;
      width: 100%;
      margin-bottom: 10px;
    }
  }

  .label-list {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }
}

.infinite-list {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-grow: 1;
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: none;
  }

  scrollbar-width: none;
  -ms-overflow-style: none;

  .primary-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-bottom: 4px;

    &:hover {
      background-color: #f5f7fa;
    }

    &.active {
      color: #ff994d;
      background-color: #fff7f0;
      font-weight: 500;
    }

    .more-tag {
      font-size: 16px;
      color: #909399;
      transform: rotate(90deg);
      padding: 5px;
    }

    &:hover .more-tag {
      display: inline-block;
    }
  }
}

.label-list {
  .table-wrapper {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid #e0e6eb;
    border-radius: 12px;
    background-color: #fff;
    overflow: hidden;
    margin-top: 0 !important;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;

    .m-l-20 {
      font-weight: 500;
    }
  }

  .table-header:after {
    content: '';
    display: block;
    width: 100%;
    height: 1px;
    background-color: transparent !important;
  }

  .table-content {
    flex-grow: 1;
    overflow-y: auto;
  }

  .ps-pagination {
    padding: 15px 20px;
    border-top: 1px solid #e0e6eb;
  }
}

.status-point {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
  vertical-align: middle;
}

.el-popover.el-popper {
  &.ps-popover {
    width: 90px !important;
    min-width: 90px !important;
    padding: 5px 0 !important;

    .ps-column {
      display: flex;
      flex-direction: column;

      .el-button--text {
        padding: 8px 12px;
        text-align: center;
        width: 100%;
        border-radius: 0;

        &:hover {
          background-color: #f5f7fa;
        }
      }

      .line-hor {
        height: 1px;
        background-color: #e0e6eb;
        margin: 2px 0;
      }
    }
  }
  .ps-el-drawer-footer {
    border-top: none !important;
  }
}

</style>
