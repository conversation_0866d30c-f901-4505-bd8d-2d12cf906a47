<template>
  <!-- eslint-disable vue/no-unused-vars -->
  <!--由原来的分类列表IngredientsCategory修改，改得头疼，有时间后续人员优化一下-->
  <div class="super-ingredients-category container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div id="ingredients-category-label" v-loading="isLoading">
      <div class="organization-tree">
        <div >
          <button-icon color="origin" type="" @click="clickShowDialogLabel('addCategory')" class="m-l-10">
            新建
          </button-icon>
          <ul class="infinite-list" :style="{ overflow: 'auto' }">
            <li v-for="(item, index) in foodFoodCategoryPrimaryList" :key="index">
              <div class="primary-label" :class="{ active: index === articleTagIndex }">
                <span  style="cursor: pointer;flex:1;padding:5px 0;"
                  @click="clickArticleTag(item, index)">
                  {{ item.name }}
                </span>
                <div>
                  <el-popover placement="right" title="" width="150" trigger="hover" content="" popper-class="ps-popover">
                    <div slot="reference">
                      <i class="el-icon-more more-tag"></i>
                    </div>
                    <div class="ps-column">
                      <el-button type="text" size="small" class="ps-text"
                        @click="clickShowDialogLabel('editCategory', item)">
                        编辑
                      </el-button>
                      <div class="line-hor"></div>
                      <el-button type="text" size="small" class="ps-text" @click="showDialogHandler('add', item)">
                        新建二级
                      </el-button>
                      <div class="line-hor"></div>
                      <el-button type="text" size="small" class="ps-red"
                        @click="deleteCategory('delCategory', item.id)">
                        删除
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="label-list">
        <!-- <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px"
          @search="searchHandle"></search-form> -->
        <div class="table-wrapper">
          <div class="table-header">
            <div class="m-l-20 m-t-10">一级分类：{{selectTag.name}}</div>
            <div class="align-r">
              <button-icon color="origin"  @click="showDialogHandler('add')">
                新建分类
              </button-icon>
              <!-- <button-icon color="plain" type="del" @click="deleteCategory('multi')">
                批量删除
              </button-icon> -->
              <!-- <button-icon color="origin" type="export" @click="gotoExport">导出EXCEL</button-icon> -->
            </div>
          </div>
          <div class="table-content">
            <!-- table start -->
            <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" stripe row-key="id"
              header-row-class-name="ps-table-header-row" height="calc(100vh - 370px)">
              <!-- <el-table-column type="selection" class-name="ps-checkbox" width="55"></el-table-column> -->
              <!-- <el-table-column prop="category_name" label="一级分类名称" align="center"></el-table-column> -->
              <el-table-column prop="name" label="二级分类名称" align="center"></el-table-column>
              <el-table-column prop="ingredient_count" label="关联食材数" align="center"
                :render-header="renderHeaderMethods"></el-table-column>
              <el-table-column prop="update_time" label="修改时间" align="center"></el-table-column>
              <el-table-column prop="operator_name" label="操作人" align="center">
                <template slot-scope="scope">
                  {{ scope.row.operator_name || '--' }}
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                  <el-button type="text" size="small" class="" @click="showDialogHandler('modify', scope.row)">
                    编辑
                  </el-button>
                  <span style="margin: 0 10px; color: #e2e8f0">|</span>
                  <el-button type="text" size="small" class="ps-warn-text"
                    @click="deleteCategory('single', scope.row.id)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <!-- table end -->
          </div>
          <!-- 分页 start -->
          <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="currentPage" :page-sizes="[10, 20, 50, 100, 500]" :page-size="pageSize"
              layout="total, prev, pager, next, sizes, jumper" :total="totalCount" background class="ps-text"
              popper-class="ps-popper-select"></el-pagination>
          </div>
          <!-- 分页 end -->
        </div>
      </div>
    </div>
    <!-- 编辑/添加弹窗 start -->
    <!-- 一级分类 -->
    <el-drawer :title="dialogLabelTitle" :visible.sync="showDialogCategory" size="500px" custom-class="ps-el-drawer" direction="rtl" :show-close="false">
      <el-form @submit.native.prevent status-icon ref="dialogCategoryForm" v-loading="dialogLoading" class="m-l-20 m-t-20 p-b-20"
        :rules="dialogFormDataRuls" :model="dialogFormData" label-width="80px">
        <el-form-item label="一级分类" prop="primaryCategoryName" >
          <el-input class="ps-input" style="width: 190px" placeholder="请输入一级分类名称"
            v-model="dialogFormData.primaryCategoryName"></el-input>
        </el-form-item>
      </el-form>
      <span class="ps-el-drawer-footer m-l-20 m-t-10">
        <el-button @click="closeDialogCategory">取 消</el-button>
        <el-button class="ps-btn" type="primary" :loading="dialogLoading" @click="determineCategoryDialog">
          确 定
        </el-button>
      </span>
    </el-drawer>
    <!-- 二级分类 -->
    <el-drawer :title="dialogTitle" :visible.sync="dialogVisible" size="500px" top="20vh" custom-class="ps-el-drawer" direction="rtl" :show-close="false"
      :close-on-click-modal="false" @closed="dialogHandleClose">
      <el-form v-if="dialogType === 'add' || dialogType === 'modify'" ref="dialogFormRef" v-loading="dialogLoading"
        :rules="dialogFormDataRuls" :model="dialogFormData" class="dialog-form m-l-20 m-t-20 p-b-20" size="small">
        <el-form-item label="一级分类名称" prop="name" class="block-label">
          <el-select v-model="dialogFormData.category_id" placeholder="请下拉选择一级分类" class="ps-select" :disabled="true"
            popper-class="ps-popper-select" style="width: 190px">
            <el-option v-for="(item, index) in foodFoodCategoryPrimaryList" :key="index" :label="item.name"
              :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="二级分类名称" prop="name" class="block-label">
          <el-input class="ps-input" style="width: 190px" v-model="dialogFormData.name"
            placeholder="请输入二级分类名称"></el-input>
        </el-form-item>
      </el-form>
      <span class="ps-el-drawer-footer m-l-20 m-t-10">
        <el-button :disabled="dialogLoading" @click="dialogVisible = false">
          {{ $t('dialog.cancel_btn') }}
        </el-button>
        <el-button class="ps-btn" :disabled="dialogLoading" type="primary" @click="submitHandler">
          {{
            dialogType === 'modify'
              ? $t('dialog.confirm_btn')
              : dialogType === 'add'
                ? $t('dialog.add_btn')
                : $t('dialog.close_btn')
          }}
        </el-button>
      </span>
    </el-drawer>
    <!-- 弹窗 end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, to } from '@/utils'
import { LIBRARY_SEARCH_SETTING_CATEORY } from './constants'

export default {
  name: 'SuperIngredientsCategoryNew',
  mixins: [exportExcel],
  data() {
    return {
      articleTagIndex: -1,
      labelBoxHeight: '',
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: LIBRARY_SEARCH_SETTING_CATEORY,
      dialogData: {},
      dialogTitle: '',
      dialogType: 'add',
      dialogVisible: false,
      dialogLoading: false,
      checkList: [], // table 选中数据的id
      accountData: [],
      dialogFormData: {
        id: '',
        name: '',
        category_id: '',
        primaryCategoryName: ''
      },
      dialogFormDataRuls: {
        name: [{ required: true, message: '分类名称不能为空', trigger: 'blur' }],
        primaryCategoryName: [{ required: true, message: '一级分类名称不能为空', trigger: 'blur' }],
        category_id: [{ required: true, message: '一级分类不能为空', trigger: 'blur' }]
      },
      articleTagRow: {},
      showDialogCategory: false,
      // primaryName: '',
      dialogLabelTitle: '',
      foodFoodCategoryPrimaryList: [],
      selectTag: {} // 选择的一级分类
    }
  },
  created() {
    this.initLoad()
    this.getIngredientCategoryList()
    this.getCategoryCategoryNameList()
  },
  mounted() { },
  methods: {
    initLoad() {
      // this.getCategorylist()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getCategorylist()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      // this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
      this.getIngredientCategoryList()
      this.getCategoryCategoryNameList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value || (data[key].value && data[key].value.length)) {
          if (key !== 'select_time') {
            // 因为要区分一级和二级id 字段不同 如果是一级就用category_id 二级sort_id 目前只有二级分类
            if (key === 'sort_id') {
              data[key].dataList.map(v => {
                if (data[key].value.split('_')[0] === '1') {
                  params.category_id = Number(data[key].value.split('_')[1])
                } else {
                  if (data[key].value.split('_')[0] === '2') {
                    params.sort_id = Number(data[key].value.split('_')[1])
                  }
                }
              })
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 提示标签
    renderHeaderMethods(h, { column }) {
      let msg = '超管关联食材的数量'
      return h('div', [
        h('span', column.label),
        h(
          'el-tooltip',
          {
            undefined,
            props: {
              undefined,
              effect: 'dark',
              placement: 'top',
              content: ''
            }
          },
          [
            h('div', { slot: 'content' }, [msg, h('br')]),
            h('i', {
              undefined,
              class: 'el-icon-question',
              style: 'color:#909399;'
            })
          ]
        )
      ])
    },
    // 获取所有一级食材以及下面的二级食材
    async getCategoryCategoryNameList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientCategoryCategoryNameListPost()
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        console.log(res.data)
        LIBRARY_SEARCH_SETTING_CATEORY.sort_id.dataList = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.sort_list) {
            if (item.sort_list.length > 0) {
              traversal(item.sort_list)
            } else {
              _that.$delete(item, 'sort_list')
            }
          } else {
            _that.$delete(item, 'sort_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 获取二级食材分类列表
    async getCategorylist() {
      this.isLoading = true

      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientSortListPost({
          // ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize,
          category_id: this.selectTag.id
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取食材一级分类列表
    async getIngredientCategoryList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminIngredientCategoryListPost({}))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.foodFoodCategoryPrimaryList = res.data
        if (this.foodFoodCategoryPrimaryList && this.foodFoodCategoryPrimaryList.length > 0) {
          this.selectTag = this.foodFoodCategoryPrimaryList[0]
          this.articleTagIndex = 0
          this.getCategorylist()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改分类名
    async modifySort() {
      this.dialogLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientSortModifyPost({
          id: this.dialogFormData.id,
          name: this.dialogFormData.name,
          category_id: this.dialogFormData.category_id
        })
      )
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getCategorylist()
        this.dialogVisible = false
      } else {
        this.$message.error(res.msg)
      }
    },
    // 打开弹窗
    showDialogHandler(type, data) {
      this.dialogType = type
      if (type === 'add') {
        this.dialogFormData.id = ''
        this.dialogFormData.name = ''
        this.dialogFormData.category_id = data ? data.id : this.selectTag.id
        this.dialogTitle = '添加二级分类'
      } else {
        this.dialogFormData.id = data.id
        this.dialogFormData.name = data.name
        this.dialogFormData.category_id = data.category
        this.dialogTitle = '编辑二级分类'
      }
      this.dialogVisible = true
    },
    // 弹窗确定事件
    submitHandler() {
      this.$refs.dialogFormRef.validate(valid => {
        if (!valid) return
        if (this.dialogLoading) return this.$message.error('请勿重复提交！')
        if (this.dialogType === 'add') {
          this.addSort()
        } else {
          this.modifySort()
        }
      })
    },
    // 添加分类
    async addSort() {
      this.dialogLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientSortAddPost({
          name: this.dialogFormData.name,
          category_id: this.dialogFormData.category_id
        })
      )
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getCategorylist()
        this.dialogVisible = false
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改一级分类名
    async modifyCategor() {
      this.dialogLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientCategoryModifyPost({
          id: this.dialogFormData.id,
          name: this.dialogFormData.primaryCategoryName
        })
      )
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getIngredientCategoryList()
        this.showDialogCategory = false
      } else {
        this.$message.error(res.msg)
      }
    },
    // 添加二级分类
    async addCategory() {
      this.dialogLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientCategoryAddPost({
          name: this.dialogFormData.primaryCategoryName
        })
      )
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getIngredientCategoryList()
        this.showDialogCategory = false
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除分类
    deleteCategory(type, id) {
      let ids = []
      if (type === 'single' || type === 'delCategory') {
        ids = [id]
      } else {
        if (!this.checkList.length) return this.$message.error('请先选择要删除的数据！')
        ids = this.checkList
      }
      this.$confirm(`确定删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogLoading) return this.$message.error('请勿重复提交！')
            this.dialogLoading = true
            instance.confirmButtonLoading = true
            let [err, res] = ''
            if (type === 'delCategory') {
              ;[err, res] = await to(
                this.$apis.apiBackgroundAdminIngredientCategoryDeletePost({
                  ids: ids
                })
              )
            } else {
              ;[err, res] = await to(
                this.$apis.apiBackgroundAdminIngredientSortDeletePost({
                  ids: ids
                })
              )
            }

            this.dialogLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              if (type === 'delCategory') {
                this.getIngredientCategoryList()
              } else {
                this.getCategorylist()
              }
              this.checkList = []
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => { })
        .catch(e => { })
    },
    // 列表选择
    handleSelectionChange(e) {
      this.checkList = e.map(item => {
        return item.id
      })
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getCategorylist()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getCategorylist()
    },
    // 关闭弹窗回调
    dialogHandleClose(e) {
      // 重置数据
      if (this.dialogType === 'add' || this.dialogType === 'edit') {
        this.$refs.dialogFormRef.clearValidate()
        this.dialogFormData = {
          name: ''
        }
      }
      this.dialogData = {}
      this.dialogTitle = ''
      this.dialogType = ''
    },
    gotoExport() {
      const option = {
        type: 'SuperIngredientsCategory',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    // 点击一级标签
    clickArticleTag(row, index) {
      // 用于点击某条数据 颜色
      this.articleTagIndex = index
      this.selectTag = row
      this.getCategorylist()
      // this.articleTagRow = row // 一级标签内容 选中
      // this.getArticleTagChildList({ parent_id: row.id })
    },
    clickShowDialogLabel(type, row) {
      this.dialogType = type
      if (type === 'addCategory') {
        this.dialogLabelTitle = '新增分类'
        this.dialogFormData = {
          id: '',
          name: '',
          category_id: '',
          primaryCategoryName: ''
        }
        this.showDialogCategory = true
      } else if (type === 'editCategory') {
        this.dialogLabelTitle = '编辑分类'
        this.dialogFormData = {
          id: row.id,
          name: '',
          category_id: '',
          primaryCategoryName: row.name
        }
        this.showDialogCategory = true
      }
    },
    determineCategoryDialog() {
      this.$refs.dialogCategoryForm.validate(valid => {
        if (!valid) return
        if (this.dialogLoading) return this.$message.error('请勿重复提交！')
        if (this.dialogType === 'addCategory') {
          this.addCategory()
        } else {
          this.modifyCategor()
        }
      })
    },
    // 关闭一级分类弹窗
    closeDialogCategory() {
      this.showDialogCategory = false
      this.dialogFormData = {
        id: '',
        name: '',
        category_id: '',
        primaryCategoryName: ''
      }
      setTimeout(() => {
        if (this.$refs.dialogCategoryForm) {
          this.$refs.dialogCategoryForm.clearValidate()
        }
      }, 100)
    }
    // changeArticleTag() {}
  },
  watch: {
    // tableData: function () {
    //   this.$nextTick(function () {
    //     this.labelBoxHeight =
    //       document.getElementsByClassName('search-form-wrapper')[0].offsetHeight +
    //       document.getElementsByClassName('table-wrapper')[0].offsetHeight -
    //       140
    //   })
    // }
  }
}
</script>

<style lang="scss" scoped>
#ingredients-category-label {
  display: flex;

  .label-list {
    flex: 1;
    min-width: 0;
    margin-left: 10px;
    // background-color: ;
  }

  .active {
    color: #ff994d;
    background-color: #e2e8f0;
  }
}

.super-ingredients-category {
  height: 78vh;
  .organization-tree {
    border-radius: 12px;
    height: 78vh;
    overflow: auto;
    padding: 10px 0;
    &::-webkit-scrollbar {
    width: 0 !important; /* 宽度设为0，滚动条消失 */
    height: 0 !important; /* 高度设为0（垂直滚动条场景） */
    }
    /* 隐藏 Firefox 滚动条（需 Firefox 64+） */
    scrollbar-width: none;
    /* 隐藏 IE/Edge 滚动条 */
    -ms-overflow-style: none;
    }
}
.container-wrapper .table-wrapper {
  margin-top: 0 !important;
  height: 78vh;
  overflow: hidden;
}

.infinite-list {
  &::-webkit-scrollbar {
    width: 0 !important; /* 宽度设为0，滚动条消失 */
    height: 0 !important; /* 高度设为0（垂直滚动条场景） */
  }
  /* 隐藏 Firefox 滚动条（需 Firefox 64+） */
  scrollbar-width: none;
  /* 隐藏 IE/Edge 滚动条 */
  -ms-overflow-style: none;
  .primary-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    padding: 0 20px;
    font-size: 14px;
  }
}

.dialog-form {
  .block-label {
    width: 100%;

    .el-form-item__label {
      display: block;
      text-align: left;
      line-height: 1.5;
      float: none;
    }
  }
}

.more-tag {
  // 旋转90度
  font-size: 20px;
  transform: rotate(90deg);
  cursor: pointer;
}
.el-popover.el-popper{
  &.ps-popover{
    width: 90px !important;
    min-width: 90px !important;
  }
}

.ps-column {
  display: flex;
  flex-direction: column;
}

.line-hor {
  width: 100%;
  height: 1px;
  background-color: #e2e8f0;
}

</style>
