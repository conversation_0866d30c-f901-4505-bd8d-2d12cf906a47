<!--批量修改分类 使用 el-drawer 组件-->
<template>
  <custom-drawer
    :show="visible"
    title="详情"
    size="50%"
    @close="handleClose"
    @cancel="handleClose"
    @confirm="handleClose"
    :confirmShow="true"
    :cancelShow="false"
    confirm-text="关闭"
    v-bind="$attrs"
    v-on="$listeners">
    <div class="ingredients-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <!-- <div class="section-title">基本信息</div> -->
        <div class="info-grid m-t-20">
          <div class="info-item full-width">
            <span class="label">菜品/商品名称</span>
            <span class="value">{{ data.name || '--' }}</span>
          </div>

          <div class="info-item full-width">
            <span class="label">别名</span>
            <div class="value"><div :class="['value-block', index > 0 ? 'm-t-5' : '' ]" v-for="(item,index) in data.alias_name" :key="index">{{ item }}</div></div>
          </div>

          <div class="info-item full-width">
            <span class="label">属性</span>
            <span class="value">{{ data.attributes === 'goods' ? '商品' : '菜品'  }}</span>
          </div>

          <div class="info-item full-width">
            <span class="label">图片</span>
            <el-image
              class="food-image m-l-20"
              :src="data.image || require('@/assets/img/ingredients_default.png')"
              :preview-src-list="[data.image || require('@/assets/img/ingredients_default.png')]"
              fit="cover">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </div>

          <div class="info-item full-width">
            <span class="label m-r-20 ">识别照片</span>
            <div v-if="!data.extra_image || data.extra_image.length === 0 ">无</div>
              <div class="image-list">
                <el-image
                  v-for="(item, index) in data.extra_image"
                  :key="index"
                  :class="['food-image', index % 5 !==0 ? 'm-l-20' : '', index > 3 ? 'm-t-10' : '']"
                  :src="item"
                  fit="cover"
                  :preview-src-list="[item]">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
          </div>

          <div class="info-item full-width">
            <span class="label">分类</span>
            <span class="value">{{ data.sort_name + " - " + data. category_name }}</span>
          </div>

          <div class="info-item full-width" v-if="data.attributes !== 'goods'">
            <span class="label">烹饪方式</span>
            <span class="value">{{ data.cooking_manner_alias || '--' }}</span>
          </div>
        </div>
      </div>

      <!-- 标签信息 -->
      <div class="detail-section" v-if="data.label && data.label.length">
        <div class="title-label">标签</div>
        <div class="tags-container m-l-20">
          <el-tag
            v-for="(item, index) in data.label"
            :key="index"
            class="tag-item"
            type="warning"
            effect="plain">
            {{ item.name }}
          </el-tag>
        </div>
      </div>

      <!-- 食材占比 -->
      <div class="detail-section" v-if="data.ingredients_list && data.ingredients_list.length">
        <div class="title-label">食材占比</div>
        <div class="ratio-table m-l-20">
          <el-table :data="data.ingredients_list" border style="width: 100%" stripe header-row-class-name="ps-table-header-row">
            <el-table-column prop="ingredient_name" label="食材" align="center" />
            <el-table-column prop="ingredient_scale" label="占比" align="center">
              <template slot-scope="scope">
                {{ scope.row.ingredient_scale }}%
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 营养信息 -->
      <div class="detail-section">
        <div class="title-label">食材营养</div>
        <div class="nutrition-grid">
          <template v-if="nutritionInfo.default">
            <div class="nutrition-item" v-for="(value, key) in nutritionInfo.default" :key="key">
              <span class="label">{{ getNutritionLabel(key) }}（{{ getNutritionUnit(key) }}）：</span>
              <span class="value">{{ value }}</span>
            </div>
          </template>
          <template v-if="nutritionInfo.element">
            <div class="nutrition-item" v-for="(value, key) in nutritionInfo.element" :key="'element_'+key">
              <span class="label">{{ getNutritionLabel(key) }}（{{ getNutritionUnit(key) }}）：</span>
              <span class="value">{{ value }}</span>
            </div>
          </template>
          <template v-if="nutritionInfo.vitamin">
            <div class="nutrition-item" v-for="(value, key) in nutritionInfo.vitamin" :key="'vitamin_'+key">
              <span class="label">{{ getNutritionLabel(key) }}（{{ getNutritionUnit(key) }}）：</span>
              <span class="value">{{ value }}</span>
            </div>
          </template>
        </div>
      </div>
    </div>
  </custom-drawer>
</template>

<script>
import { deepClone } from '@/utils'
import { NUTRITION_LIST } from '../../health-nutrition/constants'

export default {
  name: 'IngredientsLibraryDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      nutritionList: deepClone(NUTRITION_LIST),
      nutritionInfo: {}
    }
  },
  computed: {
    hasNutrition() {
      return this.data.nutritionInfo && Object.keys(this.data.nutritionInfo).length > 0
    }

  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close', false)
    },
    // 获取食材别名
    getAliasName(aliasName) {
      if (!aliasName || aliasName.length === 0) return '--'
      return aliasName.join('、')
    },
    // 获取营养标签名称
    getNutritionLabel(key) {
      const nutrition = this.nutritionList.find(item => item.key === key)
      return nutrition ? nutrition.name : key
    },
    // 获取营养单位
    getNutritionUnit(key) {
      const nutrition = this.nutritionList.find(item => item.key === key)
      return nutrition ? nutrition.unit : ''
    },
    // 获取营养信息
    getNutritionInfo() {
      if (!this.data.nutrition_info) return {}
      console.log("this.data.nutritionInfo", this.data.nutrition_info);
      const info = {
        default: {},
        element: {},
        vitamin: {}
      }

      // 处理基础营养信息 - 按照 NUTRITION_LIST 中 default 类型的顺序
      this.nutritionList.forEach(nutrition => {
        if (nutrition.type === 'default') {
          info.default[nutrition.key] = this.data.nutrition_info.default[nutrition.key] || 0
        }
      })

      // 处理元素信息
      if (this.data.nutrition_info.element) {
        const elementData = JSON.parse(this.data.nutrition_info.element)
        // 按照 NUTRITION_LIST 中 element 类型的顺序
        this.nutritionList.forEach(nutrition => {
          if (nutrition.type === 'element') {
            info.element[nutrition.key] = elementData[nutrition.key] || 0
          }
        })
      }

      // 处理维生素信息
      if (this.data.nutrition_info.vitamin) {
        const vitaminData = JSON.parse(this.data.nutrition_info.vitamin)
        // 按照 NUTRITION_LIST 中 vitamin 类型的顺序
        this.nutritionList.forEach(nutrition => {
          if (nutrition.type === 'vitamin') {
            info.vitamin[nutrition.key] = vitaminData[nutrition.key] || 0
          }
        })
      }

      return info
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        console.log("newVal", newVal);
        if (newVal) {
          this.nutritionInfo = this.getNutritionInfo()
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ingredients-detail {
  padding: 0 20px;

  .detail-section {
    margin-bottom: 24px;
    display: flex;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #23282d;
      margin-bottom: 16px;
      padding-left: 8px;
      border-left: 4px solid #ff9b45;
    }
    .title-label {
      min-width: 120px;
      color: #7b7c82;
      flex-shrink: 0;
      text-align: right;
    }

    .info-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }

    .info-item {
      display: flex;
      align-items: baseline;

      &.full-width {
        width: 100%;
      }

      .label {
        min-width: 120px;
        color: #7b7c82;
        flex-shrink: 0;
        text-align: right;
        align-self: flex-start;
      }

      .value {
        color: #23282d;
        flex: 1;
        word-break: break-all;
        margin-left: 20px;
      }
      .value-block {
        display: block;
      }
    }

    .food-image {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border-radius: 4px;
    }

    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .tag-item {
        margin-right: 8px;
        margin-bottom: 8px;
        background-color: #ff9b51;
        border-color: #ff9b51;
        color: #fff;
      }
    }

    .ratio-table {
      width: 500px;
      :deep(.el-table) {
        th {
          background-color: #f5f7fa;
          color: #606266;
        }

        td {
          color: #606266;
        }
      }
    }

    .nutrition-grid {
      flex: 1;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;

      .nutrition-item {
        display: flex;
        align-items: center;
        font-size: 14px;

        .label {
          min-width: 140px;
          color: #666;
          flex-shrink: 0;
          text-align: right;
          padding-right: 4px;
        }

        .value {
          color: #23282d;
        }
      }
    }
  }
  .image-list {
    width: 700px;
  }
}
</style>
