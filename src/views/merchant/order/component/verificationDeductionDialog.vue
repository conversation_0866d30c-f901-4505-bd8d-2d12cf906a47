<template>
  <div>
    <el-dialog :visible.sync="visible" :title="title" :width="width" @close="handlerClose">
      <p class="twoRefund">{{ dialogInfo.msg }}</p>

      <template #footer>
        <div class="dialog-footer">
          <el-button class="ps-cancel-btn" @click="visible = false">重新匹配</el-button>
          <el-button class="ps-btn" @click="clickDeductionDialog">补扣</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog :visible.sync="deductionVisible" :title="'补扣'" :width="width" @close="deductionVisible = false">
      <el-form :model="deductionForm" :rules="deductionFormRules" ref="deductionForm">
        <el-form-item prop="amount">
          <el-input placeholder="请输入扣款金额" v-model="deductionForm.amount" class="w-300">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="ps-cancel-btn" @click="deductionVisible = false">取消</el-button>
          <el-button class="ps-btn" @click="clickConfirmDeduction">确认并扣款</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { validataPlateAmountPrice } from '@/assets/js/validata'
import NP from 'number-precision'
export default {
  name: 'ImageViewPreview',
  props: {
    loading: Boolean,
    isshow: Boolean,
    title: {
      // 标题
      type: String,
      default: '提示'
    },
    width: {
      // 宽度
      type: String,
      default: '600px'
    },
    dialogType: {
      // 类型
      type: String,
      default: 'default'
    },
    dialogInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },

  data() {
    return {
      isLoading: false,
      deductionVisible: false,
      deductionForm: {
        amount: ''
      },
      deductionFormRules: {
        amount: [
          { required: true, message: '请输入扣款金额', trigger: 'blur' },
          { validator: validataPlateAmountPrice, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    visible: {
      get() {
        console.log('visible', this.isshow)
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {},
  methods: {
    // 关闭
    handlerClose() {
      this.visible = false
      this.$emit('close', false)
    },
    clickDeductionDialog() {
      this.deductionForm.amount = ''
      if (this.dialogInfo.activeTab === 'scan') {
        // 扣款的activeTab 传进来的
        let params = {
          origin_fee: NP.times(this.dialogInfo.origin_fee, 100)
        }
        this.setAddOrdervaBindUser(params)
      } else {
        this.deductionVisible = true
      }
    },
    clickConfirmDeduction() {
      this.$refs.deductionForm.validate(valid => {
        if (valid) {
          // 这里可以调用扣款接口
          // 扣款成功后关闭弹窗
          let params = {
            origin_fee: NP.times(this.deductionForm.amount, 100)
          }
          this.setAddOrdervaBindUser(params)
        } else {
          console.log('表单验证失败')
          return false
        }
      })
    },
    // 扣款金额
    async setAddOrdervaBindUser(params) {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOrderOrderVerificationAbnormalAddOrdervaBindUserPost({
          meal_type: this.dialogInfo.meal_type,
          verification_abnormal_id: this.dialogInfo.verification_abnormal_id,
          card_info_id: this.dialogInfo.card_info_id,
          ...params
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.deductionVisible = false
        this.visible = false
        this.$emit('comfirmDeduction')
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
<style lang="scss" scoped></style>
