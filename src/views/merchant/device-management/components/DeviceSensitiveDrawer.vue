<template>
  <div class="drawer-box">
    <customDrawer :show.sync="showDrawer" :loading="isLoading" :title="title" :size="850">
      <div class="drawer-container">
        <div class="red">配置离线时，设备触发脱机提示弹窗阈值</div>
        <div class="drawer-content">
          <div class="m-b-10">
            情况1:当设备在
            <el-input-number v-model="drawerFormData.number1" :min="3" size="mini"></el-input-number>
            分钟内，网络健康检查接口连续产生次
            <el-input-number v-model="drawerFormData.number2" :min="3" size="mini"></el-input-number>
            超时；
          </div>
          <div>
            情况2:当下单接口连续
            <el-input-number v-model="drawerFormData.number3" :min="2" size="mini"></el-input-number>
            次超时
            <el-input-number v-model="drawerFormData.number4" :min="5" size="mini"></el-input-number>
            秒以上。
          </div>
        </div>
      </div>
    </customDrawer>
  </div>
</template>

<script>
export default {
  name: 'DeviceSensitiveDrawer',
  components: {},
  props: {
    isshow: Boolean,
    dialogInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false,
      isLoading: false,
      title: '脱敏设置',
      drawerFormData: {
        number1: '3',
        number2: '3',
        number3: '2',
        number4: '5'
      }
    }
  },
  computed: {
    showDrawer: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {},
  methods: {
    closeClick() {
      this.showDrawer = false
    },
    saveSetting() {
      this.$refs.drawerFormDataRef.validate(valid => {
        if (valid) {
          this.setSensitiveSetting()
        }
      })
    }
    // async setSensitiveSetting() {
    //   if (this.isLoading) return
    //   this.isLoading = true
    //   const res = await this.$apis.apiBackgroundDeviceDeviceModifyPost(params)
    //   this.isLoading = false
    //   if (res.code === 0) {
    //     this.$message.success('保存成功')
    //     this.visible = false
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // }
  }
}
</script>

<style lang="scss" scoped>
.drawer-container {
  padding: 20px;
  .drawer-content {
    margin-top: 20px;
    padding: 20px;
    background: #f2f2f2;
    border-radius: 20px;
  }
}
</style>
