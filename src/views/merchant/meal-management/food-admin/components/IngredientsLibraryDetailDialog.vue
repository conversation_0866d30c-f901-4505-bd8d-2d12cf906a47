<!--批量修改分类 使用 el-drawer 组件-->
<template>
  <custom-drawer
    :show="visible"
    title="食材详情"
    size="45%"
    @close="handleClose"
    @cancel="handleClose"
    @confirm="handleClose"
    :confirmShow="true"
    :cancelShow="false"
    confirm-text="关闭"
    v-bind="$attrs"
    v-on="$listeners">
    <div class="ingredients-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <div class="info-grid">
           <!-- 食材名称独占一行 -->
           <div class="info-item full-width">
            <span class="label">食材名称</span>
            <span class="value">{{ data.name || '--' }}</span>
          </div>

          <!-- 食材别名独占一行 -->
          <div class="info-item full-width">
            <span class="label">食材别名</span>
            <div class="value"><div class="value-block" v-for="(item,index) in data.alias_name" :key="index">{{ item }}</div></div>
          </div>
          <!-- 食材图片 -->
          <div class="info-item full-width">
            <span class="label">食材图片</span>
            <el-image
              class="food-image m-l-20"
              :src="data.image || require('@/assets/img/ingredients_default.png')"
              :preview-src-list="[data.image || require('@/assets/img/ingredients_default.png')]"
              fit="cover"
              >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </div>
          <div class="info-item  full-width">
            <span class="label">生熟比</span>
            <span class="value">{{ getPersent(data) }}</span>
          </div>
          <!-- 食材分类 -->
          <div class="info-item full-width">
            <span class="label">食材分类</span>
            <span class="value">{{ data.category_name || '--' }} - {{ data.sort_name || '--' }}</span>
          </div>

          <div class="info-item full-width">
            <span class="label">食材应季</span>
            <span class="value">{{ getSeasonalMonth(data.seasonal_month) }}</span>
          </div>
        </div>
      </div>

      <!-- 标签信息 -->
      <div class="detail-section" v-if="data.label && data.label.length">
        <div class="tags-container">
          <div class="title-label">食材标签</div>
          <el-tag
            v-for="(item, index) in data.label"
            :key="index"
            class="m-r-5 m-b-5 m-l-10"
            size="medium"
            effect="plain"
            type="light">
            {{ item.name }}
          </el-tag>
        </div>
      </div>

      <!-- 营养信息 -->
      <div class="detail-section" v-if="hasNutrition">
        <div class="title-label">食材营养</div>
        <div class="nutrition-grid">
          <template v-if="nutritionInfo.default">
            <div class="nutrition-item" v-for="(value, key) in nutritionInfo.default" :key="key">
              <span class="label">{{ getNutritionLabel(key) }}（{{ getNutritionUnit(key) }}）：</span>
              <span class="value">{{ value }}</span>
            </div>
          </template>
          <template v-if="nutritionInfo.element">
            <div class="nutrition-item" v-for="(value, key) in nutritionInfo.element" :key="'element_'+key">
              <span class="label">{{ getNutritionLabel(key) }}（{{ getNutritionUnit(key) }}）：</span>
              <span class="value">{{ value }}</span>
            </div>
          </template>
          <template v-if="nutritionInfo.vitamin">
            <div class="nutrition-item" v-for="(value, key) in nutritionInfo.vitamin" :key="'vitamin_'+key">
              <span class="label">{{ getNutritionLabel(key) }}（{{ getNutritionUnit(key) }}）：</span>
              <span class="value">{{ value }}</span>
            </div>
          </template>
        </div>
      </div>

    </div>
  </custom-drawer>
</template>

<script>
import { replaceSingleQuote, deepClone } from '@/utils'
import { NUTRITION_LIST } from '@/views/super/health-system/health-nutrition/constants'

export default {
  name: 'IngredientsLibraryDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      nutritionList: deepClone(NUTRITION_LIST)
    }
  },
  computed: {
    hasNutrition() {
      return this.data.nutrition && Object.keys(this.data.nutrition).length > 0
    },
    nutritionInfo() {
      if (!this.data.nutrition) return {}

      const info = {
        default: {},
        element: {},
        vitamin: {}
      }

      // 处理基础营养信息 - 按照 NUTRITION_LIST 中 default 类型的顺序
      this.nutritionList.forEach(nutrition => {
        if (nutrition.type === 'default') {
          info.default[nutrition.key] = this.data.nutrition[nutrition.key] || 0
        }
      })

      // 处理元素信息
      if (this.data.nutrition.element) {
        const elementData = JSON.parse(replaceSingleQuote(this.data.nutrition.element))
        // 按照 NUTRITION_LIST 中 element 类型的顺序
        this.nutritionList.forEach(nutrition => {
          if (nutrition.type === 'element') {
            info.element[nutrition.key] = elementData[nutrition.key] || 0
          }
        })
      }

      // 处理维生素信息
      if (this.data.nutrition.vitamin) {
        const vitaminData = JSON.parse(replaceSingleQuote(this.data.nutrition.vitamin))
        // 按照 NUTRITION_LIST 中 vitamin 类型的顺序
        this.nutritionList.forEach(nutrition => {
          if (nutrition.type === 'vitamin') {
            info.vitamin[nutrition.key] = vitaminData[nutrition.key] || 0
          }
        })
      }

      return info
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close', false)
    },
    // 获取食材别名
    getAliasName(aliasName) {
      if (!aliasName || aliasName.length === 0) return '--'
      return aliasName.join('、')
    },
    // 获取食材应季
    getSeasonalMonth(list) {
      if (!list) return '--'
      return list.map(v => v.month_name).join('、')
    },
    // 获取生熟比
    getPersent(row) {
      let rawWeight = parseFloat(row.raw_weight) || 0;
      let cookedWeight = parseFloat(row.cooked_weight) || 0;
      if (rawWeight === 0 || cookedWeight === 0) return '--';
      const percentage = (rawWeight / cookedWeight) * 100;
      return `${percentage.toFixed(2)}%`;
    },
    // 获取营养标签名称
    getNutritionLabel(key) {
      const nutrition = this.nutritionList.find(item => item.key === key)
      return nutrition ? nutrition.name : key
    },
    // 获取营养单位
    getNutritionUnit(key) {
      const nutrition = this.nutritionList.find(item => item.key === key)
      return nutrition ? nutrition.unit : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.ingredients-detail {
  padding: 0 20px;

  .detail-section {
    margin-bottom: 24px;
    display: flex;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #23282d;
      margin-bottom: 16px;
      padding-left: 8px;
      border-left: 4px solid #ff9b45;
    }
    .title-label {
      min-width: 100px;
      color: #666;
      flex-shrink: 0;
      text-align: right;
    }

    .info-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }

    .info-item {
      display: flex;
      align-items: baseline;

      &.half-width {
        width: calc(50% - 8px); // 减去gap的一半
      }

      &.full-width {
        width: 100%;
      }

      .label {
        min-width: 100px;
        color: #666;
        flex-shrink: 0;
        text-align: right;
      }

      .value {
        color: #23282d;
        flex: 1;
        word-break: break-all;
        margin-left: 20px;
      }
      .value-block {
        display: block;
        margin-bottom: 5px;
      }
    }

    .food-image {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border-radius: 4px;
    }

    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .nutrition-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;

      .nutrition-item {
        display: flex;
        align-items: center;

        .label {
          min-width: 140px;
          color: #666;
          flex-shrink: 0;
          text-align: right;
          padding-right: 4px;
        }

        .value {
          color: #23282d;
        }
      }
    }
  }
}

.m-r-5 {
  margin-right: 5px;
}

.m-b-5 {
  margin-bottom: 5px;
}
</style>
