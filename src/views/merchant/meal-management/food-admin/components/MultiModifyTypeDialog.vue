<!--批量修改分类 使用 el-drawer 组件-->
<template>
  <custom-drawer
    :show="visible"
    title="批量修改分类"
    size="30%"
    confirm-text="保存"
    @close="handleClose"
    @cancel="handleClose"
    @confirm="clickConfirmHandle"
    v-bind="$attrs"
    v-on="$listeners">
    <el-form :model="form" label-width="120px" ref="formRef" :rules="formRuls">
      <el-form-item :label="type === 'ingredient' ? '食材类别' : '菜品类别'" prop="sort_id">
        <el-cascader
          class="ps-select"
          :placeholder="type === 'ingredient' ? '请选择或输入食材类别' : '请选择或输入菜品类别'"
          style="width: 80%"
          v-model="form.sort_id"
          :options="categoryList"
          :show-all-levels="false"
          :props="cascaderProps"
        ></el-cascader>
      </el-form-item>
    </el-form>
  </custom-drawer>
</template>

<script>
export default {
  name: 'MultiModifyTypeDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    categoryList: {
      type: Array,
      default: () => []
    },
    selectedList: {
      type: Array,
      default: () => []
    },
    type: { // 类型：ingredient 食材，food 菜品
      type: String,
      default: 'ingredient'
    }
  },
  data() {
    return {
      form: {
        sort_id: ''
      },
      cascaderProps: {
        label: 'name',
        value: 'id',
        children: 'sort_list',
        emitPath: false
      },
      formRuls: {
        sort_id: [{ required: true, message: this.type === 'ingredient' ? '请选择食材类别' : '请选择菜品类别', trigger: 'blur' }]
      }
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        if (newVal) {
          this.resetForm()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 重置表单
    resetForm() {
      this.form.sort_id = ''
      this.cascaderProps = this.type === 'ingredient' ? {
        label: 'name',
        value: 'id',
        children: 'sort_list',
        emitPath: false
      } : {
        label: 'name',
        value: 'id',
        children: 'children',
        emitPath: false
      }
      if (this.$refs.formRef) {
        this.$refs.formRef.clearValidate()
      }
    },
    // 关闭处理
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close', false)
    },
    // 提交处理
    clickConfirmHandle() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.saveBatchClassify()
        }
      })
    },
    // 保存批量分类
    async saveBatchClassify() {
      let api = this.type === 'ingredient' ? this.$apis.apiBackgroundFoodIngredientBatchClassifyPost : this.$apis.apiBackgroundFoodBatchClassifyPost
      let params = {
        id_list: this.selectedList
      }
      if (this.type === 'ingredient') {
        params.sort_id = this.form.sort_id
      } else {
        params.category_id = this.form.sort_id
      }
      const [err, res] = await this.$to(api(params))
      if (err) {
        this.$message.error(err.message)
      }
      if (res && res.code === 0) {
        this.$message.success('修改成功')
        this.$emit('submit', this.form)
      } else {
        this.$message.error(res.msg || '修改失败')
      }
    }
  }
}
</script>

<style scoped>
.el-drawer__body {
  padding: 20px;
}
</style>
