<!--批量修改分类 使用 el-drawer 组件-->
<template>
  <custom-drawer :show="visible" title="菜品/商品详情" size="45%" @close="handleClose" @cancel="handleClose"
    @confirm="handleClose" :confirmShow="true" :cancelShow="false" confirm-text="关闭" v-bind="$attrs" v-on="$listeners">
    <div class="ingredients-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <div class="info-grid">
          <!-- 名称独占一行 -->
          <div class="info-item full-width">
            <span class="label">菜品/商品名称</span>
            <span class="value">{{ data.name || '--' }}</span>
          </div>

          <!-- 别名独占一行 -->
          <div class="info-item full-width">
            <span class="label">别名</span>
            <div class="value">
              <div class="value-block" v-for="(item, index) in data.alias_name" :key="index">{{ item }}</div>
            </div>
          </div>
          <!-- 属性独占一行 -->
          <div class="info-item full-width">
            <span class="label">属性</span>
            <span class="value">{{ data.attributes === 'goods' ? '商品' : '菜品' }}</span>
          </div>
          <!-- 属性独占一行 -->
          <div class="info-item full-width" v-if="data.attributes === 'goods'">
            <span class="label">条形码</span>
            <span class="value">{{ data.barcode }}</span>
          </div>
          <!-- 图片 -->
          <div class="info-item full-width">
            <span class="label">图片</span>
            <el-image class="food-image m-l-20" fit="cover"
              :src="data.image || 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'"
              :preview-src-list="[data.image || 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png']">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </div>
          <!-- 识别图片 -->
          <div class="info-item full-width">
            <span class="label">识别图片</span>
            <div class='image-list'>
              <el-image class="food-image m-l-20" fit="cover" v-for="(item, index) in data.extra_image" :key="index"
                :src="item || 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'"
                :preview-src-list="[item || 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png']">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
          </div>
          <!-- 分类 -->
          <div class="info-item full-width">
            <span class="label">分类</span>
            <span class="value">{{ data.sort_name || '--' }} - {{ data.category_name || '--' }}</span>
          </div>
          <!-- 烹饪方式 -->
          <div class="info-item full-width" v-if="data.attributes !== 'goods'">
            <span class="label">烹饪方式</span>
            <span class="value">{{ data.cooking_manner_alias }}</span>
          </div>
        </div>
      </div>
      <!-- 价格信息 -->
      <div class="detail-section">
        <div class="title-label">价格信息</div>
        <!--分三种情况：count_type1.菜品/商品 2.称重价格 3.菜品/商品加称重价格-->
        <div class="ratio-table m-l-20">
          <div class="">类型：{{ getNameByType(data.price_info) }}</div>
          <el-table :data="data.spec_list" border style="width: 600px;margin-top: 20px;" stripe
            header-row-class-name="ps-table-header-row" v-if="countType === 1">
            <el-table-column prop="name" label="规格" align="center" />
            <el-table-column prop="food_price" label="价格（¥）" align="center">
              <template slot-scope="scope">
                {{ getPrice(scope.row.food_price) }}
              </template>
            </el-table-column>
            <el-table-column prop="weight" label="重量（g）" align="center">
              <template slot-scope="scope">
                {{ scope.row.weight || '--' }}
              </template>
            </el-table-column>
          </el-table>
          <div v-if="countType === 2 || countType === 3">
            <div class="m-t-20">称重类型: {{ getWeightTypeName(data.price_info) }}</div>
            <div class="m-t-20" v-if="getWeightType(data.price_info) === 1">称重价格：{{ data.price_info ? data.price_info.weight_price : '0'}}元/{{ data.price_info ? data.price_info.weight : '' }}克</div>
            <div class="m-t-20 ps-flex" v-if="getWeightType(data.price_info) === 2">
              <div class="">称重价格：{{ data.price_info ? data.price_info.weight_price : '0' }}元/份</div>
              <div class="m-l-40">单份重量：{{ data.price_info ? data.price_info.weight : '' }}克</div>
            </div>
            <div class="m-t-20 ps-flex" v-if="getWeightType(data.price_info) === 2">
              <div class="">起始计价重量：{{ data.price_info ? data.price_info.start_gram : '0' }}克</div>
              <div class="m-l-40">误差率：{{ data.price_info ? data.price_info.fault_rate : '' }}</div>
            </div>
            <div class="m-t-20 ps-flex" v-if="countType === 3">
              <div class="">菜品/商品价格：{{ data.price_info ? data.price_info.food_price : '0' }}元</div>
            </div>
          </div>
          <div class="m-t-20">成本价（元）: {{ getOriginPrice(data) }}</div>
          <div class="m-t-20">打包费（元）: {{ data.price_info ? data.price_info.pack_price : '--' }}</div>
        </div>
      </div>

      <!-- 标签信息 -->
      <div class="detail-section" v-if="data.label && data.label.length">
        <div class="tags-container">
          <div class="title-label">标签</div>
          <el-tag v-for="(item, index) in data.label" :key="index" class="m-r-5 m-b-5 m-l-10" size="medium"
            effect="plain" type="light">
            {{ item.name }}
          </el-tag>
        </div>
      </div>
      <!-- 食材占比 -->
      <div class="detail-section" v-if="data.ingredients_list && data.ingredients_list.length">
        <div class="title-label">食材占比</div>
        <div class="ratio-table m-l-20">
          <el-table :data="data.ingredients_list" border style="width: 600px" stripe
            header-row-class-name="ps-table-header-row">
            <el-table-column prop="ingredient_name" label="食材" align="center" />
            <el-table-column prop="ingredient_scale" label="占比" align="center">
              <template slot-scope="scope">
                {{ scope.row.ingredient_scale }}%
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 营养信息 -->
      <div class="detail-section" v-if="hasNutrition">
        <div class="title-label">食材营养</div>
        <div class="nutrition-grid">
          <template v-if="nutritionInfo.default">
            <div class="nutrition-item" v-for="(value, key) in nutritionInfo.default" :key="key">
              <span class="label">{{ getNutritionLabel(key) }}（{{ getNutritionUnit(key) }}）：</span>
              <span class="value">{{ value }}</span>
            </div>
          </template>
          <template v-if="nutritionInfo.element">
            <div class="nutrition-item" v-for="(value, key) in nutritionInfo.element" :key="'element_' + key">
              <span class="label">{{ getNutritionLabel(key) }}（{{ getNutritionUnit(key) }}）：</span>
              <span class="value">{{ value }}</span>
            </div>
          </template>
          <template v-if="nutritionInfo.vitamin">
            <div class="nutrition-item" v-for="(value, key) in nutritionInfo.vitamin" :key="'vitamin_' + key">
              <span class="label">{{ getNutritionLabel(key) }}（{{ getNutritionUnit(key) }}）：</span>
              <span class="value">{{ value }}</span>
            </div>
          </template>
        </div>
      </div>

    </div>
  </custom-drawer>
</template>

<script>
import { replaceSingleQuote, deepClone, divide } from '@/utils'
import { NUTRITION_LIST } from '@/views/super/health-system/health-nutrition/constants'

export default {
  name: 'MealFoodLibraryDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      nutritionList: deepClone(NUTRITION_LIST)
    }
  },
  computed: {
    hasNutrition() {
      return this.data.nutrition_info && Object.keys(this.data.nutrition_info).length > 0
    },
    // 价格信息类型
    countType() {
      let priceInfo = this.data.price_info || {}
      let countType = priceInfo.count_type || ''
      return countType
    },
    nutritionInfo() {
      if (!this.data.nutrition_info) return {}

      const info = {
        default: {},
        element: {},
        vitamin: {}
      }

      // 处理基础营养信息 - 按照 NUTRITION_LIST 中 default 类型的顺序
      this.nutritionList.forEach(nutrition => {
        if (nutrition.type === 'default') {
          info.default[nutrition.key] = this.data.nutrition_info.default[nutrition.key] || 0
        }
      })

      // 处理元素信息
      if (this.data.nutrition_info.element) {
        const elementData = JSON.parse(replaceSingleQuote(this.data.nutrition_info.element))
        // 按照 NUTRITION_LIST 中 element 类型的顺序
        this.nutritionList.forEach(nutrition => {
          if (nutrition.type === 'element') {
            info.element[nutrition.key] = elementData[nutrition.key] || 0
          }
        })
      }

      // 处理维生素信息
      if (this.data.nutrition_info.vitamin) {
        const vitaminData = JSON.parse(replaceSingleQuote(this.data.nutrition_info.vitamin))
        // 按照 NUTRITION_LIST 中 vitamin 类型的顺序
        this.nutritionList.forEach(nutrition => {
          if (nutrition.type === 'vitamin') {
            info.vitamin[nutrition.key] = vitaminData[nutrition.key] || 0
          }
        })
      }

      return info
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close', false)
    },
    // 获取食材别名
    getAliasName(aliasName) {
      if (!aliasName || aliasName.length === 0) return '--'
      return aliasName.join('、')
    },
    // 获取食材应季
    getSeasonalMonth(list) {
      if (!list) return '--'
      return list.map(v => v.month_name).join('、')
    },
    // 获取生熟比
    getPersent(row) {
      let rawWeight = parseFloat(row.raw_weight) || 0;
      let cookedWeight = parseFloat(row.cooked_weight) || 0;
      if (rawWeight === 0 || cookedWeight === 0) return '--';
      const percentage = (rawWeight / cookedWeight) * 100;
      return `${percentage.toFixed(2)}%`;
    },
    // 获取营养标签名称
    getNutritionLabel(key) {
      const nutrition = this.nutritionList.find(item => item.key === key)
      return nutrition ? nutrition.name : key
    },
    // 获取营养单位
    getNutritionUnit(key) {
      const nutrition = this.nutritionList.find(item => item.key === key)
      return nutrition ? nutrition.unit : ''
    },
    // 获取打包费等价格
    getPrice(price) {
      if (!price) {
        return '--'
      }
      return divide(price)
    },
    // 获取成本价
    getOriginPrice(row) {
      // const priceInfo = row.price_info || {}
      // const countType = priceInfo.count_type
      // const weightPrice = priceInfo.weight_price || 0
      // const foodPrice = priceInfo.food_price || 0
      // const specList = row.spec_list || []
      // if (countType === 2) {
      //   return divide(weightPrice)
      // } else if (countType === 3) {
      //   return divide(foodPrice)
      // }
      // return specList.map(item => divide(item.food_price)).join('、')
      const priceInfo = row.price_info || {}
      const originPrice = priceInfo.origin_price || 0
      return originPrice
    },
    // 根据类型获取价格名称
    getNameByType(priceInfo) {
      const type = priceInfo.count_type || ""
      switch (type) {
        case 1:
          return '菜品/商品价格'
        case 2:
          return '称重价格'
        case 3:
          return '菜品/商品价格+称重价格'
        default:
          break;
      }
      return ''
    },
    // 获取称重类型名称
    getWeightTypeName(priceInfo) {
      if (!priceInfo) {
        return ''
      }
      const type = priceInfo.weight_type || ""
      switch (type) {
        case 1:
          return '按克'
        case 2:
          return '按份'
        default:
          break;
      }
      return ''
    },
    // 获取称重类型
    getWeightType(priceInfo) {
      if (!priceInfo) {
        return ''
      }
      const type = priceInfo.weight_type || ""
      return type
    }
  }
}
</script>

<style lang="scss" scoped>
.ingredients-detail {
  padding: 0 20px;

  .detail-section {
    margin-bottom: 24px;
    display: flex;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #23282d;
      margin-bottom: 16px;
      padding-left: 8px;
      border-left: 4px solid #ff9b45;
    }

    .title-label {
      min-width: 120px;
      color: #666;
      flex-shrink: 0;
      text-align: right;
    }

    .info-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }

    .info-item {
      display: flex;
      align-items: baseline;

      &.half-width {
        width: calc(50% - 8px); // 减去gap的一半
      }

      &.full-width {
        width: 100%;
      }

      .label {
        min-width: 120px;
        color: #666;
        flex-shrink: 0;
        align-self: flex-start;
        text-align: right;
      }

      .value {
        color: #23282d;
        flex: 1;
        word-break: break-all;
        margin-left: 20px;
      }

      .value-block {
        display: block;
        margin-bottom: 5px;
      }
    }

    .food-image {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border-radius: 4px;
    }

    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .nutrition-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;

      .nutrition-item {
        display: flex;
        align-items: center;

        .label {
          min-width: 140px;
          color: #666;
          flex-shrink: 0;
          text-align: right;
          padding-right: 4px;
        }

        .value {
          color: #23282d;
        }
      }
    }
  }
}

.m-r-5 {
  margin-right: 5px;
}

.m-b-5 {
  margin-bottom: 5px;
}

.image-list {
  width: 700px;
}
</style>
