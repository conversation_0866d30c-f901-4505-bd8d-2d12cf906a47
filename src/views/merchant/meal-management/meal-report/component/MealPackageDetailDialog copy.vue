<template>
  <div class="MealPackageDetailDialog">
    <CustomDrawer
      :show.sync="showDrawer"
      :size="760"
      :title="title"
      :loading.sync="isLoading"
      fixedFooter
      :confirmShow="false"
      cancelText="取消"
    >
      <div class="meal-package-box m-r-20">
        <ul class="tab-box">
          <li v-for="(tab, index) in talList" :class="['tab-item pointer', activeTab === tab.value ? 'active' : '']" @click="clickTabHandle(tab)">{{ tab.label }}</li>
        </ul>
        <div class="meal-package-content">
          <el-scrollbar class="package-wrapper">
            <ul class="content-box" v-if="packageList.length > 0">
              <li v-for="(item, index) in packageList" class="content-item">
                <div class="content-item-left">
                  <span class="status inline-block is-take m-r-10">{{ item.take_meal_status_alias }}</span>
                  <span class="time inline-block m-r-10">{{ item.report_date }}</span>
                  <span class="week inline-block m-r-10">星期{{ parseTime(item.report_date, '{a}') }}</span>
                  <span class="meal inline-block m-r-10">{{ item.meal_type_alias }}</span>
                </div>
                <div class="content-refund m-r-20">
                  <el-button v-if="item.take_meal_status === 'no_take'" type="text" size="mini" class="ps-text" @click="clickRefundHandle(item)">去退款</el-button>
                </div>
              </li>
            </ul>
            <div v-else class="">暂无更多数据</div>
          </el-scrollbar>
        </div>
        <table-statistics v-if="packageList.length > 0" class="m-t-20" v-loading="isLoadingCollect" element-loading-custom-class="el-loading-wrapp"  element-loading-spinner="loading" :element-loading-text="elementLoadingText" :statistics="collect" />
      </div>
    </CustomDrawer>
  </div>
</template>

<script>
import { MEAL_TYPES } from '@/utils/constants'
import { debounce, parseTime } from '@/utils'
export default {
  name: 'MealPackageDetailDialog',
  props: {
    isshow: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    title: {
      type: String,
      default: '餐包使用情况'
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      activeTab: 'all',
      talList: [
        { label: '全部', value: 'all' },
        ...MEAL_TYPES
      ],
      packageList: [],
      collect: [ // 统计
        { key: 'total', value: 0, label: '已订餐：', dot: true },
        { key: 'take_out_total', value: 0, label: '已取餐：', dot: true },
        { key: 'no_take_total', value: 0, label: '未用餐：', dot: true },
        { key: 'refund_total', value: 0, label: '已退款：' },

      ],
      elementLoadingText: "数据正在加载，请耐心等待...",
      isLoadingCollect: false,
    }
  },
  computed: {
    showDrawer: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    isshow(val) {
      if (val) {
        this.packageList = []
        this.activeTab = 'all'
        this.getMealPackageDetail()
        this.getMealPackageCountDetail()
      }
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    parseTime,
    // tab点击事件
    clickTabHandle: debounce(function(item) {
      this.activeTab = item.value
      this.getMealPackageDetail()
    }, 200),
    async getMealPackageDetail() {
      this.isLoading = true
      let params = {
        page: 1,
        page_size: 999999,
        unified_trade_no: this.infoData.trade_no,
        date_type: 'pay_time',
        start_date: parseTime(this.infoData.pay_time, '{y}-{m}-{d}'),
        end_date: parseTime(new Date(parseTime(this.infoData.pay_time, '{y}/{m}/{d}')).getTime() + 24 * 60 * 60 * 1000, '{y}-{m}-{d}')
      }
      if (this.activeTab !== 'all') {
        params.meal_type_list = [this.activeTab]
      }
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderPaymentListPost(params))
      this.isLoading = false
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.packageList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getMealPackageCountDetail() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderReportMealOrderReportMealPackDetailsCountPost({
        unified_trade_no: this.infoData.trade_no
      }))
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.collect.forEach(item => {
          for (let i in res.data) {
            if (item.key === i) {
              item.value = res.data[i]
            }
          }
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    clickRefundHandle(item) {
      let query = {
        tab: 1,
        queryData: {
          trade_no: item.trade_no,
          date_type: 'pay_time',
          select_time: [parseTime(this.infoData.pay_time, '{y}-{m}-{d}'), parseTime(new Date(parseTime(this.infoData.pay_time, '{y}/{m}/{d}')).getTime() + 24 * 60 * 60 * 1000, '{y}-{m}-{d}')]
        } 
      }
      if (this.activeTab !== 'all') {
        query.queryData.meal_type_list = [this.activeTab]
      }
      query.queryData = JSON.stringify(query.queryData)
      this.$router.push({
        name: 'MerchantConsumption',
        query: query
      })
    }
  }
};
</script>

<style scoped lang="scss">
.MealPackageDetailDialog{
  .w-input {
    width: 360px;
  }
  .meal-package-box {
    .tab-box {
      .tab-item {
        display: inline-block;
        min-width: 60px;
        margin-right: 20px;
        margin-bottom: 10px;
        padding: 8px 20px;
        background: inherit;
        background-color: rgba(242, 242, 242, 1);
        border: none;
        border-radius: 8px;
        font-size: 12px;
        &:last-child{
          margin-right: 0;
        }
        &.active{
          color: #fff;
          background-color: #ff9b45;
        }
      }
    }
    
    .meal-package-content {
      padding: 20px;
      border-radius: 8px;
      background-color: #f2f2f2;
      font-size: 13px;
    }
    .content-box {
      .content-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        &:last-child{
          margin-bottom: 20px;
        }
        .content-item-left {
          flex: 1;
        }
        &:hover {
          background-color: #ededed;
        }
      }
      .status {
        padding: 4px 6px;
        border-radius: 4px;
        background-color: #aaaaaa;
        &.is-take {
          background-color: #ff9b45;
        }
      }
      
    }
  }
}
::v-deep .package-wrapper {
  .el-scrollbar__wrap {
    max-height: calc( 100vh - 72px - 66px - 136px ) !important;
  }
}
</style>
