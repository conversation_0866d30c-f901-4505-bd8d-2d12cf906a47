<template>
  <div class="drawer-box">
    <customDrawer :show.sync="visible" :loading="isLoading" :title="'删除'" :size="800" @confirm="saveSetting">
      <div class="drawer-container">
        <div class="drawer-content">
          <el-form
            :model="drawerFormData"
            @submit.native.prevent
            status-icon
            ref="drawerFormDataRef"
            :rules="drawerFormDataRuls"
            label-position="top"
          >
            <el-form-item label="请输入删除原因:" prop="remark">
              <el-input
                type="textarea"
                class="ps-input"
                size="small"
                maxlength="100"
                :autosize="{ minRows: 4, maxRows: 8 }"
                placeholder="请输入，不超过100字"
                v-model="drawerFormData.remark"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </customDrawer>
  </div>
</template>

<script>
export default {
  props: {
    isshow: Boolean,
    drawerData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      drawerFormData: {
        remark: ''
      },
      drawerFormDataRuls: {
        remark: [{ required: true, message: '请输入删除原因', trigger: ['blur', 'change'] }]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {},
  methods: {
    closeClick() {
      this.visible = false
    },
    saveSetting() {
      this.$refs.drawerFormDataRef.validate(valid => {
        if (valid) {
          this.$confirm('确定删除吗？', `提示`, {
            confirmButtonText: this.$t('dialog.confirm_btn'),
            cancelButtonText: this.$t('dialog.cancel_btn'),
            closeOnClickModal: false,
            customClass: 'ps-confirm',
            cancelButtonClass: 'ps-cancel-btn',
            confirmButtonClass: 'ps-btn',
            center: true,
            beforeClose: async (action, instance, done) => {
              if (action === 'confirm') {
                instance.confirmButtonLoading = true
                const [err, res] = await this.$to(
                  this.$apis.apiBackgroundStoreRetentionRecordFoodReservedSampleRecordDeletePost({
                    id: this.drawerData.id,
                    remark: this.drawerFormData.remark
                  })
                )
                instance.confirmButtonLoading = false
                if (err) {
                  this.$message.error(err.message)
                  return
                }
                if (res.code === 0) {
                  done()
                  this.visible = false
                  this.$message.success('删除成功')
                  this.$emit('clickSaveDrawer')
                } else {
                  this.$message.error(res.msg)
                }
              } else {
                if (!instance.confirmButtonLoading) {
                  done()
                }
              }
            }
          })
            .then(e => {})
            .catch(e => {})
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-box {
  .drawer-container {
    position: relative;
    height: 100%;
    overflow: hidden;
    .drawer-content {
    }
    .drawer-footer {
      position: absolute;
      bottom: 20px;
      left: 20px;
      width: 100%;
      text-align: left;
    }
  }
  ::v-deep .el-drawer__header {
    margin-bottom: 0;
    padding: 23px 20px;
    background: #e7e9ef;
  }
}
</style>
