<template>
  <div class="drawer-box">
    <customDrawer :show.sync="visible" :loading="isLoading" :title="'销样时间'" :size="800" @confirm="saveSetting">
      <div class="drawer-container">
        <div class="drawer-content">
          <el-input-number
            v-model="drawerFormData.time"
            controls-position="right"
            :min="0"
            @input.native="handleNativeInput($event, 'time')"
          ></el-input-number>
          <span style="color: #ff9b45" class="m-r-10">时</span>
          <el-input-number
            v-model="drawerFormData.divide"
            controls-position="right"
            :min="0"
            @input.native="handleNativeInput($event, 'divide')"
          ></el-input-number>
          <span style="color: #ff9b45">分</span>
          <span class="m-l-10">自动销样</span>
          <div style="color: #ff9b45" class="m-t-10">未设置则以取样时间作为销样时间</div>
        </div>
      </div>
    </customDrawer>
  </div>
</template>

<script>
export default {
  props: {
    isshow: Boolean,
    drawerData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      drawerFormData: {
        time: '',
        divide: ''
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {
    this.getRecordGetDestroyTime()
  },
  methods: {
    closeClick() {
      this.visible = false
    },
    async getRecordGetDestroyTime() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundStoreRetentionRecordGetDestroyTimePost()
      this.isLoading = false
      if (res && res.code === 0) {
        // 接口返回的是秒，需要转换为小时和分钟
        const destroyTimeInSeconds = res.data.destroy_time || 0
        this.convertSecondsToTimeAndDivide(destroyTimeInSeconds)
      } else {
        this.$message.error(res.msg)
      }
    },
    async setRecordGetDestroyTime() {
      this.isLoading = true
      // 将小时和分钟转换为秒
      const destroyTimeInSeconds = this.convertTimeAndDivideToSeconds()
      const res = await this.$apis.apiBackgroundStoreRetentionRecordModifyDestroyTimePost({
        destroy_time: destroyTimeInSeconds
      })
      this.isLoading = false
      if (res && res.code === 0) {
        this.$message.success('设置成功')
        this.visible = false
      } else {
        this.$message.error(res.msg)
      }
    },
    handleNativeInput(e, type) {
      const value = e.target.value
      // 只保留数字
      const intValue = value.replace(/[^0-9]/g, '')
      this.drawerFormData[type] = intValue === '' ? null : Number(intValue)
    },
    // async delDrawerForm() {
    // },
    saveSetting() {
      this.setRecordGetDestroyTime()
    },
    // 将秒转换为小时和分钟
    convertSecondsToTimeAndDivide(seconds) {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      this.drawerFormData.time = hours
      this.drawerFormData.divide = minutes
    },
    // 将小时和分钟转换为秒
    convertTimeAndDivideToSeconds() {
      const hours = this.drawerFormData.time || 0
      const minutes = this.drawerFormData.divide || 0
      return hours * 3600 + minutes * 60
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-box {
  .drawer-container {
    position: relative;
    height: 100%;
    overflow: hidden;
    .drawer-content {
      padding: 0 20px;
    }
    .drawer-footer {
      position: absolute;
      bottom: 20px;
      left: 20px;
      width: 100%;
      text-align: left;
    }
  }
  ::v-deep .el-drawer__header {
    margin-bottom: 0;
    padding: 23px 20px;
    background: #e7e9ef;
  }
}
</style>
