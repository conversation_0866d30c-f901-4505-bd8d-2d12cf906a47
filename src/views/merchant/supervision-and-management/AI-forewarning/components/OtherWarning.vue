<template>
  <div>
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
import { to, debounce, deepClone } from '@/utils'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import dayjs from 'dayjs'

export default {
  components: {
    ElImageViewer
  },
  data() {
    return {
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '预警时间',
          clearable: false,
          value: [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
        },
        org_type: {
          type: 'select',
          label: '组织属性',
          value: '',
          placeholder: '请选择类型',
          isLinkage: true,
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '学校',
              value: 'school'
            },
            {
              label: '供应商',
              value: 'vendor'
            }
          ]
        },
        warn_type: {
          type: 'select',
          label: '证件类型',
          value: '',
          placeholder: '请选择类型',
          isLinkage: true,
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '日常经营',
              value: 'day_business'
            }
          ]
        }
      },
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '预警时间', key: 'warn_date' },
        { label: '组织属性', key: 'org_attribute_alias' },
        { label: '监管组织', key: 'supervision_channel_name' },
        { label: '所属组织', key: 'org_name' },
        { label: '类型', key: 'warn_type_alias' },
        { label: '预警情况', key: 'msg' }
      ],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      supportList: [],
      showImagePreview: false,
      previewList: []
    }
  },
  async created() {
    await this.getSupportList()
    this.getDataList()
  },
  methods: {
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    async getDataList() {
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionEarlyWarningOtherWarnMsgListPost({
        page: this.currentPage,
        page_size: this.pageSize,
        ...this.formatQueryParams(this.searchFormSetting)
      }))
      if (err) {
        this.$message.error(err.msg)
        return
      }
      if (res && res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = deepClone(res.data.results)
      } else {
        this.$message.error(res.msg)
        return
      }
    },
    async getSupportList() {
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionEarlyWarningGetSubordinateSupplierManagePost())
      if (err) {
        this.$message.error(err.msg)
        return
      }
      if (res && res.code === 0) {
        this.supportList = res.data.map(item => {
          let obj = {
            label: item.name,
            value: item.id
          }
          return obj
        })
      } else {
        this.$message.error(res.msg)
        return
      }
    },
    handleClick(data) {
      if (data.image_json.length) {
        this.previewList = [data.image_json[0].img]
      } else {
        this.previewList = [data.image_json.img]
      }
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
