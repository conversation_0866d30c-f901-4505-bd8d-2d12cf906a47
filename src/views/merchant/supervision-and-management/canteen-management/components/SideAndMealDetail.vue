<template>
  <div class="ps-el-drawer">
    <el-drawer
      title="详情"
      :visible="drawerShow"
      :show-close="false"
      size="50%"
    >
      <div class="p-20" v-if="infoData">
        <el-form ref="drawerFormRef" :model="drawerForm" label-width="140px" label-position="right">
          <el-form-item label="公司名称" >
            {{ infoData.name || '--' }}
          </el-form-item>
          <el-form-item :label="'统一社会信用代码'" prop="name">
            {{ infoData.uscc || '--' }}
          </el-form-item>
          <el-form-item :label="'负责人'" prop="name">
            {{ infoData.head_person || '--' }}
          </el-form-item>
          <el-form-item :label="'联系电话'" prop="name">
            {{ infoData.phone || '--' }}
          </el-form-item>
          <el-form-item :label="'公司地址'" prop="address">
            {{ infoData.address || '--' }}
          </el-form-item>
          <el-form-item :label="'启用状态'" prop="status">
            {{ infoData.status === 'enable' ? '启用' : '禁用' }}
          </el-form-item>
          <el-form-item :label="'经营执照'" prop="images">
            <template v-if="infoData.business_license && infoData.business_license.length">
              <el-image v-for="url in infoData.business_license" :key="url" class="image" :src="url" :preview-src-list="infoData.business_license" />
            </template>
            <template v-else>--</template>
          </el-form-item>
          <el-form-item label="中标通知书">
            <template v-if="infoData.win_the_bid && infoData.win_the_bid.length">
              <div v-for="fileItem in infoData.win_the_bid" :key="fileItem.uid" class="file-item">
                <span>{{ fileItem.name }}</span>
                <span class="color-gray m-l-10">{{ formatFileSize(fileItem.size) }}</span>
                <i @click="downloadHandle(fileItem)" class="el-icon-download down-icon"></i>
              </div>
            </template>
            <template v-else>--</template>
          </el-form-item>
          <el-form-item label="合同">
            <template v-if="infoData.contract && infoData.contract.length">
              <div v-for="fileItem in infoData.contract" :key="fileItem.uid" class="file-item">
                <span>{{ fileItem.name }}</span>
                <span class="color-gray m-l-10">{{ formatFileSize(fileItem.size) }}</span>
                <i @click="downloadHandle(fileItem)" class="el-icon-download down-icon"></i>
              </div>
            </template>
            <template v-else>--</template>
          </el-form-item>
        </el-form>
        <div class="ps-el-drawer-footer">
          <el-button type="primary" class="w-100" @click="cancelHandle">关 闭</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import FileSaver from 'file-saver'
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  computed: {
    drawerShow: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  data() {
    return {
      drawerForm: {
        name: '',
        uscc: '',
        head_person: '',
        imageFileList: [],
        awardFileList: [],
        contractFileList: []
      },
      uploading: false, // 上传加载中
      serverUrl: '/api/background/file/upload',
      uploadParams: { // 上传头
        prefix: 'incumbents',
        key: 'incumbents' + new Date().getTime() + Math.floor(Math.random() * 150)
      },
      fileLists: [],
      totalUploadedSize: 0 // 中标通知书文件总大小
    }
  },
  methods: {
    // 取消弹窗
    cancelHandle() {
      this.drawerShow = false
    },
    // 下载附件
    async downloadHandle(file) {
      const response = await fetch(file.url)
      const blob = await response.blob()
      FileSaver.saveAs(blob, file.name)
    },
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return ''
      const mb = bytes / (1024 * 1024)
      // 如果小于0.01MB则写死0.01MB
      if (mb < 0.01) {
        return '0.01 MB'
      }
      return mb.toFixed(2) + ' MB'
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__label{
  color: #999999;
}
.image{
  margin-right: 10px;
  width: 100px;
  height: 100px;
}
.down-icon{
  margin-left: 10px;
  font-size: 18px;
  cursor: pointer;
  color: #FF9B45;
}
.color-gray{
  color: #8c939d;
}
.file-item{
  height: 28px;
}
</style>
