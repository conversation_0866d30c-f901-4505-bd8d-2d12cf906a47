<template>
  <!-- 新建、编辑团餐公司 -->
  <div class="ps-el-drawer">
    <el-drawer
      :title="title"
      :visible="drawerShow"
      :show-close="false"
      @open="openHandle"
      size="40%"
    >
      <div class="p-20">
        <el-form ref="drawerFormRef" :model="drawerForm" :rules="addDataFormRules" label-width="140px" label-position="right">
          <el-form-item prop="name" :label="'公司名称'">
            <el-input v-model.trim="drawerForm.name" class="w-300" placeholder="请输入" maxlength="20" :disabled="selectType === 'edit'"></el-input>
          </el-form-item>
          <el-form-item prop="uscc" :label="'统一社会信用代码'">
            <el-input v-model.trim="drawerForm.uscc" class="w-300" placeholder="请输入" maxlength="18" :disabled="selectType === 'edit'"></el-input>
          </el-form-item>
          <el-form-item :label="'负责人'" prop="head_person">
            <el-input v-model.trim="drawerForm.head_person" class="w-300" placeholder="请输入" maxlength="12"></el-input>
          </el-form-item>
          <el-form-item :label="'联系电话'" prop="phone">
            <el-input v-model.trim="drawerForm.phone" class="w-300" placeholder="请输入" maxlength="11"></el-input>
          </el-form-item>
          <el-form-item :label="'公司地址'" prop="address">
            <el-input
              v-model.trim="drawerForm.address"
              class="w-300"
              type="textarea"
              autosize
              placeholder="请输入（不超过50字）"
              maxlength="50"
            ></el-input>
          </el-form-item>
          <el-form-item :label="'启用状态'" prop="status">
            <el-switch v-model="drawerForm.status" active-value="enable" inactive-value="disable" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item :label="'经营执照'" prop="images">
            <div style="color: #a5a5a5;">
              仅支持jpg、png、bmp格式，大小不超过2M，最多上传5张
            </div>
            <el-upload
              class="avatar-uploader"
              :class="{'hide-upload': fileLists.length >= 5}"
              v-loading="uploading"
              element-loading-text="上传中"
              :data="uploadParams"
              list-type="picture-card"
              ref="uploadFood"
              :limit="10"
              :action="serverUrl"
              :on-success="uploadSuccess"
              :before-upload="beforeImgUpload"
              :before-remove="beforeRemove"
              :headers="headersOpts"
              :file-list="fileLists"
            >
            <i class="el-icon-circle-plus" style="color: #ff9b45;"></i>
            </el-upload>
          </el-form-item>
          <el-form-item label="中标通知书">
            <file-upload
              ref="winTheBidFileRef"
              :fileList="drawerForm.win_the_bid"
              type="enclosure"
              prefix="notice"
              tips="最多上传9个文件，总数不得超过20M"
              :rename="false"
              :isCustomizeTemplateFile="true"
              :limit="9"
              :before-upload="(file) => beforeUpload(file, 'win_the_bid')"
              @fileLists="(files) => getFileLists(files, 'win_the_bid')"
            />
          </el-form-item>
          <el-form-item label="合同">
            <file-upload
              ref="contractFileRef"
              :fileList="drawerForm.contract"
              type="enclosure"
              prefix="notice"
              tips="最多上传9个文件，总数不得超过20M"
              :rename="false"
              :isCustomizeTemplateFile="true"
              :limit="9"
              :before-upload="(file) => beforeUpload(file, 'contract')"
              @fileLists="(files) => getFileLists(files, 'contract')"
            />
          </el-form-item>
        </el-form>
        <div class="ps-el-drawer-footer">
          <el-button size="small" class="w-100" @click="cancelHandle">取消</el-button>
          <el-button size="small" type="primary" class="w-100" @click="saveHandleBefore">保存</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { getToken, deepClone } from '@/utils/index'
import { validateSocialCreditCode } from "@/utils/form-validata.js"
// import { pickBy } from 'lodash';
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    selectType: {
      type: String,
      default: 'add'
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default: '新增'
    }
  },
  computed: {
    drawerShow: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  data() {
    return {
      drawerForm: {
        name: '',
        uscc: '',
        head_person: '',
        phone: '',
        address: '',
        status: 'enable',
        business_license: [],
        win_the_bid: [],
        contract: []
      },
      addDataFormRules: {
        name: [{ required: true, message: '请输入公司名称', trigger: ['blur'] }],
        uscc: [
          { required: true, message: '请输入统一社会信用代码', trigger: ['blur'] },
          { validator: validateSocialCreditCode, trigger: 'blur' }
        ],
        phone: [
          { message: '请输入联系电话', trigger: ['blur'] },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: ['blur'] }
        ]
      },
      uploading: false, // 上传加载中
      serverUrl: '/api/background/file/upload',
      uploadParams: { // 上传头
        prefix: 'incumbents',
        key: 'incumbents' + new Date().getTime() + Math.floor(Math.random() * 150)
      },
      fileLists: [],
      headersOpts: { // 上传插入表头
        TOKEN: getToken()
      },
      uploadedSizes: {
        win_the_bid: 0, // 中标通知书总大小
        contract: 0 // 合同总大小
      },
      allUsccList: [] // 已存在的所有统一社会信用代码
    }
  },
  methods: {
    // 打开弹窗
    openHandle() {
      console.log(this.infoData, 'this.infoData')
      if (this.selectType === 'edit') {
        // 合并表单数据
        this.$set(this, 'drawerForm', {
          ...this.drawerForm,
          ...this.infoData,
          status: this.infoData.status || 'enable'
        })
        // 处理文件列表的通用方法
        const processFiles = (files, type) => {
          if (!files || !files.length) return []
          let time = Date.now()
          return files.map(item => {
            time += 1
            let fileName = item.url ? item.url.substring(item.url.lastIndexOf('/') + 1) : item.substring(item.lastIndexOf('/') + 1)
            // 处理文件名中的查询参数
            const queryIndex = fileName.indexOf('?')
            if (queryIndex > -1) {
              fileName = fileName.substring(0, queryIndex)
            }
            // 计算文件大小（仅适用于附件）
            if (item.file_size) {
              this.totalUploadedSize += item.file_size
            }
            return {
              name: fileName,
              status: 'success',
              uid: time,
              url: item.url || item,
              size: item.size,
              ...(type === 'business_license' ? {
                response: {
                  data: {
                    public_url: item
                  }
                }
              } : {})
            }
          })
        }
        // 处理图片
        this.fileLists = processFiles(this.infoData.business_license, 'business_license')
        this.drawerForm.business_license = this.infoData.business_license || []
        // 处理附件
        this.drawerForm.win_the_bid = processFiles(this.infoData.win_the_bid, 'win_the_bid')
        this.drawerForm.contract = processFiles(this.infoData.contract, 'contract')
        // 计算文件大小
        this.calculateFileSizes()
      } else {
        this.resetFormData()
      }
      this.drawerShow = true
      this.$nextTick(() => {
        this.$refs.drawerFormRef.clearValidate()
      })
      this.getAllUsccList()
    },
    // 回显文件大小
    calculateFileSizes() {
      const getFileSize = (file) => {
        // 根据实际文件对象结构获取大小
        return file.size || file.fileSize || 0
      }
      this.uploadedSizes = {
        win_the_bid: this.drawerForm.win_the_bid.reduce(
          (sum, file) => sum + getFileSize(file), 0
        ),
        contract: this.drawerForm.contract.reduce(
          (sum, file) => sum + getFileSize(file), 0
        )
      }
    },
    // 重置表单数据
    resetFormData() {
      this.drawerForm = {
        name: '',
        uscc: '',
        head_person: '',
        phone: '',
        address: '',
        status: 'enable',
        business_license: [],
        win_the_bid: [],
        contract: []
      }
      // 清空上传组件状态
      this.fileLists = [] // 清空图片上传组件
      if (this.$refs.winTheBidFileRef) {
        this.$refs.winTheBidFileRef.clearHandle()
      }
      if (this.$refs.contractFileRef) {
        this.$refs.contractFileRef.clearHandle()
      }
    },
    // 取消弹窗
    cancelHandle() {
      this.$refs.drawerFormRef.resetFields()
      this.resetFormData()
      this.drawerShow = false
    },
    // 保存方法前校验
    saveHandleBefore() {
      this.$refs.drawerFormRef.validate((valid) => {
        if (!valid) {
          this.$message.error('请确认表单内容填写是否正确')
          return
        }
        const isExistUsccAdmin = this.allUsccList.admin_data.find(item => item === this.drawerForm.uscc)
        const isExistUsccOrg = this.allUsccList.org_data.find(item => item === this.drawerForm.uscc)
        // 2、超管有---当前组织没有--正常提示--正常保存
        if (this.selectType === 'add' && isExistUsccAdmin && !isExistUsccOrg) {
          const h = this.$createElement;
          this.$msgbox({
            title: '消息',
            message: h('p', null, [
              h('span', null, '当前团餐公司信息已存在系统'),
              h('span', { style: 'color: #FF9B45' }, `（统一社会信用代码：${isExistUsccAdmin}）。`),
              h('span', null, '确认后修正公司名称并保存，如修正后名称有误，可联系客服调整。')
            ]),
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.saveHandleAfter()
          }).catch(() => {})
          // 3、超管有--当前组织有--异常提示（当前公司信息已存在）--不能保存
        } else if (this.selectType === 'add' && isExistUsccAdmin && isExistUsccOrg) {
          this.$message.error('当前公司信息已存在')
        } else {
          // 1、超管没有--当前组织也没有--没有提示--正常保存
          this.saveHandleAfter()
        }
      })
    },
    // 保存后处理
    saveHandleAfter() {
      // API配置映射
      const apiConfig = {
        add: {
          api: 'apiBackgroundFundSupervisionCustodyManagementAdd',
          successMsg: '新增成功'
        },
        edit: {
          api: 'apiBackgroundFundSupervisionCustodyManagementModify',
          successMsg: '编辑成功'
        }
      }
      // 如果是空字符串的值则设置为 null
      Object.keys(this.drawerForm).forEach(key => {
        if (this.drawerForm[key] === '') {
          this.drawerForm[key] = null
        }
      })
      // 过滤空值
      // const filteredForm = pickBy(this.drawerForm, value =>
      //   value !== '' &&
      //   value !== null &&
      //   value !== undefined
      // )
      // const params = {
      //   // id: this.selectType === 'add' ? undefined : this.selectId,
      //   ...filteredForm
      // }
      // 统一调用方法
      this.handleSubmit({
        api: apiConfig[this.selectType].api,
        params: this.drawerForm,
        successMsg: apiConfig[this.selectType].successMsg
      })
    },
    // 获取团餐公司所有信用代码
    getAllUsccList() {
      this.$apis.apiBackgroundFundSupervisionCustodyManagementGetUsccList().then(res => {
        if (res.code === 0) {
          this.allUsccList = res.data || []
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 统一提交处理
    handleSubmit({ api, params, successMsg }) {
      this.$apis[api](params).then(res => {
        if (res.code === 0) {
          this.$message.success(successMsg)
          this.handleSuccess()
        } else {
          this.$message.error(res.msg)
        }
      }).catch(error => {
        console.error('API调用失败:', error)
        this.$message.error('操作失败，请稍后重试')
      })
    },

    // 成功后的统一处理
    handleSuccess() {
      this.$refs.drawerFormRef.resetFields()
      this.resetFormData()
      this.drawerShow = false
      this.$emit('updateDataList')
    },
    // 图片上传成功
    uploadSuccess(res, file, fileList) {
      this.uploading = false
      if (res && res.code === 0) {
        console.log('file', file, fileList)
        let arr = []
        fileList.forEach(item => {
          arr.push(item.response.data.public_url)
        })
        this.fileLists = deepClone(fileList)
        this.drawerForm.business_license = deepClone(arr)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 图片上传前检测
    beforeImgUpload(file) {
      // 文件校验
      const fileType = ['jpeg', 'jpg', 'png', 'bmp']
      const isLt2M = file.size / 1024 / 1024 <= 20
      let result = file.type.split("/")[1]
      console.log(result);

      if (this.fileLists.length >= 5) {
        this.$message.error('最多上传5张图片')
        return false
      }
      if (!fileType.includes(result)) {
        this.$message.error('请上传正确的文件')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      this.uploading = true
    },

    // 删除图片
    beforeRemove(file) {
      const fileIndex = this.fileLists.findIndex(item => item.url === file.url)
      if (fileIndex > -1) {
        this.fileLists.splice(fileIndex, 1)
        this.drawerForm.business_license.splice(fileIndex, 1)
      }
    },

    // 文件上传成功回调
    getFileLists(fileLists, type) {
      this.drawerForm[type] = fileLists;
      // 计算当前类型的总大小
      this.uploadedSizes[type] = fileLists.reduce((sum, file) => {
        return sum + (file.size || 0);
      }, 0);
    },
    // 中标通知书上传前回调
    beforeUpload(file, type) {
      // 1. 校验文件类型
      let unUploadType = ['.bat', '.sh']
      if (unUploadType.includes(this.getSuffix(file.name))) {
        this.$message.error('请检查上传文件格式！')
        return false
      }
      // 2. 校验单个文件大小
      const isLt20M = (file.size / 1024 / 1024) <= 20
      console.log(file.size, 'file.size')
      console.log(isLt20M, 'isLt20M')
      if (!isLt20M) {
        this.$message.error('上传附件大小不能超过 20M')
        return false
      }
      // 校验当前类型的总大小
      const MAX_SIZE = 20 * 1024 * 1024 // 20MB
      const newTotalSize = this.uploadedSizes[type] + file.size;
      if (newTotalSize > MAX_SIZE) {
        this.$message.error(`${type === 'win_the_bid' ? '中标通知书' : '合同'}总大小不能超过20MB`)
        return false
      }
      return true
    },
    // 获取文件后缀名
    getSuffix(filename) {
      let pos = filename.lastIndexOf('.')
      let suffix = ''
      if (pos !== -1) {
        suffix = filename.substring(pos)
      }
      return suffix
    }
  }
}
</script>
<style scoped>
::v-deep .hide-upload .el-upload--picture-card {
  display: none !important;
}
</style>
