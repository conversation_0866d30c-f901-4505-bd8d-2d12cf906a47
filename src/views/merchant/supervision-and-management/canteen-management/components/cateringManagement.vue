<template>
  <div>
    <div class="table-header-box">
      <div class="header-left-title">配餐公司信息</div>
      <el-button type="primary" size="small" style="width: 100px;" @click="showDrawer('add')" v-permission="['background_fund_supervision.catering_company.add']">新 增</el-button>
    </div>
    <div class="table-content">
      <!-- table start -->
      <el-table
        v-loading="isLoading"
        :data="tableData"
        ref="tableData"
        style="width: 100%"
        stripe
        header-row-class-name="ps-table-header-row"
      >
        <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
          <template #phone="{ row }">
            <el-tooltip v-if="row.phone" class="item" effect="dark" :content="row.phone" placement="top">
              <div>{{ computedPhone(row.phone) }}</div>
            </el-tooltip>
            <span v-else>--</span>
          </template>
          <template #status="{ row, index }">
            <el-switch
              v-model="row.status"
              :active-value="'enable'"
              :inactive-value="'disable'"
              @change="changeStatusFun($event,row, index)"
              active-color="#ff9b45"
              inactive-color="#ffcda2">
            </el-switch>
          </template>
          <template #address="{ row }">
            <el-tooltip v-if="row.address" class="item" effect="dark" :content="row.address" placement="top">
              <div>{{ truncateText(row.address) }}</div>
            </el-tooltip>
            <span v-else>--</span>
          </template>
          <template #img="{ row }">
            <el-button type="text" size="small" class="ps-text" :disabled="!(row.business_license && row.business_license.length)"  @click="handleClick(row.business_license)">查看</el-button>
          </template>
          <template #win_the_bid="{ row }">
            <span>{{ row.win_the_bid && row.win_the_bid.length ? '已上传' : '未上传' }}</span>
          </template>
          <template #contract="{ row }">
            <span>{{ row.contract && row.contract.length ? '已上传' : '未上传' }}</span>
          </template>
          <template #update_time="{ row }">
            <span v-if="row.version > 1">{{ row.update_time }}</span>
            <span v-else>--</span>
          </template>
          <template #operation="{ row }">
            <!-- v-permission="['background_fund_supervision.publicity_info.modify_food_admin']" -->
            <el-button type="text" size="small" class="ps-text" @click="showDrawer('detail', row)">详情</el-button>
            <el-button type="text" size="small" class="ps-text" @click="showDrawer('edit', row)" v-permission="['background_fund_supervision.catering_company.modify']">编辑</el-button>
            <el-button type="text" size="small" class="ps-warn-text" @click="muldelete(row)" v-permission="['background_fund_supervision.catering_company.delete']">删除</el-button>
          </template>
        </table-column>
      </el-table>
      <!-- table end -->
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[5, 10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>

    <!-- 新增/编辑配餐公司弹窗 -->
    <add-and-edit-catering
      :show.sync="addAndEditDrawerShow"
      :selectType="selectType"
      :infoData="infoData"
      :title="drawerTitle"
      @updateDataList="getDataList" />

    <!-- 配餐公司详情弹窗 -->
    <side-and-meal-detail :show.sync="drawerShowDetail" :infoData="infoData" />

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
import { deepClone } from '@/utils/index'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import AddAndEditCatering from './AddAndEditCatering'
import SideAndMealDetail from './SideAndMealDetail'

export default {
  components: {
    ElImageViewer,
    AddAndEditCatering,
    SideAndMealDetail
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '公司名称', key: 'name', showTooltip: true },
        { label: '统一社会信用代码', key: 'uscc', width: 180 },
        { label: '负责人', key: 'head_person' },
        { label: '联系电话', key: 'phone', width: 150, type: 'slot', slotName: 'phone' },
        { label: '公司地址', key: 'address', width: 150, type: 'slot', slotName: 'address' },
        { label: '启用状态', key: 'status', type: 'slot', slotName: 'status' },
        { label: '经营执照', key: 'face_url', type: "slot", slotName: "img" },
        { label: '中标通知书', key: 'win_the_bid', type: "slot", slotName: "win_the_bid" },
        { label: '合同', key: 'contract', type: "slot", slotName: "contract" },
        { label: '创建时间', key: 'create_time', width: 100 },
        { label: '修改时间', key: 'update_time', width: 100, type: "slot", slotName: "update_time" },
        { label: '操作人', key: 'operator' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", width: "120", fixed: "right" }
      ],
      page: 1,
      pageSize: 10,
      totalCount: 0,
      selectType: '',
      showImagePreview: false,
      addAndEditDrawerShow: false,
      drawerShowDetail: false,
      previewList: [],
      infoData: {},
      drawerTitle: ''
    }
  },
  created() {
    this.getDataList()
  },
  computed: {
    computedPhone() {
      return d => {
        if (!d) return '--'
        return d.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
      }
    }
  },
  methods: {
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.getDataList()
    },
    // 查看图片
    handleClick(imageList) {
      this.previewList = imageList
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    // 关闭预览图片
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    // 获取数据
    getDataList() {
      this.isLoading = true
      let params = {
        page: this.page,
        page_size: this.pageSize
      }
      this.$apis.apiBackgroundFundSupervisionCateringCompanyList(params).then(res => {
        this.isLoading = false
        if (res.code === 0) {
          this.tableData = deepClone(res.data.results)
          this.totalCount = res.data.count
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 删除配餐公司
    async muldelete(row) {
      this.$confirm('确认删除配餐公司信息？删除后不可恢复，请谨慎操作。', '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn'
      })
        .then(async(e) => {
          const res = await this.$apis.apiBackgroundFundSupervisionCateringCompanyDelete({
            id: row.id
          })
          if (res.code === 0) {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch(e => {})
    },

    // 切换状态-配餐支持开启多个
    async changeStatusFun(e, row, index) {
      // const res = await this.$apis.apiBackgroundFundSupervisionCateringCompanyIsEnable()
      const params = {
        id: row.id,
        status: e
      }
      await this.changeStatusApi(params, row, index)
      // if (e === 'enable' && res.data) {
      //   await this.$confirm('已存在启用中的配餐公司，确认替换当前配餐公司？', '提示', {
      //     confirmButtonText: this.$t('dialog.confirm_btn'),
      //     cancelButtonText: this.$t('dialog.cancel_btn'),
      //     closeOnClickModal: false,
      //     customClass: 'ps-confirm',
      //     cancelButtonClass: 'ps-cancel-btn',
      //     confirmButtonClass: 'ps-btn',
      //     beforeClose: async (action, instance, done) => {
      //       if (action === 'confirm') {
      //         await this.changeStatusApi(params)
      //         done()
      //       } else {
      //         if (!instance.confirmButtonLoading) {
      //           this.tableData[index].status = row.status === 'enable' ? 'disable' : 'enable'
      //           done()
      //         }
      //       }
      //     }
      //   })
      //     .then(e => {})
      //     .catch(e => {})
      // } else {
      //   await this.changeStatusApi(params)
      // }
    },
    // 切换状态接口
    async changeStatusApi(params, row, index) {
      const res = await this.$apis.apiBackgroundFundSupervisionCateringCompanyModifyStatus(params)
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.getDataList()
      } else {
        this.$message.error(res.msg)
        this.tableData[index].status = row.status === 'enable' ? 'disable' : 'enable'
        // // 操作失败时恢复原状态
        // this.$set(this.tableData[index], 'status', row.status)
      }
    },
    // 打开添加配餐公司
    showDrawer(type, data) {
      this.selectType = type
      this.infoData = data ? deepClone(data) : {}
      this.drawerTitle = type === 'add' ? '新增配餐公司' : '编辑配餐公司'
      if (type === 'detail') {
        this.drawerShowDetail = true
      } else {
        this.addAndEditDrawerShow = true
      }
    },
    truncateText(text, maxLength = 7) {
      if (!text) return '--'
      return text.length > maxLength
        ? text.substring(0, maxLength) + '...'
        : text
    }
  }
}
</script>

<style lang="scss" scoped>
.table-header-box {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  &-title {
    height: 32px;
    line-height: 32px;
    margin-right: 10px;
    font-weight: 700;
  }
}
.upload-img-wrapper{
  &.hide-upload{
    .el-upload--picture-card{
      display: none;
    }
  }
  .el-upload--picture-card{
    border: none;
  }
  .el-upload-dragger{
    width: 194px;
    height: 114px;
  }
  .upload-img{
    width: 194px;
    height: 114px;
    img{
      max-width: 194px;
      max-height: 114px;
    }
  }
  .el-upload--picture-card {
    width: 194px;
    height: 114px;
    line-height: 114px;
  }
}
</style>
