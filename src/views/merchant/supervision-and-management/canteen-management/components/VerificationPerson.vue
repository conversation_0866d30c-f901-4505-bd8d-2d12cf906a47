<template>
  <div class="app-container">
    <el-form ref="verificationForm" :model="formData" :rules="rules" label-width="0px" v-loading="isLoading">
      <div class="verification-section">
        <div class="button-right">
          <el-button type="primary" size="mini" style="width: 80px;" @click="handleSave" v-loading="btnLoading">保存</el-button>
        </div>
        <div class="switch-row">
          <span class="ver-title">物资入库核验</span>
          <el-switch v-model="formData.materialEntryVerification"></el-switch>
        </div>

        <div v-if="formData.materialEntryVerification" class="verification-content">
          <div class="action-row">
            <span>核验人员</span>
            <el-button type="primary" size="mini" class="m-l-20" @click="openPersonDialog('entry')">添加</el-button>
          </div>

          <el-form-item prop="entryMinPersons">
            <div class="min-person-row">
              <span>核验人员至少</span>
              <el-input
                v-model.number="formData.entryMinPersons"
                size="mini"
                class="input-person"
              ></el-input>
              <span>人</span>
            </div>
          </el-form-item>

          <el-form-item prop="entryPersons">
            <el-table :data="formData.entryPersons" border stripe style="width: 100%" header-row-class-name="ps-table-header-row">
              <el-table-column prop="role_alias" label="角色名称" align="center"></el-table-column>
              <el-table-column prop="member_name" label="账号名" align="center"></el-table-column>
              <el-table-column prop="username" label="账号" align="center"></el-table-column>
              <el-table-column prop="mobile" label="手机号" align="center"></el-table-column>
              <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                  <el-button type="text" @click="handleDeletePerson(scope.row, 'entry')">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </div>
      </div>

      <div class="verification-section">
        <div class="switch-row">
          <span class="ver-title">物资收货核验</span>
          <el-switch v-model="formData.materialReceiptVerification"></el-switch>
        </div>

        <div v-if="formData.materialReceiptVerification" class="verification-content">
          <div class="action-row">
            <span>核验人员</span>
            <el-button type="primary" size="mini" @click="openPersonDialog('receipt')" class="m-l-10">添加</el-button>
          </div>

          <el-form-item prop="receiptMinPersons">
            <div class="min-person-row">
              <span>核验人员至少</span>
              <el-input
                v-model.number="formData.receiptMinPersons"
                size="mini"
                class="input-person"
              ></el-input>
              <span>人</span>
            </div>
          </el-form-item>

          <el-form-item prop="receiptPersons">
            <el-table :data="formData.receiptPersons" border stripe style="width: 100%" header-row-class-name="ps-table-header-row">
              <el-table-column prop="role_alias" label="角色名称" align="center"></el-table-column>
              <el-table-column prop="member_name" label="账号名" align="center"></el-table-column>
              <el-table-column prop="username" label="账号" align="center"></el-table-column>
              <el-table-column prop="mobile" label="手机号" align="center"></el-table-column>
              <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                  <el-button type="text" @click="handleDeletePerson(scope.row, 'receipt')">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </div>
      </div>
    </el-form>

    <!-- 添加人员对话框 -->
    <person-add-dialog
      :visible.sync="personDialogVisible"
      :selected-persons="currentSelectedPersons"
      :type="currentDialogType"
      @confirm="handlePersonConfirm"
      @cancel="personDialogVisible = false"
    />
  </div>
</template>

<script>
import { deepClone } from '@/utils'
import PersonAddDialog from './PersonAddDialog.vue'

export default {
  name: 'VerificationPerson',
  components: {
    PersonAddDialog
  },
  data() {
    // 自定义校验规则：验证整数且大于0
    const validatePositiveInteger = (rule, value, callback) => {
      const val = parseInt(value)
      if (isNaN(val) || val <= 0) {
        callback(new Error('请输入大于0的整数'))
      } else if (val !== Math.floor(val)) {
        callback(new Error('请输入整数'))
      } else {
        callback()
      }
    }

    // 自定义校验规则：验证入库核验人员数量不超过表格数据长度
    const validateEntryMinPersons = (rule, value, callback) => {
      if (!this.formData.materialEntryVerification) {
        callback()
        return
      }

      const val = parseInt(value)
      if (val > this.formData.entryPersons.length) {
        callback(new Error(`核验人员不能超过当前表格人数${this.formData.entryPersons.length}`))
      } else {
        callback()
      }
    }

    // 自定义校验规则：验证收货核验人员数量不超过表格数据长度
    const validateReceiptMinPersons = (rule, value, callback) => {
      if (!this.formData.materialReceiptVerification) {
        callback()
        return
      }

      const val = parseInt(value)
      if (val > this.formData.receiptPersons.length) {
        callback(new Error(`核验人员不能超过当前表格人数${this.formData.receiptPersons.length}`))
      } else {
        callback()
      }
    }

    // 自定义校验规则：验证入库核验人员表格不为空
    const validateEntryPersons = (rule, value, callback) => {
      if (!this.formData.materialEntryVerification) {
        callback()
        return
      }

      if (value.length === 0) {
        callback(new Error('物资入库核验需要至少添加一名核验人员'))
      } else {
        callback()
      }
    }

    // 自定义校验规则：验证收货核验人员表格不为空
    const validateReceiptPersons = (rule, value, callback) => {
      if (!this.formData.materialReceiptVerification) {
        callback()
        return
      }

      if (value.length === 0) {
        callback(new Error('物资收货核验需要至少添加一名核验人员'))
      } else {
        callback()
      }
    }

    return {
      formData: {
        materialEntryVerification: true,
        materialReceiptVerification: true,
        entryMinPersons: '',
        receiptMinPersons: '',
        entryPersons: [],
        receiptPersons: []
      },
      rules: {
        entryMinPersons: [
          { validator: validatePositiveInteger, trigger: 'blur' },
          { validator: validateEntryMinPersons, trigger: 'blur' }
        ],
        receiptMinPersons: [
          { validator: validatePositiveInteger, trigger: 'blur' },
          { validator: validateReceiptMinPersons, trigger: 'blur' }
        ],
        entryPersons: [
          { validator: validateEntryPersons, trigger: 'blur' }
        ],
        receiptPersons: [
          { validator: validateReceiptPersons, trigger: 'blur' }
        ]
      },
      // 人员选择对话框
      personDialogVisible: false,
      currentDialogType: 'entry', // entry 或 receipt
      currentSelectedPersons: [],
      isLoading: false, // 加载中
      btnLoading: false, // 按钮加载中
      id: '' // 当前ID
    }
  },
  created() {
    this.getSetting()
  },
  watch: {
    'formData.materialEntryVerification'() {
      // 当开关状态变化时，重新验证表单
      this.$nextTick(() => {
        this.$refs.verificationForm && this.$refs.verificationForm.validate()
      })
    },
    'formData.materialReceiptVerification'() {
      // 当开关状态变化时，重新验证表单
      this.$nextTick(() => {
        this.$refs.verificationForm && this.$refs.verificationForm.validate()
      })
    },
    'formData.entryPersons': {
      handler() {
        // 当人员列表变化时，重新验证最小人数
        this.$nextTick(() => {
          this.$refs.verificationForm && this.$refs.verificationForm.validateField('entryMinPersons')
        })
      },
      deep: true
    },
    'formData.receiptPersons': {
      handler() {
        // 当人员列表变化时，重新验证最小人数
        this.$nextTick(() => {
          this.$refs.verificationForm && this.$refs.verificationForm.validateField('receiptMinPersons')
        })
      },
      deep: true
    }
  },
  methods: {
    // 打开人员选择对话框
    openPersonDialog(type) {
      this.currentDialogType = type
      // 深拷贝已选择的人员，确保不会直接修改原数据
      this.currentSelectedPersons = type === 'entry'
        ? deepClone(this.formData.entryPersons)
        : deepClone(this.formData.receiptPersons)
      this.personDialogVisible = true
    },

    // 处理人员选择确认
    handlePersonConfirm(selectedPersons, type) {
      if (type === 'entry') {
        // 更新入库核验人员
        this.formData.entryPersons = deepClone(selectedPersons)
      } else {
        // 更新收货核验人员
        this.formData.receiptPersons = deepClone(selectedPersons)
      }
    },

    handleSave() {
      this.$refs.verificationForm.validate(valid => {
        if (valid) {
          // 所有校验通过，执行保存操作
          this.saveSetting()
          console.log('保存的数据:', this.formData)
        } else {
          this.$message.error('表单验证失败，请检查输入')
          return false
        }
      })
    },

    handleDeletePerson(row, type) {
      // 删除核验人员的逻辑
      if (type === 'entry') {
        const index = this.formData.entryPersons.findIndex(item => item.id === row.id)
        if (index !== -1) {
          this.formData.entryPersons.splice(index, 1)
        }
      } else {
        const index = this.formData.receiptPersons.findIndex(item => item.id === row.id)
        if (index !== -1) {
          this.formData.receiptPersons.splice(index, 1)
        }
      }
    },
    // 获取核验设置
    async getSetting() {
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpDrpFaceVerificationListPost())
      this.isLoading = false
      if (err) {
        this.$message.error(err.message || '获取配置失败')
        return
      }
      if (res && res.code === 0) {
        const data = res.data || {}
        const results = data.results || {}
        const accountInfo = results.account_info || {}
        this.id = results.id
        console.log('accountInfo:', accountInfo)
        if (typeof results === 'object' && Object.keys(results).length > 0) {
          let formData = deepClone(this.formData)
          formData = {
            materialEntryVerification: results.entry_verification || false,
            materialReceiptVerification: results.delivery_verification || false,
            entryMinPersons: results.entry_headcount,
            receiptMinPersons: results.delivery_headcount,
            entryPersons: accountInfo.entry_accounts || [],
            receiptPersons: accountInfo.delivery_accounts || []
          }
          this.formData = deepClone(formData)
        }
      } else {
        this.$message.error(res.msg || '获取配置失败')
      }
    },
    // 保存成功
    async saveSetting() {
      this.btnLoading = true
      let params = {
        entry_verification: this.formData.materialEntryVerification,
        entry_headcount: this.formData.materialEntryVerification ? this.formData.entryMinPersons : 0,
        entry_accounts: this.formData.materialEntryVerification ? this.formData.entryPersons.map(v => v.id) : [],
        delivery_verification: this.formData.materialReceiptVerification,
        delivery_headcount: this.formData.materialReceiptVerification ? this.formData.receiptMinPersons : 0,
        delivery_accounts: this.formData.materialReceiptVerification ? this.formData.receiptPersons.map(v => v.id) : []
      }
      if (this.id) {
        params.id = this.id
      }
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpDrpFaceVerificationAddPost(params))
      this.btnLoading = false
      if (err) {
        this.$message.error(err.message || '保存配置失败')
        return
      }
      if (res && res.code === 0) {
        this.$message.success('保存配置成功')
      } else {
        this.$message.error(res.msg || '保存配置失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.button-right {
  display: flex;
  justify-content: flex-end;
}
.input-person{
  width: 100px;
}

.verification-section {
  margin-bottom: 30px;
}

.switch-row {
  display: flex;
  align-items: center;

  span {
    margin-right: 10px;
  }
}

.verification-content {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
}

.action-row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 15px;
}

.min-person-row {
  display: flex;
  align-items: center;

  span {
    margin-right: 10px;

    &:last-child {
      margin-left: 5px;
    }
  }

  .el-input-number {
    width: 120px;
  }
}
</style>
