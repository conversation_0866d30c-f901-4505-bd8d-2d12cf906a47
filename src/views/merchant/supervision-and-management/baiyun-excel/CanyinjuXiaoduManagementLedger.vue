<template>
  <div class="container-wrapper">
    <div class="dining-utensils-disinfection">
      <h2 class="table-title">餐堂餐饮具消毒记录本</h2>

      <el-table
        :data="displayData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <!-- 日期列 -->
        <el-table-column label="日期" align="center" width="260">
          <template slot-scope="scope">
            <div class="date-selector">
              <el-select v-model="scope.row.year" placeholder="年" size="mini" style="width: 80px">
                <el-option v-for="i in years" :key="`row-year-${i}`" :label="`${i}年`" :value="i"></el-option>
              </el-select>
              <el-select v-model="scope.row.month" placeholder="月" size="mini" style="width: 70px">
                <el-option v-for="i in 12" :key="`row-month-${i}`" :label="`${i}月`" :value="i"></el-option>
              </el-select>
              <el-select v-model="scope.row.day" placeholder="日" size="mini" style="width: 70px">
                <el-option v-for="i in 31" :key="`row-day-${i}`" :label="`${i}日`" :value="i"></el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>

        <!-- 餐次列 -->
        <el-table-column label="餐次" align="center" width="100">
          <template slot-scope="scope">
            <el-select v-model="scope.row.mealTime" placeholder="选择餐次" size="mini" style="width: 80px">
              <el-option label="早" value="早"></el-option>
              <el-option label="午" value="午"></el-option>
              <el-option label="晚" value="晚"></el-option>
            </el-select>
          </template>
        </el-table-column>

        <!-- 消毒物品列 -->
        <el-table-column label="消毒物品" align="center">
          <template slot-scope="scope">
            <el-select v-model="scope.row.item" placeholder="选择物品" size="mini">
              <el-option v-for="item in utensils" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </template>
        </el-table-column>

        <!-- 消毒时间列 -->
        <el-table-column label="消毒时间" align="center" width="180">
          <template slot-scope="scope">
            <div class="time-selector">
              <el-select v-model="scope.row.disinfectionHour" placeholder="时" size="mini" style="width: 80px">
                <el-option v-for="i in 24" :key="`hour-${i-1}`" :label="`${i-1}时`" :value="i-1"></el-option>
              </el-select>
              <el-select v-model="scope.row.disinfectionMinute" placeholder="分" size="mini" style="width: 80px">
                <el-option v-for="i in 12" :key="`minute-${i*5-5}`" :label="`${i*5-5}分`" :value="i*5-5"></el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>

        <!-- 消毒方式列 -->
        <el-table-column label="消毒方式" align="center">
          <!-- 红外线高温消毒 -->
          <el-table-column label="红外线高温消毒" align="center" >
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.infrared"></el-checkbox>
            </template>
          </el-table-column>

          <!-- 紫外线臭氧消毒 -->
          <el-table-column label="紫外线臭氧消毒" align="center" >
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.ultraviolet"></el-checkbox>
            </template>
          </el-table-column>

          <!-- 消毒粉泡洗/擦拭 -->
          <el-table-column label="消毒粉泡洗/擦拭(10g/2400ml)" align="center" >
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.powder"></el-checkbox>
            </template>
          </el-table-column>

          <!-- 蒸气消毒20分钟以上 -->
          <el-table-column label="蒸气消毒20分钟以上" align="center" >
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.steam"></el-checkbox>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 操作者 -->
        <el-table-column label="操作者" align="center" width="100">
          <template slot-scope="scope">
            <el-input v-model="scope.row.operator" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CanyinjuXiaoduManagementLedger',
  data() {
    // Generate current year and next 5 years
    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: 6 }, (_, i) => currentYear + i - 3);

    // List of utensils for dropdown
    const utensils = ["餐盘", "碗", "勺", "筷子", "刀", "叉", "其他饮具"];

    return {
      currentPage: 1,
      pageSize: 40,
      years: years,
      utensils: utensils,
      tableData: this.generateTableData(currentYear)
    };
  },
  computed: {
    displayData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableData.slice(start, end);
    }
  },
  methods: {
    generateTableData(defaultYear) {
      const data = [];
      const utensils = ["餐盘", "碗", "勺", "其他饮具"];
      const mealTime = "午";

      // 生成12天的数据
      for (let day = 1; day <= 12; day++) {
        // 每天添加所有餐具
        utensils.forEach(item => {
          data.push({
            year: defaultYear,
            month: null,
            day: day,
            mealTime: mealTime,
            item: item,
            disinfectionHour: null,
            disinfectionMinute: null,
            infrared: false,
            ultraviolet: false,
            powder: false,
            steam: false,
            operator: ''
          });
        });
      }

      return data;
    },
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    cellStyle() {
      return {
        padding: '5px',
        fontSize: '14px',
        textAlign: 'center'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 0',
        textAlign: 'center'
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.dining-utensils-disinfection {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  .date-selector,
  .time-selector {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #409EFF;
    border-color: #409EFF;
  }

  ::v-deep .el-table .cell {
    padding: 5px;
  }

  ::v-deep .el-input__inner {
    border: none;
    border-bottom: 1px solid #DCDFE6;
    border-radius: 0;
    text-align: center;
  }

  ::v-deep .el-select .el-input__inner {
    padding: 0 5px;
    height: 28px;
    line-height: 28px;
  }

  ::v-deep .el-select-dropdown__item {
    padding: 0 15px;
    text-align: center;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
