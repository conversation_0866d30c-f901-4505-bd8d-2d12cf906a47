<template>
  <div class="container-wrapper">
    <div class="disinfection-record">
      <h2 class="table-title">
        <span>厨房餐厅消毒记录表 </span>
        <span class="year-month">2025年6月</span>
      </h2>

      <el-table
        :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column label="日期" prop="day" align="center" width="60"></el-table-column>

        <el-table-column label="配制记录（10g兑2400 ml水）" align="center">
          <el-table-column label="消毒剂名称" prop="disinfectantName" align="center" width="120"></el-table-column>
          <el-table-column label="用量" align="center" width="100">
            <template slot-scope="scope">
              <div>{{ scope.row.amount || '' }} g</div>
            </template>
          </el-table-column>
          <el-table-column label="用水量" align="center" width="100">
            <template slot-scope="scope">
              <div>{{ scope.row.waterAmount || '' }} ml</div>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="消毒记录" align="center">
          <el-table-column label="用途" align="center">
            <template slot-scope="">
              <div class="purpose-text">门、门把手、窗户、窗户把手、桌、椅、工作台、货架、空调、地面、墙壁、垃圾桶、加工设备表面、不锈钢板、照明开关、筷车、餐车、风幕等</div>
            </template>
          </el-table-column>
          <el-table-column label="确认打勾" align="center" width="80">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.confirmed"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="消毒人" prop="operator" align="center" width="100"></el-table-column>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[15, 31]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChufangCantingXiaoduManagementLedger',
  data() {
    // Generate sample data
    const generateData = () => {
      const data = [];

      for (let i = 1; i <= 31; i++) {
        data.push({
          id: i,
          day: i + '日',
          disinfectantName: '',
          amount: '',
          waterAmount: '',
          confirmed: false,
          operator: ''
        });
      }
      return data;
    };

    return {
      tableData: generateData(),
      currentPage: 1,
      pageSize: 15,
      totalItems: 31
    };
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    cellStyle() {
      return {
        padding: '5px',
        fontSize: '14px'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 0'
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.disinfection-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;

    .year-month {
      margin-left: 10px;
    }
  }

  .purpose-text {
    font-size: 12px;
    line-height: 1.4;
    text-align: left;
    padding: 0 5px;
  }

  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #409EFF;
    border-color: #409EFF;
  }

  ::v-deep .el-table .cell {
    word-break: break-word;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
