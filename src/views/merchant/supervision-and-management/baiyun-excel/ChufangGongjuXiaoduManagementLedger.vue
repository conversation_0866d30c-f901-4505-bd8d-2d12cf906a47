<template>
  <div class="container-wrapper">
    <div class="kitchen-tools-disinfection">
      <h2 class="table-title">
        厨房工具消毒登记(
        <el-select v-model="year" placeholder="年" size="small" style="width: 110px">
          <el-option v-for="i in years" :key="`year-${i}`" :label="`${i}年`" :value="i"></el-option>
        </el-select>
        <el-select v-model="month" placeholder="月" size="small" style="width: 80px">
          <el-option v-for="i in 12" :key="`month-${i}`" :label="`${i}月`" :value="i"></el-option>
        </el-select>
        )
      </h2>

      <el-table :data="tableData" border style="width: 100%" :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <!-- 日期列 -->
        <el-table-column label="日期" prop="date" align="center" width="260">
          <template slot-scope="scope">
            <div class="date-selector">
              <el-select v-model="scope.row.year" placeholder="年" size="mini" style="width: 80px">
                <el-option v-for="i in years" :key="`row-year-${i}`" :label="`${i}年`" :value="i"></el-option>
              </el-select>
              <el-select v-model="scope.row.month" placeholder="月" size="mini" style="width: 70px">
                <el-option v-for="i in 12" :key="`row-month-${i}`" :label="`${i}月`" :value="i"></el-option>
              </el-select>
              <el-select v-model="scope.row.day" placeholder="日" size="mini" style="width: 70px">
                <el-option v-for="i in 31" :key="`row-day-${i}`" :label="`${i}日`" :value="i"></el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>

        <!-- 名称列 -->
        <el-table-column label="名称" prop="name" align="center">
          <template slot-scope="scope">
            <div>{{ scope.row.name }}</div>
          </template>
        </el-table-column>

        <!-- 消毒方式列 -->
        <el-table-column label="消毒方式" align="center">
          <!-- 红外线消毒 -->
          <el-table-column label="红外线消毒" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.infrared"></el-checkbox>
            </template>
          </el-table-column>

          <!-- 蒸汽消毒 -->
          <el-table-column label="蒸汽消毒(15分钟以上)" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.steam"></el-checkbox>
            </template>
          </el-table-column>

          <!-- 消毒粉泡洗/擦拭 -->
          <el-table-column label="消毒粉泡洗/擦拭(10g/2400ml)" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.powder"></el-checkbox>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 操作人签名 -->
        <el-table-column label="操作人签名" prop="operator" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.operator" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>

        <!-- 检查人签名 -->
        <el-table-column label="检查人签名" prop="inspector" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.inspector" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
          :page-sizes="[12, 24, 36]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChufangGongjuXiaoduManagementLedger',
  data() {
    // Generate current year and next 5 years
    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: 6 }, (_, i) => currentYear + i - 3);

    return {
      year: currentYear,
      month: new Date().getMonth() + 1,
      years: years,
      currentPage: 1,
      pageSize: 12,
      tableData: this.generateTableData(currentYear, new Date().getMonth() + 1)
    };
  },
  watch: {
    year() {
      this.updateTableData();
    },
    month() {
      this.updateTableData();
    }
  },
  methods: {
    generateTableData(defaultYear, defaultMonth) {
      const kitchenItems = [
        "装菜盘", "生肉盘", "熟肉盘", "半成品专用盘", "砧板",
        "蒸饭盘", "切肉机", "保温柜", "留样盒",
        "配送专用保温收纳", "配送专用保温餐车"
      ];

      // Generate 5 days of entries
      const data = [];
      for (let day = 1; day <= 5; day++) {
        // For each day, add all kitchen items
        kitchenItems.forEach(item => {
          data.push({
            year: defaultYear,
            month: defaultMonth,
            day: day,
            name: item,
            infrared: false,
            steam: false,
            powder: false,
            operator: '',
            inspector: ''
          });
        });
      }

      return data;
    },
    updateTableData() {
      // Keep existing data for items but update the dates
      const updatedData = [...this.tableData];

      let dayCounter = 1;
      let itemCounter = 0;
      const kitchenItemCount = 11; // Number of kitchen items per day

      updatedData.forEach((item, index) => {
        // Reset day counter when we've gone through all items for a day
        if (itemCounter >= kitchenItemCount) {
          itemCounter = 0;
          dayCounter++;
        }

        item.year = this.year;
        item.month = this.month;
        item.day = dayCounter;

        itemCounter++;
      });

      this.tableData = updatedData;
    },
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    cellStyle() {
      return {
        padding: '5px',
        fontSize: '14px'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 0'
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.kitchen-tools-disinfection {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;

    .el-select {
      margin: 0 5px;
      vertical-align: middle;
    }
  }

  .date-selector {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #409EFF;
    border-color: #409EFF;
  }

  ::v-deep .el-table .cell {
    padding: 5px;
  }

  ::v-deep .el-input__inner {
    border: none;
    border-bottom: 1px solid #DCDFE6;
    border-radius: 0;
    text-align: center;
  }

  ::v-deep .el-select .el-input__inner {
    padding: 0 5px;
    height: 28px;
    line-height: 28px;
  }

  ::v-deep .el-select-dropdown__item {
    padding: 0 15px;
    text-align: center;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
