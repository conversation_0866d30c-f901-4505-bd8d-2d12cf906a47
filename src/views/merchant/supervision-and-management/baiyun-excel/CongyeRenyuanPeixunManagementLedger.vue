<template>
  <div class="container-wrapper">
    <div class="training-record">
      <h2 class="table-title">从业人员食品安全培训记录</h2>

      <el-table
        :data="[trainingData]"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <!-- 第一行 -->
        <el-table-column label="主讲人" align="center" width="150">
          <template>
            <el-input v-model="trainingData.lecturer" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>

        <el-table-column label="" align="center">
          <template>
            <div></div>
          </template>
        </el-table-column>

        <el-table-column label="培训日期" align="center" width="150">
          <template>
            <el-date-picker
              v-model="trainingData.trainingDate"
              type="date"
              placeholder="选择日期"
              size="mini"
              style="width: 120px;">
            </el-date-picker>
          </template>
        </el-table-column>

        <el-table-column label="" align="center">
          <template>
            <div></div>
          </template>
        </el-table-column>

        <el-table-column label="培训时长" align="center" width="150">
          <template>
            <el-input v-model="trainingData.trainingDuration" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>
      </el-table>

      <!-- 第二行 -->
      <el-table
        :data="[{}]"
        border
        style="width: 100%; margin-top: -1px;"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <el-table-column label="培训主题" align="center" width="150" row-key="theme">
          <template>
            <div class="vertical-center">培训主题</div>
          </template>
        </el-table-column>

        <el-table-column label="" align="center">
          <template>
            <div class="checkbox-group">
              <div><el-checkbox v-model="trainingData.topics.laws">餐饮相关的食品安全的法律法规</el-checkbox></div>
              <div><el-checkbox v-model="trainingData.topics.basicKnowledge">基础知识</el-checkbox></div>
              <div><el-checkbox v-model="trainingData.topics.management">食品安全管理制度</el-checkbox></div>
              <div><el-checkbox v-model="trainingData.topics.process">加工制作规程</el-checkbox></div>
              <div class="other-option">
                <el-checkbox v-model="trainingData.topics.other">其他:</el-checkbox>
                <el-input
                  v-if="trainingData.topics.other"
                  v-model="trainingData.topics.otherText"
                  size="mini"
                  placeholder=""
                  class="other-input">
                </el-input>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="培训方式" align="center" width="150">
          <template>
            <div class="vertical-center">培训方式</div>
          </template>
        </el-table-column>

        <el-table-column label="" align="center">
          <template>
            <div class="checkbox-group">
              <div><el-checkbox v-model="trainingData.methods.lecture">专题讲座</el-checkbox></div>
              <div><el-checkbox v-model="trainingData.methods.practice">实际操作</el-checkbox></div>
              <div><el-checkbox v-model="trainingData.methods.demo">现场演示</el-checkbox></div>
              <div class="other-option">
                <el-checkbox v-model="trainingData.methods.other">其他:</el-checkbox>
                <el-input
                  v-if="trainingData.methods.other"
                  v-model="trainingData.methods.otherText"
                  size="mini"
                  placeholder=""
                  class="other-input">
                </el-input>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 第三行 - 培训主题2 -->
      <el-table
        :data="[{}]"
        border
        style="width: 100%; margin-top: -1px;"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <el-table-column label="培训主题" align="center" width="150">
          <template>
            <div class="vertical-center">培训主题</div>
          </template>
        </el-table-column>

        <el-table-column label="" align="center">
          <template>
            <el-input
              type="textarea"
              :rows="2"
              v-model="trainingData.additionalTheme"
              placeholder="">
            </el-input>
          </template>
        </el-table-column>
      </el-table>

      <!-- 第四行 - 培训内容 -->
      <el-table
        :data="[{}]"
        border
        style="width: 100%; margin-top: -1px;"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <el-table-column label="培训内容" align="center" width="150">
          <template>
            <div class="vertical-center">培训内容</div>
          </template>
        </el-table-column>

        <el-table-column label="" align="center">
          <template>
            <el-input
              type="textarea"
              :rows="5"
              v-model="trainingData.content"
              placeholder="">
            </el-input>
          </template>
        </el-table-column>
      </el-table>

      <!-- 第五行 - 参会人员 -->
      <el-table
        :data="[{}]"
        border
        style="width: 100%; margin-top: -1px;"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <el-table-column label="参会人员" align="center" width="150">
          <template>
            <div class="vertical-center">参会人员</div>
          </template>
        </el-table-column>

        <el-table-column label="" align="center">
          <template>
            <el-input
              type="textarea"
              :rows="3"
              v-model="trainingData.attendees"
              placeholder="">
            </el-input>
          </template>
        </el-table-column>

        <el-table-column label="签到" align="center" width="150">
          <template>
            <div class="vertical-center">签到</div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 第六行 - 参加培训记录照 -->
      <el-table
        :data="[{}]"
        border
        style="width: 100%; margin-top: -1px;"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <el-table-column label="参加培训记录照" align="center" width="150">
          <template>
            <div class="vertical-center">参加培训记录照</div>
          </template>
        </el-table-column>

        <el-table-column label="" align="center">
          <template>
            <div class="photo-upload-area">
              <div class="upload-text">附上照片</div>
              <el-upload
                action="#"
                list-type="picture-card"
                :auto-upload="false"
                :on-change="handlePhotoChange">
                <i class="el-icon-plus"></i>
              </el-upload>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 第七行 - 考核方式 -->
      <el-table
        :data="[{}]"
        border
        style="width: 100%; margin-top: -1px;"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <el-table-column label="考核方式" align="center" width="150">
          <template>
            <div class="vertical-center">考核方式</div>
          </template>
        </el-table-column>

        <el-table-column label="" align="center">
          <template>
            <div class="checkbox-group horizontal">
              <el-checkbox v-model="trainingData.assessment.interview">询问</el-checkbox>
              <el-checkbox v-model="trainingData.assessment.exam">答题</el-checkbox>
              <div class="other-option horizontal">
                <el-checkbox v-model="trainingData.assessment.other">其他:</el-checkbox>
                <el-input
                  v-if="trainingData.assessment.other"
                  v-model="trainingData.assessment.otherText"
                  size="mini"
                  placeholder=""
                  class="other-input-small">
                </el-input>
              </div>
              <el-checkbox v-model="trainingData.assessment.practice">观察实际操作</el-checkbox>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="评估结果" align="center" width="150">
          <template>
            <div class="vertical-center">评估结果</div>
          </template>
        </el-table-column>

        <el-table-column label="" align="center">
          <template>
            <div class="checkbox-group horizontal">
              <el-checkbox v-model="trainingData.result.pass">合格</el-checkbox>
              <el-checkbox v-model="trainingData.result.fail">不合格，重新培训</el-checkbox>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 第八行 - 记录人 -->
      <el-table
        :data="[{}]"
        border
        style="width: 100%; margin-top: -1px;"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <el-table-column label="记录人" align="center" width="150">
          <template>
            <div class="vertical-center">记录人</div>
          </template>
        </el-table-column>

        <el-table-column label="" align="center">
          <template>
            <el-input v-model="trainingData.recorder" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>

        <el-table-column label="审核人" align="center" width="150">
          <template>
            <div class="vertical-center">审核人</div>
          </template>
        </el-table-column>

        <el-table-column label="" align="center">
          <template>
            <el-input v-model="trainingData.reviewer" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>
      </el-table>

      <div class="button-row">
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="resetForm">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CongyeRenyuanPeixunManagementLedger',
  data() {
    return {
      trainingData: {
        lecturer: '',
        trainingDate: '',
        trainingDuration: '',
        topics: {
          laws: false,
          basicKnowledge: false,
          management: false,
          process: false,
          other: false,
          otherText: ''
        },
        methods: {
          lecture: false,
          practice: false,
          demo: false,
          other: false,
          otherText: ''
        },
        additionalTheme: '',
        content: '',
        attendees: '',
        photos: [],
        assessment: {
          interview: false,
          exam: false,
          practice: false,
          other: false,
          otherText: ''
        },
        result: {
          pass: false,
          fail: false
        },
        recorder: '',
        reviewer: ''
      }
    };
  },
  methods: {
    submitForm() {
      // 保存表单数据的逻辑
      console.log('表单数据:', this.trainingData);
      this.$message.success('保存成功');
    },
    resetForm() {
      // 重置表单数据
      this.trainingData = {
        lecturer: '',
        trainingDate: '',
        trainingDuration: '',
        topics: {
          laws: false,
          basicKnowledge: false,
          management: false,
          process: false,
          other: false,
          otherText: ''
        },
        methods: {
          lecture: false,
          practice: false,
          demo: false,
          other: false,
          otherText: ''
        },
        additionalTheme: '',
        content: '',
        attendees: '',
        photos: [],
        assessment: {
          interview: false,
          exam: false,
          practice: false,
          other: false,
          otherText: ''
        },
        result: {
          pass: false,
          fail: false
        },
        recorder: '',
        reviewer: ''
      };
      this.$message.info('已重置表单');
    },
    handlePhotoChange(file) {
      // 处理照片上传
      this.trainingData.photos.push(file.raw);
    },
    cellStyle() {
      return {
        padding: '5px',
        fontSize: '14px'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 0'
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.training-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  ::v-deep .el-table .cell {
    padding: 5px;
  }

  ::v-deep .el-input__inner {
    border: none;
    border-bottom: 1px solid #DCDFE6;
    border-radius: 0;
  }

  .vertical-center {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-weight: bold;
  }

  .checkbox-group {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 5px 10px;
    text-align: left;

    &.horizontal {
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-around;
      align-items: center;
    }

    div {
      margin: 5px 0;
    }
  }

  .other-option {
    display: flex;
    align-items: center;

    &.horizontal {
      margin: 0 10px;
    }
  }

  .other-input {
    width: 180px;
    margin-left: 5px;
  }

  .other-input-small {
    width: 100px;
    margin-left: 5px;
  }

  .photo-upload-area {
    padding: 20px;

    .upload-text {
      color: #f56c6c;
      margin-bottom: 10px;
      font-weight: bold;
    }
  }

  .button-row {
    margin-top: 20px;
    text-align: center;
  }
}
</style>
