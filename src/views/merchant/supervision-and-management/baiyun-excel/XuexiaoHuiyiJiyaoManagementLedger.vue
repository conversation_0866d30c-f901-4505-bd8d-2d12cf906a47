<template>
  <div class="container-wrapper">
    <div class="meeting-minutes-wrapper">
      <h2 class="meeting-title">学校食堂每月食品安全调度会议纪要</h2>

      <!-- 会议基本信息 -->
      <div class="meeting-info">
        <div class="info-row">
          <div class="info-item">
            <span class="label">记录编号：</span>
            <el-input v-model="recordNumber" size="small" style="width: 150px;"></el-input>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item meeting-name">
            <span class="label">会议名称</span>
            <el-date-picker
              v-model="meetingDate"
              type="month"
              placeholder="选择年月"
              format="yyyy 年 MM 月"
              value-format="yyyy-MM"
              size="small"
              style="width: 150px;">
            </el-date-picker>
          </div>
          <div class="info-item">
            <span class="label">主持人<br>(校长/法人)</span>
            <el-input v-model="chairperson" size="small" style="width: 150px;"></el-input>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">会议日期</span>
            <div class="date-time">
              <el-date-picker
                v-model="meetingFullDate"
                type="date"
                placeholder="选择日期"
                format="yyyy 年 MM 月 dd 日"
                value-format="yyyy-MM-dd"
                size="small"
                style="width: 150px;">
              </el-date-picker>
              <el-time-picker
                v-model="startTime"
                format="HH:mm"
                placeholder="开始时间"
                size="small"
                style="width: 100px; margin: 0 5px;">
              </el-time-picker>
              <span>—</span>
              <el-time-picker
                v-model="endTime"
                format="HH:mm"
                placeholder="结束时间"
                size="small"
                style="width: 100px; margin: 0 5px;">
              </el-time-picker>
            </div>
          </div>
          <div class="info-item">
            <span class="label">会议地点</span>
            <el-input v-model="meetingLocation" size="small" style="width: 150px;"></el-input>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item full-width">
            <span class="label">出席人员<br>(包括且不限于食品安全总监、食品安全员、食品监督员)</span>
            <el-input v-model="attendees" type="textarea" :rows="2"></el-input>
          </div>
        </div>
      </div>

      <!-- 会议内容记录 -->
      <div class="meeting-content">
        <h3 class="content-title">会议内容记录：</h3>

        <!-- 第一部分：食品安全管理工作情况汇报 -->
        <div class="content-section">
          <h4 class="section-title">一、食品安全管理工作情况汇报</h4>

          <div class="subsection">
            <h5 class="subsection-title">（一）"互联网+明厨亮灶"智慧系统发现的问题及整改落实情况</h5>
            <el-input v-model="internetIssues" type="textarea" :rows="3" placeholder="请填写互联网+明厨亮灶智慧系统发现的问题及整改落实情况..."></el-input>
          </div>

          <div class="subsection">
            <h5 class="subsection-title">（二）食品原料验收情况：</h5>
            <div class="material-stats">
              <div class="stats-row">
                <span>原料：本月到货总批数</span>
                <el-input-number v-model="totalBatches" :min="0" size="small" controls-position="right"></el-input-number>
                <span>批次，合格</span>
                <el-input-number v-model="qualifiedBatches" :min="0" size="small" controls-position="right"></el-input-number>
                <span>批次，不合格</span>
                <el-input-number v-model="unqualifiedBatches" :min="0" size="small" controls-position="right"></el-input-number>
                <span>批次，到货合格率</span>
                <el-input-number v-model="qualificationRate" :min="0" :max="100" size="small" controls-position="right"></el-input-number>
                <span>%。</span>
              </div>
              <div class="stats-row">
                <span>不合格原料为：</span>
                <el-input v-model="unqualifiedMaterials" size="small" style="width: 150px;"></el-input>
                <span>，不合格原因：</span>
                <el-input v-model="unqualifiedReasons" size="small" style="width: 150px;"></el-input>
              </div>
              <div class="stats-row">
                <span>对不合格原料采取的处理措施：</span>
                <el-input v-model="handlingMeasures" size="small" style="width: 300px;"></el-input>
              </div>
            </div>
          </div>

          <div class="subsection">
            <h5 class="subsection-title">（三）学生及家长投诉处理情况（学校食堂或供餐单位当月接收和处理的质量投诉）</h5>
            <div class="complaint-stats">
              <div class="stats-row">
                <span>本月质量投诉起数为</span>
                <el-input-number v-model="totalComplaints" :min="0" size="small" controls-position="right"></el-input-number>
                <span>起，有效投诉</span>
                <el-input-number v-model="validComplaints" :min="0" size="small" controls-position="right"></el-input-number>
                <span>起，无效投诉</span>
                <el-input-number v-model="invalidComplaints" :min="0" size="small" controls-position="right"></el-input-number>
                <span>起。</span>
              </div>
              <div class="stats-row">
                <span>投诉产品主要为：</span>
                <el-input v-model="complainedProducts" size="small" style="width: 150px;"></el-input>
                <span>；投诉主要原因为：</span>
                <el-input v-model="complaintReasons" size="small" style="width: 150px;"></el-input>
              </div>
              <div class="stats-row">
                <span>采取的主要改进措施为：</span>
                <el-input v-model="improvementMeasures" size="small" style="width: 300px;"></el-input>
              </div>
            </div>
          </div>

          <div class="subsection">
            <h5 class="subsection-title">（四）供应商管理（对供应商审核及管理情况）</h5>
            <div class="supplier-management">
              <span>本月对供应商审核情况：</span>
              <el-input v-model="supplierAudit" type="textarea" :rows="2" placeholder="请填写本月对供应商审核情况..."></el-input>
            </div>
          </div>

          <div class="subsection">
            <h5 class="subsection-title">（五）从业人员食品安全相关培训情况</h5>
            <div class="training-stats">
              <div class="stats-row">
                <span>本月开展员工食品安全相关培训情况：本月开展食品安全培训</span>
                <el-input-number v-model="trainingTimes" :min="0" size="small" controls-position="right"></el-input-number>
                <span>次，合计</span>
                <el-input-number v-model="trainingHours" :min="0" size="small" controls-position="right"></el-input-number>
                <span>学时，参加培训人员合计</span>
                <el-input-number v-model="trainingParticipants" :min="0" size="small" controls-position="right"></el-input-number>
                <span>人。</span>
              </div>
            </div>
          </div>

          <div class="subsection">
            <h5 class="subsection-title">（六）食品安全日管控检查问题落实情况</h5>
            <div class="daily-check-stats">
              <div class="stats-row">
                <span>1、本月日常检查问题数为</span>
                <el-input-number v-model="totalIssues" :min="0" size="small" controls-position="right"></el-input-number>
                <span>项，已落实整改</span>
                <el-input-number v-model="resolvedIssues" :min="0" size="small" controls-position="right"></el-input-number>
                <span>项，待整改</span>
                <el-input-number v-model="pendingIssues" :min="0" size="small" controls-position="right"></el-input-number>
                <span>项，计划完成整改时间为</span>
                <el-input v-model="plannedCompletionTime" size="small" style="width: 200px;"></el-input>
                <span>；</span>
              </div>
              <div class="stats-row">
                <span>2.相关责任部门整改配合情况：</span>
                <el-input v-model="departmentCooperation" size="small" style="width: 300px;"></el-input>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二部分：较大风险隐患排查情况 -->
        <div class="content-section">
          <h4 class="section-title">二、较大风险隐患排查情况（包括但不限于以下部分）</h4>
          <el-input v-model="riskInvestigation" type="textarea" :rows="5" placeholder="请填写较大风险隐患排查情况..."></el-input>
        </div>

        <!-- 第三部分：食品安全相关部门发言及达成的共识 -->
        <div class="content-section">
          <h4 class="section-title">三、食品安全相关部门发言及达成的共识</h4>
          <el-input v-model="departmentConsensus" type="textarea" :rows="5" placeholder="请填写食品安全相关部门发言及达成的共识..."></el-input>
        </div>

        <!-- 第四部分：学校主要负责人工作指示 -->
        <div class="content-section">
          <h4 class="section-title">四、学校主要负责人工作指示（负责人：
            <el-input v-model="principalName" size="small" style="width: 120px; display: inline-block;"></el-input>）
          </h4>
          <el-input v-model="principalInstructions" type="textarea" :rows="5" placeholder="请填写学校主要负责人工作指示..."></el-input>
        </div>

        <!-- 第五部分：下个月食品安全管理重点工作调度计划 -->
        <div class="content-section">
          <h4 class="section-title">五、下个月食品安全管理重点工作调度计划</h4>
          <el-input v-model="nextMonthPlan" type="textarea" :rows="5" placeholder="请填写下个月食品安全管理重点工作调度计划..."></el-input>
        </div>
      </div>

      <!-- 签名区域 -->
      <div class="signature-area">
        <div class="signature-row">
          <div class="signature-item">
            <div class="signature-label">记录人：</div>
            <el-input v-model="recorder" class="signature-input" size="small"></el-input>
            <div class="date-label">日期：</div>
            <el-date-picker
              v-model="recordDate"
              type="date"
              placeholder="选择日期"
              format="yyyy 年 MM 月 dd 日"
              value-format="yyyy-MM-dd"
              size="small"
              style="width: 160px;">
            </el-date-picker>
          </div>
        </div>
        <div class="signature-row">
          <div class="signature-item">
            <div class="signature-label">负责人(校长/法人）：</div>
            <el-input v-model="principal" class="signature-input" size="small"></el-input>
            <div class="date-label">日期：</div>
            <el-date-picker
              v-model="principalDate"
              type="date"
              placeholder="选择日期"
              format="yyyy 年 MM 月 dd 日"
              value-format="yyyy-MM-dd"
              size="small"
              style="width: 160px;">
            </el-date-picker>
          </div>
        </div>
      </div>

      <!-- 按钮区域 -->
      <div class="button-area">
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="resetForm">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'XuexiaoHuiyiJiyaoManagementLedger',
  data() {
    return {
      // 会议基本信息
      recordNumber: '',
      meetingDate: '',
      chairperson: '',
      meetingFullDate: '',
      startTime: '',
      endTime: '',
      meetingLocation: '',
      attendees: '',

      // 第一部分：食品安全管理工作情况汇报
      // （一）互联网+明厨亮灶
      internetIssues: '',

      // （二）食品原料验收情况
      totalBatches: 0,
      qualifiedBatches: 0,
      unqualifiedBatches: 0,
      qualificationRate: 0,
      unqualifiedMaterials: '',
      unqualifiedReasons: '',
      handlingMeasures: '',

      // （三）学生及家长投诉处理情况
      totalComplaints: 0,
      validComplaints: 0,
      invalidComplaints: 0,
      complainedProducts: '',
      complaintReasons: '',
      improvementMeasures: '',

      // （四）供应商管理
      supplierAudit: '',

      // （五）从业人员食品安全相关培训情况
      trainingTimes: 0,
      trainingHours: 0,
      trainingParticipants: 0,

      // （六）食品安全日管控检查问题落实情况
      totalIssues: 0,
      resolvedIssues: 0,
      pendingIssues: 0,
      plannedCompletionTime: '',
      departmentCooperation: '',

      // 第二部分：较大风险隐患排查情况
      riskInvestigation: '',

      // 第三部分：食品安全相关部门发言及达成的共识
      departmentConsensus: '',

      // 第四部分：学校主要负责人工作指示
      principalName: '',
      principalInstructions: '',

      // 第五部分：下个月食品安全管理重点工作调度计划
      nextMonthPlan: '',

      // 签名区域
      recorder: '',
      recordDate: '',
      principal: '',
      principalDate: ''
    };
  },
  methods: {
    submitForm() {
      console.log('表单数据:', this.$data);
      this.$message.success('保存成功');
    },
    resetForm() {
      // 重置所有字段为初始值
      Object.keys(this.$data).forEach(key => {
        const value = this.$data[key];
        if (typeof value === 'number') {
          this.$data[key] = 0;
        } else if (typeof value === 'string') {
          this.$data[key] = '';
        }
      });
      this.$message.info('已重置表单');
    }
  }
};
</script>

<style lang="scss" scoped>
.meeting-minutes-wrapper {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .meeting-title {
    text-align: center;
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .meeting-info {
    border: 1px solid #dcdfe6;
    margin-bottom: 20px;
    padding: 15px;

    .info-row {
      display: flex;
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-item {
        display: flex;
        align-items: center;
        margin-right: 20px;

        &.meeting-name {
          flex: 1;
        }

        &.full-width {
          width: 100%;
          display: block;

          .label {
            display: block;
            margin-bottom: 5px;
          }
        }

        .label {
          font-weight: bold;
          margin-right: 10px;
          min-width: 80px;
          text-align: right;
        }

        .date-time {
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .meeting-content {
    margin-bottom: 20px;

    .content-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 15px;
    }

    .content-section {
      margin-bottom: 20px;
      border: 1px solid #dcdfe6;
      padding: 15px;

      .section-title {
        font-weight: bold;
        margin-bottom: 15px;
      }

      .subsection {
        margin-bottom: 15px;

        .subsection-title {
          font-weight: bold;
          margin-bottom: 10px;
        }

        .material-stats,
        .complaint-stats,
        .supplier-management,
        .training-stats,
        .daily-check-stats {
          padding-left: 15px;

          .stats-row {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 10px;

            .el-input-number {
              width: 80px;
              margin: 0 5px;
            }
          }
        }
      }
    }
  }

  .signature-area {
    margin-bottom: 20px;

    .signature-row {
      display: flex;
      margin-bottom: 15px;

      .signature-item {
        display: flex;
        align-items: center;

        .signature-label {
          font-weight: bold;
          margin-right: 10px;
        }

        .signature-input {
          width: 150px;
          margin-right: 20px;
        }

        .date-label {
          margin-right: 10px;
        }
      }
    }
  }

  .button-area {
    text-align: center;
    margin-top: 30px;
  }
}
</style>
