<template>
  <div class="container-wrapper">
    <div class="supplier-management-ledger">
      <h2 class="table-title">大宗食品及原料供应商名单</h2>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column prop="id" label="序号" width="60" align="center"></el-table-column>
        <el-table-column prop="itemType" label="采购品种" align="center"></el-table-column>
        <el-table-column prop="supplierName" label="供应商名称" align="center"></el-table-column>
        <el-table-column prop="supplierAddress" label="供应商经营地址" align="center"></el-table-column>
        <el-table-column prop="responsiblePerson" label="供应商负责人姓名" align="center"></el-table-column>
        <el-table-column prop="contactPhone" label="供应商联系电话" align="center"></el-table-column>
        <el-table-column prop="contractPeriod" label="供应商合同期限" align="center"></el-table-column>
        <el-table-column prop="businessLicense" label="营业执照号码" align="center"></el-table-column>
        <el-table-column prop="foodLicense" label="食品生产/经营许可证号" align="center"></el-table-column>
        <el-table-column prop="validPeriod" label="有效期限至" align="center"></el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShitangCaigouManagementLedger',
  data() {
    return {
      currentPage: 1,
      pageSize: 20,
      totalItems: 16,
      tableData: [
        {
          id: 1,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 2,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 3,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 4,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 5,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 6,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 7,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 8,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 9,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 10,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 11,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 12,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 13,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 14,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 15,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        },
        {
          id: 16,
          itemType: '',
          supplierName: '',
          supplierAddress: '',
          responsiblePerson: '',
          contactPhone: '',
          contractPeriod: '',
          businessLicense: '',
          foodLicense: '',
          validPeriod: ''
        }
      ]
    };
  },
  methods: {
    handleSizeChange(size) {
      this.pageSize = size;
    },
    handleCurrentChange(page) {
      this.currentPage = page;
    },
    cellStyle() {
      return {
        padding: '8px 5px',
        fontSize: '14px',
        textAlign: 'center'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 5px',
        textAlign: 'center'
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.supplier-management-ledger {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
