<template>
  <div class="container-wrapper">
    <div class="allergenic-ingredients-record">
      <h2 class="table-title">过敏原使用管理台账</h2>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column prop="supplyDate" label="供应日期" min-width="120" align="center"></el-table-column>
        <el-table-column prop="supplyMeal" label="供应餐次" min-width="120" align="center"></el-table-column>
        <el-table-column prop="allergenicIngredient" label="使用的过敏原食材" min-width="150" align="center"></el-table-column>
        <el-table-column prop="dishName" label="菜品名称" min-width="150" align="center"></el-table-column>
        <el-table-column prop="recorder" label="登记人" min-width="100" align="center"></el-table-column>
        <el-table-column prop="remarks" label="备注" min-width="100" align="center"></el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GuominyuanManagementLedger',
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      totalItems: 2,
      tableData: [
        {
          supplyDate: '4月1日',
          supplyMeal: '午餐',
          allergenicIngredient: '花生',
          dishName: '花生焖猪手',
          recorder: '张三',
          remarks: ''
        },
        {
          supplyDate: '4月1日',
          supplyMeal: '晚餐',
          allergenicIngredient: '鸡蛋',
          dishName: '西红柿炒鸡蛋',
          recorder: '张三',
          remarks: ''
        }
      ]
    };
  },
  methods: {
    handleSizeChange(size) {
      this.pageSize = size;
    },
    handleCurrentChange(page) {
      this.currentPage = page;
    },
    cellStyle(row, column) {
      // Add red color to example rows
      if (row.row.supplyDate && row.row.supplyDate.includes('举例')) {
        return {
          color: '#f56c6c',
          padding: '8px 5px',
          fontSize: '14px'
        };
      }
      return {
        padding: '8px 5px',
        fontSize: '14px'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 5px'
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.allergenic-ingredients-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
