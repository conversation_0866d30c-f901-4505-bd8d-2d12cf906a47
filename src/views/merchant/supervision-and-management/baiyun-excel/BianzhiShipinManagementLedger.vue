<template>
  <div class="container-wrapper">
    <div class="spoiled-food-record">
      <h2 class="table-title">变质、超过保质期食品销毁记录</h2>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column prop="disposalDate" label="销毁日期" min-width="100" align="center"></el-table-column>
        <el-table-column prop="foodName" label="食品名称" min-width="100" align="center"></el-table-column>
        <el-table-column prop="unit" label="单位" min-width="80" align="center"></el-table-column>
        <el-table-column prop="quantity" label="数量" min-width="80" align="center"></el-table-column>
        <el-table-column label="销毁原因" min-width="140" align="center">
          <template slot-scope="scope">
            <div class="checkbox-group">
              <el-checkbox v-model="scope.row.reason.spoiled">变质</el-checkbox><br>
              <el-checkbox v-model="scope.row.reason.expired">过期</el-checkbox><br>
              <div class="other-reason">
                <el-checkbox v-model="scope.row.reason.other">其他：</el-checkbox>
                <el-input v-if="scope.row.reason.other" v-model="scope.row.reason.otherText" size="mini" class="other-input"></el-input>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="销毁处理方法" min-width="120" align="center">
          <template slot-scope="scope">
            <div class="checkbox-group">
              <el-checkbox v-model="scope.row.method.dye">染色</el-checkbox><br>
              <el-checkbox v-model="scope.row.method.deform">毁形</el-checkbox>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="disposalPerson" label="销毁人" min-width="100" align="center"></el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 15, 20]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BianzhiManagementLedger',
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      totalItems: 15,
      tableData: Array(15).fill().map(() => ({
        disposalDate: '',
        foodName: '',
        unit: '',
        quantity: '',
        reason: {
          spoiled: false,
          expired: false,
          other: false,
          otherText: ''
        },
        method: {
          dye: false,
          deform: false
        },
        disposalPerson: ''
      }))
    };
  },
  methods: {
    handleSizeChange(size) {
      this.pageSize = size;
    },
    handleCurrentChange(page) {
      this.currentPage = page;
    },
    cellStyle() {
      return {
        padding: '8px 5px',
        fontSize: '14px'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 5px'
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.spoiled-food-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  .checkbox-group {
    text-align: left;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .el-checkbox {
      margin-left: 0;
      margin-bottom: 5px;
    }
  }

  .other-reason {
    display: flex;
    align-items: center;

    .other-input {
      width: 80px;
      margin-left: 5px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
