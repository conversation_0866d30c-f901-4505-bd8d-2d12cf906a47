<template>
  <div class="container-wrapper">
    <div class="equipment-maintenance-ledger">
      <h2 class="table-title">食堂设备定期维护/清洗记录本</h2>

      <div class="date-header">
        <span class="year-month">_____年_____月</span>
      </div>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column prop="day" label="日期" width="70" align="center"></el-table-column>

        <!-- 设备名称 -->
        <el-table-column label="设备名称" align="center">
          <el-table-column prop="xiaodu" label="消毒柜"  align="center"></el-table-column>
          <el-table-column prop="zhengshao" label="蒸烧柜" align="center"></el-table-column>
          <el-table-column prop="reshuiqi" label="热水器" align="center"></el-table-column>
          <el-table-column prop="reshuiyou" label="热汤池" align="center"></el-table-column>
          <el-table-column prop="bingxiang" label="冰箱" align="center"></el-table-column>
          <el-table-column prop="purenweixiang" label="紫外线灯" align="center"></el-table-column>
          <el-table-column prop="weibolu" label="灭蝇灯" align="center"></el-table-column>
          <el-table-column prop="baocanto" label="保餐柜" align="center"></el-table-column>
          <el-table-column prop="chouyouyan" label="抽油烟机" align="center"></el-table-column>
        </el-table-column>

        <el-table-column prop="cleaning" label="清洗"  align="center"></el-table-column>
        <el-table-column prop="maintenance" label="维护"  align="center"></el-table-column>
        <el-table-column prop="status" label="设备状态"  align="center"></el-table-column>
        <el-table-column prop="operator" label="操作人员"  align="center"></el-table-column>
      </el-table>

      <div class="note">
        <p>备注：食堂相关设备的维护与清洗，每周定期维护与清洗，并如实做好相关记录。</p>
      </div>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 31]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShitangShebeiManagementLedger',
  data() {
    return {
      currentPage: 1,
      pageSize: 31,
      totalItems: 31,
      tableData: this.generateDaysData()
    }
  },
  methods: {
    generateDaysData() {
      const days = [];
      for (let i = 1; i <= 31; i++) {
        days.push({
          day: `${i}日`,
          xiaodu: '',
          zhengshao: '',
          reshuiqi: '',
          reshuiyou: '',
          bingxiang: '',
          purenweixiang: '',
          weibolu: '',
          baocanto: '',
          chouyouyan: '',
          cleaning: '',
          maintenance: '',
          status: '',
          operator: ''
        });
      }
      return days;
    },
    handleSizeChange(size) {
      this.pageSize = size;
    },
    handleCurrentChange(page) {
      this.currentPage = page;
    },
    cellStyle() {
      return {
        padding: '8px 2px',
        fontSize: '13px',
        textAlign: 'center'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '10px 2px',
        textAlign: 'center'
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.equipment-maintenance-ledger {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: bold;
  }

  .date-header {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;

    .year-month {
      font-size: 16px;
      font-weight: bold;
    }
  }

  .note {
    margin-top: 15px;
    font-size: 14px;

    p {
      margin: 0;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
