<template>
  <div class="container-wrapper">
    <div class="range-hood-cleaning-ledger">
      <h2 class="table-title">厨房油烟机清洗记录</h2>

      <div class="header-info">
        <div class="canteen-name">
          <span class="label">食堂名称：</span>
          <span class="value"></span>
        </div>
        <div class="cleaning-date">
          <span class="label">清洗日期：</span>
          <span class="value"></span>
        </div>
      </div>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column prop="sequence" label="序号" width="80" align="center"></el-table-column>
        <el-table-column prop="content" label="清洗内容" align="center"></el-table-column>
        <el-table-column prop="status" label="清洗情况" align="center"></el-table-column>
        <el-table-column prop="cleaner" label="清洗人员" align="center"></el-table-column>
        <el-table-column prop="inspector" label="检查确认人" align="center"></el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChufangChouyouManagementLedger',
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      totalItems: 4,
      tableData: [
        {
          sequence: 1,
          content: '烟罩油、尘清除是否干净',
          status: '',
          cleaner: '',
          inspector: ''
        },
        {
          sequence: 2,
          content: '管道内油垢清洗',
          status: '',
          cleaner: '',
          inspector: ''
        },
        {
          sequence: 3,
          content: '排风机风扇上的油污铲干净',
          status: '',
          cleaner: '',
          inspector: ''
        },
        {
          sequence: 4,
          content: '排风机过滤器上的油污清洗干净',
          status: '',
          cleaner: '',
          inspector: ''
        }
      ]
    }
  },
  methods: {
    handleSizeChange(size) {
      this.pageSize = size;
    },
    handleCurrentChange(page) {
      this.currentPage = page;
    },
    cellStyle() {
      return {
        padding: '12px 8px',
        fontSize: '14px',
        textAlign: 'center'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '12px 8px',
        textAlign: 'center'
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.range-hood-cleaning-ledger {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  .header-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .canteen-name, .cleaning-date {
      font-size: 16px;
      font-weight: bold;

      .label {
        display: inline-block;
        min-width: 100px;
      }

      .value {
        display: inline-block;
        min-width: 200px;
        border-bottom: 1px solid #ccc;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
