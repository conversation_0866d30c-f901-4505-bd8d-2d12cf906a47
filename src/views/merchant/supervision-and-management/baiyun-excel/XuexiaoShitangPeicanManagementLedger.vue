<template>
  <div class="container-wrapper">
    <div class="school-canteen-record">
      <h2 class="table-title">学校食堂陪餐记录表</h2>

      <el-table
        :data="displayData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <!-- 陪餐日期及时间 -->
        <el-table-column label="陪餐日期及时间" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.date" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>

        <!-- 餐次 -->
        <el-table-column label="餐次" align="center">
          <template slot-scope="scope">
            <el-select v-model="scope.row.mealTime" size="mini" placeholder="">
              <el-option label="早餐" value="早餐"></el-option>
              <el-option label="午餐" value="午餐"></el-option>
              <el-option label="晚餐" value="晚餐"></el-option>
            </el-select>
          </template>
        </el-table-column>

        <!-- 陪餐饭菜明细 -->
        <el-table-column label="陪餐饭菜明细" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.mealDetails" size="mini" placeholder="" type="text" :rows="2"></el-input>
          </template>
        </el-table-column>

        <!-- 陪餐价格 -->
        <el-table-column label="陪餐价格" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.price" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>

        <!-- 用餐评价 -->
        <el-table-column label="用餐评价（卫生、质量、口感等）" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.evaluation" size="mini" placeholder="" type="text" :rows="2"></el-input>
          </template>
        </el-table-column>

        <!-- 分餐人员等 -->
        <el-table-column label="分餐人员穿戴、态度及学生就餐秩序" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.serviceEvaluation" size="mini" placeholder="" type="text" :rows="2"></el-input>
          </template>
        </el-table-column>

        <!-- 供餐菜品与食谱对比 -->
        <el-table-column label="供餐菜品与食谱对比" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.menuComparison" size="mini" placeholder="" type="text" :rows="2"></el-input>
          </template>
        </el-table-column>

        <!-- 学生反馈意见 -->
        <el-table-column label="学生反馈意见" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.studentFeedback" size="mini" placeholder="" type="text" :rows="2"></el-input>
          </template>
        </el-table-column>

        <!-- 陪餐人员建议 -->
        <el-table-column label="陪餐人员建议" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.suggestions" size="mini" placeholder="" type="text" :rows="2"></el-input>
          </template>
        </el-table-column>

        <!-- 就餐后有无异常 -->
        <el-table-column label="就餐后有无异常" align="center">
          <template slot-scope="scope">
            <el-select v-model="scope.row.abnormal" size="mini" placeholder="">
              <el-option label="无" value="无"></el-option>
              <el-option label="有" value="有"></el-option>
            </el-select>
          </template>
        </el-table-column>

        <!-- 签名 -->
        <el-table-column label="签名（职务）" align="center" width="120">
          <template slot-scope="scope">
            <el-input v-model="scope.row.signature" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>
      </el-table>

      <div class="note-section">
        <p class="note-title">注：</p>
        <p>1、陪餐人员应尝试每个菜品，并将陪餐时发现的问题记录在"用餐评价"栏中，由陪餐人员及时跟踪处理问题是否已经解决。</p>
        <p>2、餐后异常反应包括头晕、呕吐、腹痛、腹泻、嗜睡等。</p>
      </div>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'XuexiaoShitangPeicanManagementLedger',
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      tableData: this.generateTableData()
    };
  },
  computed: {
    displayData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableData.slice(start, end);
    }
  },
  methods: {
    generateTableData() {
      const data = [];

      // 生成12行数据
      for (let i = 1; i <= 12; i++) {
        data.push({
          date: '',
          mealTime: '',
          mealDetails: '',
          price: '',
          evaluation: '',
          serviceEvaluation: '',
          menuComparison: '',
          studentFeedback: '',
          suggestions: '',
          abnormal: '无',
          signature: ''
        });
      }

      return data;
    },
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    cellStyle() {
      return {
        padding: '5px',
        fontSize: '14px',
        textAlign: 'center'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 0',
        textAlign: 'center'
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.school-canteen-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  .note-section {
    margin: 20px 0;
    padding: 10px;
    background-color: #f8f8f8;
    border-radius: 4px;

    .note-title {
      font-weight: bold;
      margin-bottom: 5px;
    }

    p {
      margin: 5px 0;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  ::v-deep .el-table .cell {
    padding: 5px;
  }

  ::v-deep .el-input__inner, ::v-deep .el-text__inner {
    border: none;
    border-bottom: 1px solid #DCDFE6;
    border-radius: 0;
    text-align: center;
  }

  ::v-deep .el-text__inner {
    text-align: left;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
