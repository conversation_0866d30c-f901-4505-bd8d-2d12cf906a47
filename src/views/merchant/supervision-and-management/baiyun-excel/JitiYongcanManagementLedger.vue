<template>
  <div class="container-wrapper">
    <div class="delivery-info-wrapper">
      <h2 class="form-title">集体用餐配送单位信息记录</h2>

      <div class="info-table">
        <el-table
          :data="[tableData]"
          border
          style="width: 100%">

          <el-table-column label="配送单位名称" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.unitName" placeholder="请输入配送单位名称"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="配送单位经营地址" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.unitAddress" placeholder="请输入配送单位经营地址"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.otherInfo1" placeholder=""></el-input>
            </template>
          </el-table-column>
        </el-table>

        <el-table
          :data="[tableData]"
          border
          style="width: 100%; margin-top: -1px;">

          <el-table-column label="食品经营许可证号码" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.licenseNumber" placeholder="请输入食品经营许可证号码"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="有效期至" header-align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.expiryDate"
                type="date"
                placeholder="选择有效期日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%;">
              </el-date-picker>
            </template>
          </el-table-column>

          <el-table-column label="" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.otherInfo2" placeholder=""></el-input>
            </template>
          </el-table-column>
        </el-table>

        <el-table
          :data="[tableData]"
          border
          style="width: 100%; margin-top: -1px;">

          <el-table-column label="营业执照号码" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.businessLicenseNumber" placeholder="请输入营业执照号码"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="总配送数" header-align="center">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.totalDeliveries" :min="0" placeholder="请输入总配送数" style="width: 100%;"></el-input-number>
            </template>
          </el-table-column>

          <el-table-column label="" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.otherInfo3" placeholder=""></el-input>
            </template>
          </el-table-column>
        </el-table>

        <el-table
          :data="[tableData]"
          border
          style="width: 100%; margin-top: -1px;">

          <el-table-column label="法定代表人" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.legalRepresentative" placeholder="请输入法定代表人姓名"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="联系电话" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.legalRepPhone" placeholder="请输入联系电话"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.otherInfo4" placeholder=""></el-input>
            </template>
          </el-table-column>
        </el-table>

        <el-table
          :data="[tableData]"
          border
          style="width: 100%; margin-top: -1px;">

          <el-table-column label="食品安全管理员" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.safetyManager" placeholder="请输入食品安全管理员姓名"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="联系电话" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.safetyManagerPhone" placeholder="请输入联系电话"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.otherInfo5" placeholder=""></el-input>
            </template>
          </el-table-column>
        </el-table>

        <el-table
          :data="[tableData]"
          border
          style="width: 100%; margin-top: -1px;">

          <el-table-column label="配送负责人" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.deliveryManager" placeholder="请输入配送负责人姓名"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="联系电话" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.deliveryManagerPhone" placeholder="请输入联系电话"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="" header-align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.otherInfo6" placeholder=""></el-input>
            </template>
          </el-table-column>
        </el-table>

        <el-table
          :data="[tableData]"
          border
          style="width: 100%; margin-top: -1px;">

          <el-table-column label="配送年级/人数" header-align="center" colspan="3">
            <template slot-scope="scope">
              <el-input v-model="scope.row.deliveryGradeAndNumber" type="textarea" :rows="2" placeholder="请输入配送年级和人数"></el-input>
            </template>
          </el-table-column>
        </el-table>

        <el-table
          :data="[tableData]"
          border
          style="width: 100%; margin-top: -1px;">

          <el-table-column label="配送餐次/数量" header-align="center" colspan="3">
            <template slot-scope="scope">
              <el-input v-model="scope.row.deliveryMealsAndQuantity" type="textarea" :rows="2" placeholder="请输入配送餐次和数量"></el-input>
            </template>
          </el-table-column>
        </el-table>

        <el-table
          :data="[tableData]"
          border
          style="width: 100%; margin-top: -1px;">

          <el-table-column label="配送合同起止时间" header-align="center" colspan="3">
            <template slot-scope="scope">
              <div class="date-range-container">
                <el-date-picker
                  v-model="scope.row.contractStartDate"
                  type="date"
                  placeholder="开始日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 45%;">
                </el-date-picker>
                <span class="date-separator">至</span>
                <el-date-picker
                  v-model="scope.row.contractEndDate"
                  type="date"
                  placeholder="结束日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 45%;">
                </el-date-picker>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 按钮区域 -->
      <div class="button-area">
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="resetForm">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'JitiYongcanManagementLedger',
  data() {
    return {
      tableData: {
        // 基本信息
        unitName: '',
        unitAddress: '',
        otherInfo1: '',

        // 许可证信息
        licenseNumber: '',
        expiryDate: '',
        otherInfo2: '',

        // 营业执照和配送数量
        businessLicenseNumber: '',
        totalDeliveries: 0,
        otherInfo3: '',

        // 法定代表人
        legalRepresentative: '',
        legalRepPhone: '',
        otherInfo4: '',

        // 食品安全管理员
        safetyManager: '',
        safetyManagerPhone: '',
        otherInfo5: '',

        // 配送负责人
        deliveryManager: '',
        deliveryManagerPhone: '',
        otherInfo6: '',

        // 配送年级/人数
        deliveryGradeAndNumber: '',

        // 配送餐次/数量
        deliveryMealsAndQuantity: '',

        // 合同期限
        contractStartDate: '',
        contractEndDate: ''
      }
    };
  },
  methods: {
    submitForm() {
      console.log('表单数据:', this.tableData);
      this.$message.success('保存成功');
    },
    resetForm() {
      this.tableData = {
        unitName: '',
        unitAddress: '',
        otherInfo1: '',
        licenseNumber: '',
        expiryDate: '',
        otherInfo2: '',
        businessLicenseNumber: '',
        totalDeliveries: 0,
        otherInfo3: '',
        legalRepresentative: '',
        legalRepPhone: '',
        otherInfo4: '',
        safetyManager: '',
        safetyManagerPhone: '',
        otherInfo5: '',
        deliveryManager: '',
        deliveryManagerPhone: '',
        otherInfo6: '',
        deliveryGradeAndNumber: '',
        deliveryMealsAndQuantity: '',
        contractStartDate: '',
        contractEndDate: ''
      };
      this.$message.info('已重置表单');
    }
  }
};
</script>

<style lang="scss" scoped>
.delivery-info-wrapper {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .form-title {
    text-align: center;
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .info-table {
    margin-bottom: 20px;

    .date-range-container {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .date-separator {
        padding: 0 10px;
      }
    }
  }

  .button-area {
    text-align: center;
    margin-top: 30px;
  }
}

// 自定义表格样式
::v-deep .el-table {
  th {
    background-color: #f5f7fa;
    padding: 8px 0;

    .cell {
      font-weight: bold;
      text-align: center;
    }
  }

  td {
    padding: 5px;
  }

  .el-input__inner,
  .el-textarea__inner {
    border: none;
  }
}
</style>
