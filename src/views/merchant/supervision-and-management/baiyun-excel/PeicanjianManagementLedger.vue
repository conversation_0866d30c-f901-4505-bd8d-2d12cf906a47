<template>
  <div class="container-wrapper">
    <div class="uv-inspection-record">
      <h2 class="table-title">
        <span>食堂配餐间（专间）紫外线灯擦拭工作记录表</span>
      </h2>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column label="日期" align="center" width="300">
          <template slot-scope="scope">
            <div class="date-selector">
              <el-select v-model="scope.row.year" placeholder="年" size="mini" style="width: 90px">
                <el-option v-for="i in years" :key="`year-${i}`" :label="`${i}年`" :value="i"></el-option>
              </el-select>
              <el-select v-model="scope.row.month" placeholder="月" size="mini" style="width: 80px">
                <el-option v-for="i in 12" :key="`month-${i}`" :label="`${i}月`" :value="i"></el-option>
              </el-select>
              <el-select v-model="scope.row.day" placeholder="日" size="mini" style="width: 80px">
                <el-option v-for="i in 31" :key="`day-${i}`" :label="`${i}日`" :value="i"></el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="擦拭剂名称" prop="cleaningAgent" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.cleaningAgent" size="mini"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="执行人" prop="executor" align="center" width="120">
          <template slot-scope="scope">
            <el-input v-model="scope.row.executor" size="mini"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remarks" align="center" width="120">
          <template slot-scope="scope">
            <el-input v-model="scope.row.remarks" size="mini"></el-input>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[12, 24, 48, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PeicanjianManagementLedger',
  data() {
    // Generate current year and next 5 years
    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: 6 }, (_, i) => currentYear + i - 3);

    // Generate sample data
    const generateData = () => {
      const data = [];

      for (let i = 1; i <= 24; i++) {
        data.push({
          id: i,
          year: null,
          month: null,
          day: null,
          cleaningAgent: '',
          executor: '',
          remarks: ''
        });
      }
      return data;
    };

    return {
      tableData: generateData(),
      currentPage: 1,
      pageSize: 12,
      totalItems: 24,
      years: years
    };
  },
  computed: {
    displayData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableData.slice(start, end);
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    cellStyle() {
      return {
        padding: '5px',
        fontSize: '14px'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 0'
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.uv-inspection-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  .date-selector {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  ::v-deep .el-input__inner,
  ::v-deep .el-select .el-input__inner {
    padding: 0 5px;
    height: 28px;
    line-height: 28px;
    text-align: center;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
