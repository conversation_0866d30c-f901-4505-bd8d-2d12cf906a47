<template>
  <div class="container-wrapper">
    <div class="knife-management-ledger">
      <h2 class="table-title">学校食堂厨房刀具管理明细表（<span class="month-placeholder">6</span>月）</h2>

      <div class="year-row">
        <span class="label">年:</span>
        <span class="value"></span>
      </div>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column prop="date" label="日期" width="80" align="center"></el-table-column>
        <el-table-column prop="knifeType" label="刀具名称" align="center"></el-table-column>
        <el-table-column prop="usageCount" label="在用数量" align="center"></el-table-column>
        <el-table-column prop="disinfectionMethod" label="消毒方式" align="center"></el-table-column>
        <el-table-column prop="isDisinfected" label="是否消毒" width="100" align="center">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.isDisinfected" :true-label="'是'" :false-label="'否'" ></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column prop="knifeManager" label="刀具保管人签名" align="center"></el-table-column>
        <el-table-column prop="discardCount" label="废旧数量" align="center"></el-table-column>
        <el-table-column prop="discardMethod" label="废旧处理" align="center"></el-table-column>
        <el-table-column prop="discardManager" label="废旧刀具处理人签名" align="center"></el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShitangDaojuManagementLedger',
  data() {
    // Helper function to generate rows for a specific day
    const generateDayRows = (day) => {
      return [
        {
          date: `${day}日`,
          knifeType: '水产专用刀',
          usageCount: '把',
          disinfectionMethod: '高温红外线消毒',
          isDisinfected: '是',
          knifeManager: '',
          discardCount: '把',
          discardMethod: '统一回收',
          discardManager: ''
        },
        {
          date: '',
          knifeType: '切菜专用刀',
          usageCount: '把',
          disinfectionMethod: '高温红外线消毒',
          isDisinfected: '是',
          knifeManager: '',
          discardCount: '把',
          discardMethod: '统一回收',
          discardManager: ''
        },
        {
          date: '',
          knifeType: '切肉专用刀',
          usageCount: '把',
          disinfectionMethod: '洗涤剂法',
          isDisinfected: '否',
          knifeManager: '',
          discardCount: '把',
          discardMethod: '统一回收',
          discardManager: ''
        },
        {
          date: '',
          knifeType: '削皮刀、剪刀',
          usageCount: '把',
          disinfectionMethod: '洗涤剂法',
          isDisinfected: '否',
          knifeManager: '',
          discardCount: '把',
          discardMethod: '统一回收',
          discardManager: ''
        }
      ];
    };

    // Generate data for 8 days
    let allData = [];
    for (let i = 1; i <= 8; i++) {
      allData = [...allData, ...generateDayRows(i)];
    }

    return {
      currentPage: 1,
      pageSize: 20,
      totalItems: allData.length,
      tableData: allData
    };
  },
  methods: {
    handleSizeChange(size) {
      this.pageSize = size;
    },
    handleCurrentChange(page) {
      this.currentPage = page;
    },
    cellStyle() {
      return {
        padding: '8px 5px',
        fontSize: '14px',
        textAlign: 'center'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 5px',
        textAlign: 'center'
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.knife-management-ledger {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;

    .month-placeholder {
      display: inline-block;
      min-width: 30px;
      border-bottom: 1px solid #000;
      text-align: center;
    }
  }

  .year-row {
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: bold;

    .label {
      display: inline-block;
      min-width: 50px;
    }

    .value {
      display: inline-block;
      min-width: 100px;
      border-bottom: 1px solid #ccc;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
