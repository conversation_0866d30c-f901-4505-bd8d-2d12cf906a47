<template>
  <div class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandler"
      @reset="resetHandler"
    />
    <!-- 表格 -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="gotoConfig">配置项</button-icon>
          <button-icon color="origin" @click="gotoAdd">添加</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #create_time="{ row }">
              <div>{{ row.create_time | formatDate }}</div>
            </template>

            <template #name="{ row }">
              <div v-if="row.name">{{ row.name }}({{row.account}})</div>
              <span v-else>--</span>
            </template>

            <template #image_json="{ row }">
              <el-button
                v-if="row.image_json && row.image_json.length > 0"
                type="text"
                size="small"
                class="ps-text"
                @click="handlerImage(row)"
              >
                查看
              </el-button>
              <span v-else>--</span>
            </template>

            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="deleteList(row.id)">删除</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
    </div>
    <!-- 配置项目 -->
    <custom-drawer
      title="配置项目"
      :show.sync="showConfig"
      direction="rtl"
      :wrapperClosable="true"
      :size="500"
      :fixedFooter="true"
      class="drawer-wrapper"
      cancel-text="关闭"
      confirm-text="保存"
      @close="handlerClose('config')"
      @cancel="clickCancleHandle('config')"
      @confirm="clickConfirmHandle('config')"
    >
      <el-form v-loading="isLoading" :rules="formConfigRuls" label-width="20px" ref="formRules" size="small">
        <template v-for="(item, index) in curProject">
          <el-form-item label="" :prop="'project_' + index" :key=index>
            <div style="display: flex; align-items: center">
              <span style="width: 80px">项目名称</span>
              <el-input v-model="item.value" maxLength="20" style="width: 200px; margin-right: 20px" />
              <el-button
                v-if="index === curProject.length - 1"
                style="font-size: 25px"
                icon="el-icon-circle-plus"
                type="text"
                @click="addUsePoint('config')"
              />

              <el-button
                v-if="!(index === 0 && curProject.length === 1)"
                style="font-size: 25px"
                icon="el-icon-remove"
                type="text"
                @click="deleteUsePoint('config', index)"
              />
            </div>
          </el-form-item>
        </template>
      </el-form>
    </custom-drawer>
    <!-- 添加巡查记录 -->
    <custom-drawer
      :title="selectRow.id ? '查看巡查记录' : '添加巡查记录'"
      :show.sync="showAdd"
      direction="rtl"
      :wrapperClosable="true"
      :size="500"
      :fixedFooter="true"
      class="drawer-wrapper"
      cancel-text="取消"
      confirm-text="保存"
      @close="handlerClose('add')"
      @cancel="clickCancleHandle('add')"
      @confirm="clickConfirmHandle('add')"
    >
      <el-form v-loading="isLoading" :model="formAddModel" label-width="80px" ref="formAddRef" size="small">
        <template v-for="(item, recordIndex) in formAddModel.patrolRecord">
          <div class="patrol-item-box" :key="recordIndex">
            <el-form-item label="巡查项目" :prop="`patrolRecord.${recordIndex}.project`" :rules="formAddRuls.project">
              <div style="display: flex; align-items: center">
                <el-select v-model="item.project" placeholder="请选择" clearable>
                  <el-option
                    v-for="item in searchFormSetting.project.dataList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <el-button
                  v-if="recordIndex === formAddModel.patrolRecord.length - 1"
                  style="margin-left: 20px; font-size: 25px; padding: 0 !important"
                  icon="el-icon-circle-plus"
                  type="text"
                  @click="addUsePoint('add')"
                />
                <el-button
                  v-if="!(recordIndex === 0 && formAddModel.patrolRecord.length === 1)"
                  style="margin-left: 20px; font-size: 25px; padding: 0 !important"
                  icon="el-icon-remove"
                  type="text"
                  @click="deleteUsePoint('add', recordIndex)"
                />
              </div>
            </el-form-item>
            <el-form-item label="巡查结果" prop="result">
              <el-input
                v-model="item.result"
                type="textarea"
                :rows="3"
                placeholder="请输入内容"
                :maxlength="20"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="巡查凭证" prop="image_json">
              <file-upload
                :ref="`faceFileRef_${recordIndex}`"
                :fileList="item.image_json"
                type="enclosure"
                :before-upload="event => beforeUpload(event, recordIndex)"
                @fileLists="event => getFileLists(event, recordIndex)"
                :limit="3"
                accept="image/*"
                class="avatar-uploader"
                :show-file-list="false"
              >
                <div v-loading="item.isLoading" v-if="item.image_json.length < 3" style="margin-bottom: 20px">
                  <i class="el-icon-plus avatar-uploader-icon">（选择文件不超过2M）</i>
                </div>
                <div v-if="item.image_json.length">
                  <div v-for="(img, imageIndex) in item.image_json" :key="img.id">
                    <el-image
                      :src="img.url"
                      fit="contain"
                      class="avatar"
                      @click="clearFileHandle(recordIndex, imageIndex)"
                    />
                  </div>
                </div>
              </file-upload>
            </el-form-item>
          </div>
        </template>
      </el-form>
    </custom-drawer>
    <!-- 图片预览 -->
    <image-viewer
      v-model="showViewer"
      :initial-index="imgIndex"
      :z-index="3000"
      :on-close="closeViewer"
      :preview-src-list="getRowImageJson"
    />
  </div>
</template>

<script>
import { debounce, deepClone, getToken } from '@/utils'
import { TABLE_HEAD_SETTING, SEARCH_FORM_SETTING } from './constans'
export default {
  name: 'DailyInspection',
  data() {
    return {
      // 预览图片
      showViewer: false,
      imgIndex: 0,

      // 上传图片
      headersOpts: {
        TOKEN: getToken()
      },
      serverUrl: '/api/background/file/upload',
      upLoading: false, // 上传加载中

      // 表单表格
      formConfigRuls: {},
      curProject: [],
      formAddModel: {
        patrolRecord: [
          {
            project: '',
            result: '',
            image_json: [],
            isLoading: false
          }
        ]
      },
      selectRow: { // 选择行
        image_json: []
      },
      formAddRuls: {
        project: [{ required: true, message: '请选择巡查项目', trigger: ['change', 'blur'] }]
      }, // 添加巡查记录表单验证规则
      showAdd: false, // 添加巡查记录弹窗
      showConfig: false, // 配置项目弹窗
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableSettings: deepClone(TABLE_HEAD_SETTING),
      searchFormSetting: deepClone(SEARCH_FORM_SETTING),
      tableData: []
    }
  },
  created() {
    this.initLoad()
    this.getProjectList()
  },
  computed: {
    getCurProject() {
      if (this.searchFormSetting.project.dataList.length === 0) {
        return [{ label: '', value: '' }]
      } else {
        return this.searchFormSetting.project.dataList
      }
    },
    getRowImageJson() {
      return this.selectRow.image_json?.map(item => item.url) || []
    }
  },
  methods: {
    closeViewer() {
      this.showViewer = false
    },
    handlerImage(row) {
      this.selectRow = row
      this.showViewer = true
    },
    // 删除指定索引的图片
    clearFileHandle(recordIndex, imageIndex) {
      const fileRef = this.$refs[`faceFileRef_${recordIndex}`][0] || null
      if (fileRef && this.formAddModel.patrolRecord[recordIndex].image_json[imageIndex]) {
        fileRef.spliceFileData(this.formAddModel.patrolRecord[recordIndex].image_json[imageIndex].uid)
      }
      this.formAddModel.patrolRecord[recordIndex].image_json.splice(imageIndex, 1)
      this.formAddModel.patrolRecord[recordIndex].isLoading = false
    },
    getFileLists(fileLists, recordIndex) {
      this.formAddModel.patrolRecord[recordIndex].image_json = fileLists
      this.formAddModel.patrolRecord[recordIndex].isLoading = false
    },
    beforeUpload(file, recordIndex) {
      try {
        // 检查文件是否是图片
        const isImage = file.type.startsWith('image/')
        if (!isImage) {
          throw new Error('只能上传图片文件!')
        }

        const isLt2M = file.size / 1024 / 1024 < 2
        if (!isLt2M) {
          throw new Error('上传图片大小不能超过 2MB!')
        }
      } catch (error) {
        this.$message.error(error.message)
        return false
      } finally {
        this.formAddModel.patrolRecord[recordIndex].isLoading = true
      }
    },
    addUsePoint(type) {
      if (type === 'config') {
        this.curProject.push({
          label: '',
          value: ''
        })
      } else if (type === 'add') {
        this.formAddModel.patrolRecord.push({
          project: '',
          result: '',
          image_json: [],
          isLoading: false
        })
      }
    },
    deleteUsePoint(type, index) {
      if (type === 'config') {
        this.curProject.splice(index, 1)
      } else if (type === 'add') {
        this.formAddModel.patrolRecord.splice(index, 1)
      }
    },
    handlerClose(type) {
      if (type === 'add') {
        this.$refs.formAddRef.resetFields()
        // 清空已上传的图片
        for (let recordIndex = 0; recordIndex < this.formAddModel.patrolRecord.length; recordIndex++) {
          const uploadFileRef = this.$refs[`faceFileRef_${recordIndex}`][0] || null
          uploadFileRef && uploadFileRef.clearHandle()
        }
        this.showAdd = false
      } else if (type === 'config') {
        this.showConfig = false
      }
    },
    clickCancleHandle(type) {
      if (type === 'add') {
        this.showAdd = false
      } else if (type === 'config') {
        this.showConfig = false
      }
    },
    clickConfirmHandle(type) {
      if (type === 'add') {
        this.saveList()
      } else if (type === 'config') {
        this.saveProject()
      }
    },
    gotoConfig() {
      this.showConfig = true
      this.curProject = deepClone(this.getCurProject)
    },
    gotoAdd() {
      this.formAddModel.patrolRecord = [
        {
          project: '',
          result: '',
          image_json: [],
          isLoading: false
        }
      ]
      this.showAdd = true
    },
    /**
     * 分页页数change事件
     */
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    /**
     * 搜索重置
     */
    refreshHandle() {
      this.currentPage = 1
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.initLoad()
    },
    /**
     * 筛选
     */
    searchHandler: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    /**
     * 重置筛选列表
     */
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    /**
     * 初始化数据
     */
    initLoad() {
      this.getDataList()
    },
    /**
     * 格式化查询参数
     */
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'select_time') {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          } else {
            params[key] = data[key].value
          }
        }
      }
      return params
    },
    /**
     * 获取数据列表
     */
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await this.$to(this.$apis.apiBackgroundFundSupervisionDailyPatrolList(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        if (data) {
          this.tableData = data.results || []
          this.totalCount = data.count || 0
        } else {
          this.$message.error(res.msg)
        }
      }
    },
    // 判断数组是否存在重复值
    hasDuplicates(arr) {
      return new Set(arr).size !== arr.length
    },
    /**
     * 获取项目配置列表
     */
    async getProjectList() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundFundSupervisionDailyPatrolGetProjectList())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        const datas = res.data || []
        this.searchFormSetting.project.dataList = datas.map(item => {
          return {
            label: item,
            value: item
          }
        })
      }
    },
    /**
     * 保存项目配置
     */
    async saveProject() {
      const isEmpty = this.curProject.some(item => item.value.trim() === '')
      if (isEmpty) {
        this.$message.error('请输入有效名称')
        return
      }

      const params = {
        project: this.curProject.map(item => item.value.trim()).filter(item => item !== '')
      }

      // 名称重复，请检查
      if (this.hasDuplicates(params.project)) {
        this.$message.error('名称重复，请检查')
        return
      }

      const [err, res] = await this.$to(this.$apis.apiBackgroundFundSupervisionDailyPatrolProjectSettings(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getProjectList()
        this.showConfig = false
      }
    },
    /**
     * 保存巡查记录
     */
    async saveList() {
      this.$refs.formAddRef.validate(valid => {
        if (!valid) {
          return
        }
        // 提交时去掉is_loading属性
        const processedPatrolRecord = this.formAddModel.patrolRecord.map(record => {
          const { isLoading, ...rest } = record
          return rest
        })
        const params = {
          patrol_record: processedPatrolRecord
        }
        this.$to(this.$apis.apiBackgroundFundSupervisionDailyPatrolAdd(params)).then(([err, res]) => {
          if (err) {
            this.$message.error(err.message)
            return
          }
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.getDataList()
            this.showAdd = false
          }
        })
      })
    },
    /**
     * 删除巡查记录
     */
    async deleteList(id) {
      this.$confirm(`确定删除吗?`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const params = { id }
            const [err, res] = await this.$to(this.$apis.apiBackgroundFundSupervisionDailyPatrolDelete(params))
            this.isLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.getDataList()
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.avatar-uploader {
  .avatar {
    width: 200px;
    height: 150px;
  }
}
.patrol-item-box {
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f5f5;
}
</style>
