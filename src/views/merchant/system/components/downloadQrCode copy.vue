<template>
  <div class="container-wrapper download-qrcode clearfix">
    <div v-for="(item, i) in qrCodeList" :key="item.type" :class="['float-l', i%2==0?'m-r-60':'']">
      <div class="margin-top-20">
        <!-- <el-radio-group v-model="qrcodeType" size="small">
          <el-radio-button :class="[item.type==='payment'?'ps-radio-btn':'ps-green-radio-btn']" label="consume">{{ item.radioLabel }}</el-radio-button>
        </el-radio-group> -->
        <el-radio-group v-model="item.selectRadio" size="small" @change="changeQrcoeType($event,item)">
          <el-radio-button v-for="v in item.radioList" :key="v.value" :class="[item.type==='recharge'?'ps-green-radio-btn':'ps-radio-btn']" :label="v.value">{{ v.label }}</el-radio-button>
        </el-radio-group>
      </div>
      <div class="margin-top-20">
        <p class="margin-top-20">{{ `(${labelName})${item.label}` }}</p>
        <qrcode :id="item.type" :value="item.qr_code" :options="item.qrcode_options" tag="img" :margin="10" alt />
        <div>
          <el-radio-group :class="[item.type==='recharge'?'ps-green-radio':'ps-radio']" v-model="item.spec_type" @change="changeSpecHandle(item)">
            <el-radio v-for="item in specList" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="margin-top-20">
        <el-button :class="[item.type==='recharge'?'ps-green-btn':'ps-origin-btn']" size="small" type="primary" @click="downloadHandle(item, i)">下载二维码</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { debounce, to, getQueryObject, paramsToUrlQuerys } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import qrcode from '@chenfengyuan/vue-qrcode'
import FileSaver from 'file-saver'

export default {
  name: 'DownloadQrCode',
  // mixins: [activatedLoadData],
  components: {
    qrcode
  },
  props: {
    type: String, // 区别是否是顶级的
    infoData: { // 查看或者修改的数据
      type: Object,
      default() {
        return {}
      }
    },
    organizationData: Object,
    restoreHandle: Function
  },
  data() {
    return {
      qrcodeType: 'consume',
      labelName: '',
      formOperate: 'detail',
      isLoading: false,
      // qrcodeOptions: {
      //   width: 256,
      //   height: 256
      //   // errorCorrectionLevel: 'H' // L M Q H
      // },
      specList: [
        { label: '50*50', value: 50 },
        { label: '55*55', value: 55 },
        { label: '60*60', value: 60 }
      ], // 二维码规格
      qrCodeList: [] //
    }
  },
  computed: {
    // 检查当前状态，编辑还是详情
    checkIsFormStatus: function() { // 默认为false
      let show = false
      switch (this.operate) { // 目前从父组件传过来的操作类型只会有2个add和detail
        case 'add':
          show = true
          break;
        case 'detail':
          if (this.formOperate === 'detail') {
            show = false
          } else {
            show = true
          }
          break;
        default: // 没传的话
          if (this.formOperate === 'detail') {
            show = false
          } else {
            show = true
          }
          break;
      }
      return show
    }
  },
  watch: {
  },
  created() {
  },
  mounted() {
    this.labelName = this.infoData.level_name + '-' + this.infoData.name
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getQrCode()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
    }, 300),
    downloadHandle(item, i) {
      FileSaver.saveAs(document.getElementById(item.type).getAttribute('src'), this.labelName + '-' + item.label + '.jpg')
    },
    async getQrCode() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundPaymentPayInfoGetConsumeQrcodePost({
        ids: [],
        organization: this.organizationData.id,
        type: ['payment', 'recharge', 'register', 'face_collect']
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.setQrCodeData(res.data.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设置数据
    setQrCodeData(list) {
      if (list && list.length) {
        this.qrCodeList = list.map(v => {
          // v.label = v.type === 'payment' ? '收款二维码' : '充值二维码'
          // v.radioLabel = v.type === 'payment' ? '消费收款码' : '充值收款码'
          v.spec_type = 50
          v.qrcode_options = {
            width: v.spec_type * 5,
            height: v.spec_type * 5
          }
          v.selectRadio = v.type + '_h5'
          // 收款码
          if (v.type === 'payment') {
            v.radioList = []
            v.label = '收款二维码'
            if (v.h5_url) {
              let query = getQueryObject(v.h5_url)
              query.organization_name = this.labelName
              v.qr_code = v.h5_url.split('?')[0] + '?' + paramsToUrlQuerys(query, true)
              v.radioList.push({ label: '消费收款码-H5', value: v.type + '_h5', url: v.qr_code })
            }
            // if (v.mini_url) {
            //   let query = getQueryObject(v.mini_url)
            //   query.organization_name = this.labelName
            //   let qrCode = v.mini_url.split('?')[0] + '?' + paramsToUrlQuerys(query, true)
            //   if (!v.qr_code) v.qr_code = qrCode
            //   v.radioList.push({ label: '消费收款码-小程序', value: v.type + '_mini', url: qrCode })
            // }
          }
          // 充值收款
          if (v.type === 'recharge') {
            v.radioList = []
            v.label = '充值二维码'
            if (v.h5_url) {
              let query = getQueryObject(v.h5_url)
              query.organization_name = this.labelName
              v.qr_code = v.h5_url.split('?')[0] + '?' + paramsToUrlQuerys(query, true)
              v.radioList.push({ label: '充值收款-H5', value: v.type + '_h5', url: v.qr_code })
            }
            if (v.mini_url) {
              let query = getQueryObject(v.mini_url)
              query.organization_name = this.labelName
              let qrCode = v.mini_url.split('?')[0] + '?' + paramsToUrlQuerys(query, true)
              if (!v.qr_code) v.qr_code = qrCode
              v.radioList.push({ label: '充值收款-小程序', value: v.type + '_mini', url: qrCode })
            }
          }
          // 自注册
          if (v.type === 'register') {
            v.radioList = []
            v.label = '用户自注册二维码'
            if (v.h5_url) {
              let query = getQueryObject(v.h5_url)
              query.organization_name = this.labelName
              v.qr_code = v.h5_url.split('?')[0] + '?' + paramsToUrlQuerys(query, true)
              v.radioList.push({ label: '用户自注册', value: v.type + '_h5', url: v.qr_code })
            }
            // if (v.mini_url) {
            //   let query = getQueryObject(v.mini_url)
            //   query.organization_name = this.labelName
            //   let qrCode = v.mini_url.split('?')[0] + '?' + paramsToUrlQuerys(query, true)
            //   if (!v.qr_code) v.qr_code = qrCode
            //   v.radioList.push({ label: '充值收款-小程序', value: v.type + '_mini', url: qrCode })
            // }
          }
          // 人脸采集
          if (v.type === 'face_collect') {
            v.radioList = []
            v.label = '人脸采集二维码'
            if (v.h5_url) {
              let query = getQueryObject(v.h5_url)
              query.organization_name = this.labelName
              v.qr_code = v.h5_url.split('?')[0] + '?' + paramsToUrlQuerys(query, true)
              v.radioList.push({ label: '人脸采集二维码-H5', value: v.type + '_h5', url: v.qr_code })
            }
            // if (v.mini_url) {
            //   let query = getQueryObject(v.mini_url)
            //   query.organization_name = this.labelName
            //   let qrCode = v.mini_url.split('?')[0] + '?' + paramsToUrlQuerys(query, true)
            //   if (!v.qr_code) v.qr_code = qrCode
            //   v.radioList.push({ label: '充值收款-小程序', value: v.type + '_mini', url: qrCode })
            // }
          }
          return v
        })
      }
    },
    // 设置规格
    changeSpecHandle(item) {
      item.qrcode_options = {
        width: item.spec_type * 5,
        height: item.spec_type * 5
      }
    },
    // 二维码类型change
    changeQrcoeType(e, item) {
      let url = ''
      item.radioList.forEach(v => {
        if (v.value === e) {
          url = v.url
        }
      })
      this.$set(item, 'qr_code', url)
    }
  }
}
</script>

<style lang="scss">
.download-qrcode {
  position: relative;
  // width: 300px;
  text-align: center;
  .margin-top-20{
    margin-top: 20px;
  }
}
</style>
