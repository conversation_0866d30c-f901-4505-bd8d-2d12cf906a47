<template>
  <!--这个详情是根据AddConsumptionRules 来的有些我也看不懂-->
  <el-drawer :visible.sync="visible" :title="title" :loading.sync="isLoading" @close="handleClose" size="678px"
    append-to-body modal-append-to-body :wrapperClosable="false" class="ps-el-drawer">
    <div class="dialog-content m-t-20 m-l-20" v-loading="isLoading">
      <!-- 名称 -->
      <div class="detail-item">
        <div class="detail-label">名称：</div>
        <div class="detail-content">{{ formData.name }}</div>
      </div>

      <!-- 基本设置 -->
      <div class="l-title clearfix">
        <span class="float-l min-title-h">基本设置</span>
      </div>
      <div class="form-line margin-button"></div>

      <!-- 适用分组 -->
      <div class="detail-item">
        <div class="detail-label">适用分组：</div>
        <div class="detail-content">
          <el-tag v-for="(item, index) in formData.groupNos" :key="index" size="small" class="m-r-10">{{ item
            }}</el-tag>
        </div>
      </div>

      <!-- 适用消费点 -->
      <div class="detail-item">
        <div class="detail-label">适用消费点：</div>
        <div class="detail-content">
          <el-tag v-for="(item, index) in formData.organization" :key="index" size="small" class="m-r-10">{{ item
            }}</el-tag>
        </div>
      </div>

      <!-- 支付限制 -->
      <div class="detail-item">
        <div class="detail-label">支付限制：</div>
        <div class="detail-content">
          <el-tag v-for="(item, index) in getWalletNameList(formData.walletType)" :key="'wallet-' + index" size="small"
            class="m-r-10">{{ item }}</el-tag>
          /
          <el-tag v-for="(item, index) in getPaywayNameList(formData.payway)" :key="'payway-' + index" size="small"
            class="m-r-10">{{ item }}</el-tag>
        </div>
      </div>

      <!-- 日期限制 -->
      <div class="detail-item">
        <div class="detail-label">日期限制：</div>
        <div class="detail-content">
          <div v-if="formData.exceptWeekday && formData.exceptWeekday.length">
            <span>
              {{ getWeekDayText(formData.exceptWeekday) }}
            </span>
          </div>
          <div v-if="formData.exceptDateRange && formData.exceptDateRange.length">
            <el-tag v-for="(time, index) in formData.exceptDateRange" :key="index" type="info" class="m-r-10 m-t-5">
              {{ time.join('~') }}
            </el-tag>
          </div>
          <span v-if="(!formData.exceptDateRange || formData.exceptDateRange.length === 0) && (!formData.exceptWeekday || formData.exceptWeekday.length === 0)">无限制</span>
        </div>
      </div>

      <!-- 消费类型 -->
      <div class="detail-item">
        <div class="detail-label">消费类型：</div>
        <div class="detail-content">{{ formData.consumeType === 0 ? '计次' : '扣费' }}</div>
      </div>

      <!-- 消费规则 -->
      <div class="l-title clearfix">
        <span class="float-l min-title-h">消费规则</span>
      </div>
      <div class="form-line margin-button"></div>

      <!-- 计次规则 -->
      <template v-if="formData.consumeType === 0">
        <div class="item-box-wrapper" v-for="(item, i) in formData.countList" :key="i">
          <!-- 餐段限制 -->
          <div class="detail-item">
            <div class="detail-label-big">餐段限制：</div>
            <div class="detail-content">
              <el-tag v-for="(meal, index) in getMealNameList(item.meal)" :key="index" size="small" class="m-r-10">{{
                meal }}</el-tag>
            </div>
          </div>

          <!-- 说明 -->
          <div class="detail-tip">说明：餐段可自由组合，组合在一起的餐段共用一套规则，且已选择的餐段无法重复选择；</div>

          <!-- 可记账次数 -->
          <div class="detail-item">
            <div class="detail-label-big">可记账次数：</div>
            <div class="detail-content">
              {{ item.countType === '0' ? '不限制' : '上限' + item.countNum + '次' }}
            </div>
          </div>

          <!-- 次数达到上限时 -->
          <div class="detail-item" v-if="item.countType === '1'">
            <div class="detail-label-big">次数达到上限时：</div>
            <div class="detail-content">
              {{ item.reach === 'disable' ? '禁止消费' : '原价扣款' }}
            </div>
          </div>

          <div class="form-line margin-button"></div>
        </div>
      </template>

      <!-- 扣费规则 -->
      <template v-if="formData.consumeType === 1">
        <div class="item-box-wrapper" v-for="(item, i) in formData.deductionList" :key="i">
          <!-- 餐段限制 -->
          <div class="detail-item">
            <div class="detail-label-big">餐段限制：</div>
            <div class="detail-content">
              <el-tag v-for="(meal, index) in getMealNameList(item.meal)" :key="index" size="small" class="m-r-10">{{
                meal }}</el-tag>
            </div>
          </div>

          <!-- 说明 -->
          <div class="detail-tip">说明：餐段可自由组合，组合在一起的餐段共用一套规则，且已选择的餐段无法重复选择；若选择全天，则无法再选择其他餐段。</div>

          <!-- 扣费类型 -->
          <div class="detail-item">
            <div class="detail-label-big">扣费类型：</div>
            <div class="detail-content">
              {{ getDeductionTypeText(item.deductionType) }}
            </div>
          </div>

          <!-- 固定金额 (当扣费类型为"固定"时) -->
          <template v-if="item.deductionType === 'GD'">
            <div class="detail-item">
              <div class="detail-label-big">固定金额：</div>
              <div class="detail-content">
                <div v-for="(fixed, index) in item.fixedList" :key="index" class="m-b-5">
                  {{ fixed.name }}: {{ fixed.value ? fixed.value + fixed.unit : '原价' }}
                </div>
              </div>
            </div>

            <!-- 次数限制 -->
            <div class="detail-item">
              <div class="detail-label-big">次数限制：</div>
              <div class="detail-content">
                {{ item.fixedCountType === '0' ? '不限制' : '最多固定金额消费' + item.fixedLimit + '次' }}
              </div>
            </div>

            <!-- 次数达到上限时 -->
            <div class="detail-item" v-if="item.fixedCountType === '1'">
              <div class="detail-label-big">次数达到上限时：</div>
              <div class="detail-content">
                {{ item.fixedMaxCountType === 'disable' ? '禁止消费' : '原价扣款' }}
              </div>
            </div>
          </template>

          <!-- 折扣 (当扣费类型为"折扣"时) -->
          <template v-if="item.deductionType === 'ZK'">
            <!-- 订单金额 -->
            <div class="detail-item">
              <div class="detail-label-big">订单金额：</div>
              <div class="detail-content">
                {{ getDiscountOrderTypeText(item.discountFeeType) }}
              </div>
            </div>

            <!-- 扣费方式 -->
            <div class="detail-item">
              <div class="detail-label-big">扣费方式：</div>
              <div class="detail-content">
                {{ item.debit_type === 'normal' ? '普通折扣' : '按取餐方式' }}
              </div>
            </div>

            <!-- 取餐方式相关 -->
            <template v-if="item.debit_type === 'take_meal_type'">
              <div class="detail-item">
                <div class="detail-label-big">取餐方式：</div>
                <div class="detail-content">
                  <!-- 堂食取餐 -->
                  <div v-if="item.isCanteen">
                    <div class="m-b-5">堂食取餐：启用</div>
                    <el-tag v-for="type in item.canteen_take_meal_type.take_out_type" :key="type" size="small"
                      class="m-r-10">
                      {{ takeMealTypeText(type) }}
                    </el-tag>
                  </div>

                  <!-- 外卖 -->
                  <div v-if="item.isTakeOut" class="m-t-10">
                    <div class="m-b-5">外卖：启用</div>
                    <el-tag v-for="type in item.takeOut_take_meal_type.take_out_type" :key="type" size="small"
                      class="m-r-10">
                      {{ takeMealTypeText(type) }}
                    </el-tag>
                  </div>
                </div>
              </div>

              <!-- 仅首单折扣 -->
              <div class="detail-item"
                v-if="item.canteen_take_meal_type.take_out_type.length || item.takeOut_take_meal_type.take_out_type.length">
                <div class="detail-label-big">仅首单折扣：</div>
                <div class="detail-content">
                  {{ item.takeMealFirst ? '是' : '否' }}
                </div>
              </div>
            </template>

            <!-- 累计消费 -->
            <div v-if="item.discountFeeType !== 'normal'" class="detail-item">
              <div class="detail-label-big">{{ `餐段内${item.discountFeeType === 'once' ? '' : '累计'}消费` }}：</div>
              <div class="detail-content">
                {{ getDiscountMealConsumptionText(item.discountMealConsumption) }} {{ item.discountFee }}元
              </div>
            </div>

            <!-- 折扣范围 -->
            <div v-if="item.discountFeeType !== 'normal' && item.discountMealConsumption === 'gt'" class="detail-item">
              <div class="detail-label-big">折扣范围：</div>
              <div class="detail-content">
                {{ getDiscountRangText(item.discountRang) }}
              </div>
            </div>

            <!-- 普通折扣时的扣费类型 -->
            <div v-if="item.debit_type === 'normal'" class="detail-item">
              <div class="detail-label-big">扣费类型：</div>
              <div class="detail-content">
                <div v-for="(discount, index) in item.discountList" :key="index" class="m-b-5">
                  {{ discount.name }}: {{ discount.value ? discount.value + discount.unit : '原价' }}
                </div>
              </div>
            </div>

            <!-- 按取餐方式的折扣设置 -->
            <template v-if="item.debit_type === 'take_meal_type'">
              <!-- 堂食折扣 -->
              <template v-if="item.isCanteen">
                <div v-for="(take_meal_discount) in item.canteen_take_meal_type.discountList"
                  :key="'canteen-' + take_meal_discount.meal_type" class="detail-item">
                  <div class="detail-label-big">{{ takeMealTypeText(take_meal_discount.meal_type) }}：</div>
                  <div class="detail-content">
                    <div v-for="(discount, index) in take_meal_discount.discount" :key="index" class="m-b-5">
                      {{ discount.name }}: {{ discount.value ? discount.value + discount.unit : '原价' }}
                    </div>

                    <!-- 每人最大折扣金额 -->
                    <div class="m-t-10 m-b-5">每人最大折扣金额：{{
                      getDiscountMaxFeeTypeText(take_meal_discount.discountMaxFeeType,
                      take_meal_discount.discountMaxFee) }}</div>

                    <!-- 每人最大折扣金额上限 -->
                    <div v-if="take_meal_discount.discountMaxFeeType === 'o_once'" class="m-b-5">
                      每人最大折扣金额上限：{{ take_meal_discount.discountMaxOnceFeeType === 'o_once' ? '整单原价扣款' : '超出部分原价扣款' }}
                    </div>
                    <div v-if="take_meal_discount.discountMaxFeeType === 't_all'" class="m-b-5">
                      每人最大折扣金额上限：{{ take_meal_discount.discountMaxOnceFeeType === 't_once' ? '整单原价扣款' : '超出部分原价扣款' }}
                    </div>

                    <!-- 限额 -->
                    <div v-if="take_meal_discount.discountMaxFeeType === 'normal'" class="m-b-5">
                      限额：{{ take_meal_discount.maxLimitType === '0' ? '不限额' : '最高' + take_meal_discount.maxLimitFee +
                      '元' }}
                    </div>
                  </div>
                </div>
              </template>

              <!-- 外卖折扣 -->
              <template v-if="item.isTakeOut">
                <div v-for="(take_meal_discount) in item.takeOut_take_meal_type.discountList"
                  :key="'takeout-' + take_meal_discount.meal_type" class="detail-item">
                  <div class="detail-label-big">{{ takeMealTypeText(take_meal_discount.meal_type) }}：</div>
                  <div class="detail-content">
                    <div v-for="(discount, index) in take_meal_discount.discount" :key="index" class="m-b-5">
                      {{ discount.name }}: {{ discount.value ? discount.value + discount.unit : '原价' }}
                    </div>

                    <!-- 每人最大折扣金额 -->
                    <div class="m-t-10 m-b-5">每人最大折扣金额：{{
                      getDiscountMaxFeeTypeText(take_meal_discount.discountMaxFeeType,
                      take_meal_discount.discountMaxFee) }}</div>

                    <!-- 每人最大折扣金额上限 -->
                    <div v-if="take_meal_discount.discountMaxFeeType === 'o_once'" class="m-b-5">
                      每人最大折扣金额上限：{{ take_meal_discount.discountMaxOnceFeeType === 'o_once' ? '整单原价扣款' : '超出部分原价扣款' }}
                    </div>
                    <div v-if="take_meal_discount.discountMaxFeeType === 't_all'" class="m-b-5">
                      每人最大折扣金额上限：{{ take_meal_discount.discountMaxOnceFeeType === 't_once' ? '整单原价扣款' : '超出部分原价扣款' }}
                    </div>

                    <!-- 限额 -->
                    <div v-if="take_meal_discount.discountMaxFeeType === 'normal'" class="m-b-5">
                      限额：{{ take_meal_discount.maxLimitType === '0' ? '不限额' : '最高' + take_meal_discount.maxLimitFee +
                      '元' }}
                    </div>
                  </div>
                </div>
              </template>
            </template>

            <!-- 普通折扣的每人最大折扣金额 -->
            <div v-if="item.debit_type === 'normal'" class="detail-item">
              <div class="detail-label-big">每人最大折扣金额：</div>
              <div class="detail-content">
                {{ getDiscountMaxFeeTypeText(item.discountMaxFeeType, item.discountMaxFee) }}
              </div>
            </div>

            <!-- 每人最大折扣金额上限 -->
            <div v-if="item.debit_type === 'normal' && item.discountMaxFeeType === 'o_once'" class="detail-item">
              <div class="detail-label-big">每人最大折扣金额上限：</div>
              <div class="detail-content">
                {{ item.discountMaxOnceFeeType === 'o_once' ? '整单原价扣款' : '超出部分原价扣款' }}
              </div>
            </div>
            <div v-if="item.debit_type === 'normal' && item.discountMaxFeeType === 't_all'" class="detail-item">
              <div class="detail-label-big">每人最大折扣金额上限：</div>
              <div class="detail-content">
                {{ item.discountMaxOnceFeeType === 't_once' ? '整单原价扣款' : '超出部分原价扣款' }}
              </div>
            </div>
          </template>

          <!-- 限额 -->
          <div
            v-if="(item.deductionType === 'GD' && item.fixedMaxCountType === 'original') || (item.deductionType === 'ZK' && item.discountMaxFeeType === 'normal' && item.debit_type === 'normal')"
            class="detail-item">
            <div class="detail-label-big">限额：</div>
            <div class="detail-content">
              {{ item.maxLimitType === '0' ? '不限额' : '最高' + item.maxLimitFee + '元' }}
            </div>
          </div>

          <!-- 使用餐补 -->
          <div class="detail-item">
            <div class="detail-label-big">使用餐补：</div>
            <div class="detail-content">{{ item.useMealSubsidy ? '是' : '否' }}</div>
          </div>

          <!-- 餐补金额 -->
          <div v-if="item.useMealSubsidy" class="detail-item">
            <div class="detail-label-big">餐补金额：</div>
            <div class="detail-content">
              <div v-if="item.mealSubsidyType === 'total'">
                累计餐补：{{ item.mealSubsidyFee }}元
              </div>
              <div v-else>
                单次餐补：
                <div v-for="(subsidy, index) in item.subsidyList" :key="index" class="m-b-5">
                  {{ subsidy.name }}: {{ subsidy.value ? subsidy.value + subsidy.unit : '0元' }}
                </div>
              </div>
            </div>
          </div>

          <!-- 消费限制 (当扣费类型为"无折扣"时) -->
          <div v-if="item.deductionType === 'WZK'" class="detail-item">
            <div class="detail-label-big">消费限制：</div>
            <div class="detail-content">
              <div v-if="item.consumtionType === 'normal'">不限制</div>
              <div v-else-if="item.consumtionType === 'once'">单次最高消费{{ item.countUpperFee }}元</div>
              <div v-else-if="item.consumtionType === 'day'">每日最高消费{{ item.dayUpperFee }}元</div>
            </div>
          </div>

          <div class="form-line margin-button"></div>
        </div>
      </template>

      <!-- 备注 -->
      <div class="detail-item">
        <div class="detail-label">备注：</div>
        <div class="detail-content">{{ formData.tip || '无' }}</div>
      </div>
    </div>
    <div class="ps-el-drawer-footer">
      <el-button :disabled="isBtnLoading" class="ps-cancel-btn m-l-20" @click="clickCancleHandle">
        {{ dialogType == 'add' ? '取消' : '关闭' }}
      </el-button>
      <el-button class="ps-btn" type="primary" @click="clickConfirmHandle" v-if="dialogType === 'add'"
        :disabled="isBtnLoading" v-loading="isBtnLoading">
        确定
      </el-button>
    </div>
  </el-drawer>
  <!-- end -->
</template>

<script>
import { WALLET_LIST, MEAL_LIST } from "../constants"

export default {
  name: 'ConsumptionRulesDetailDialog',
  props: {
    loading: Boolean,
    isshow: Boolean,
    title: {
      type: String,
      default: '详情'
    },
    limitId: {
      type: String,
      default: ''
    },
    dialogType: {
      type: String,
      default: 'default'
    },
    confirm: Function
  },
  data() {
    return {
      isLoading: false,
      isBtnLoading: false,
      formData: {
        name: '',
        groupNos: [],
        organization: [],
        walletType: [],
        payway: [],
        consumeType: 0,
        exceptWeekday: [],
        exceptDateRange: [],
        countList: [],
        deductionList: [],
        tip: ''
      },
      walletList: WALLET_LIST,
      mealList: MEAL_LIST,
      paywayList: [],
      weekList: [
        { name: '周一', value: 'Mon' },
        { name: '周二', value: 'Tue' },
        { name: '周三', value: 'Wed' },
        { name: '周四', value: 'Thu' },
        { name: '周五', value: 'Fri' },
        { name: '周六', value: 'Sat' },
        { name: '周日', value: 'Sun' }
      ],
      takeMealTypeKeys: {
        on_scene: '堂食',
        bale: '食堂自提',
        cupboard: '取餐柜自取',
        waimai: '外卖配送'
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        this.getDetailData()
      }
    }
  },
  mounted() {
    this.getPaywayList()
  },
  methods: {
    clickCancleHandle() {
      this.handleClose()
    },
    // 确认选择
    async clickConfirmHandle() {
      this.$emit('confirm', true)
    },
    // 关闭
    handleClose() {
      this.isLoading = false
      this.visible = false
      this.$emit('close', false)
    },
    // 获取详情数据
    async getDetailData() {
      if (!this.limitId) return

      this.isLoading = true
      try {
        const res = await this.$apis.apiBackgroundMarketingConsumeDetailGet({
          rule_no: this.limitId
        })

        if (res.code === 0) {
          this.setFormData(res.data)
        } else {
          this.$message.error(res.msg)
        }
      } catch (err) {
        this.$message.error(err.message || '获取详情失败')
      } finally {
        this.isLoading = false
      }
    },
    // 获取支付方式列表
    async getPaywayList() {
      try {
        const res = await this.$apis.apiBackgroundPaymentPayInfoGetCompanyPayinfoPost({
          company_id: this.$store.getters.userInfo.company_id
        })

        if (res.code === 0) {
          this.paywayList = res.data
        } else {
          this.$message.error(res.msg)
        }
      } catch (err) {
        this.$message.error(err.message || '获取支付方式失败')
      }
    },
    // 设置表单数据
    setFormData(data) {
      if (!data) {
        return
      }
      console.log("setFormData", data)
      // 处理组织和分组名字
      let orgsName = []
      let groupsName = []
      if (data.orgs && data.orgs.length > 0) {
        data.orgs.forEach(item => {
          if (typeof item === 'object') {
            for (let key in item) {
              orgsName.push(item[key])
            }
          }
        })
      }

      if (data.user_groups && data.user_groups.length > 0) {
        data.user_groups.forEach(item => {
          if (typeof item === 'object') {
            for (let key in item) {
              groupsName.push(item[key])
            }
          }
        })
      }

      // 基本数据
      this.formData = {
        name: data.name || '',
        ruleNo: data.rule_no || '',
        organization: orgsName,
        groupNos: groupsName,
        walletType: data.wallet_type || [],
        payway: data.payway || [],
        consumeType: data.consume_type,
        exceptWeekday: data.weekday || [],
        exceptDateRange: data.except_date_range ? data.except_date_range.map(time => {
          return [this.parseTime(time.start_date), this.parseTime(time.end_date)]
        }) : [],
        tip: data.tip || '',
        countList: [],
        deductionList: []
      }

      // 处理规则数据
      if (data.rule && data.rule.length) {
        data.rule.forEach(rule => {
          if (data.consume_type === 0) {
            // 计次规则
            this.formData.countList.push({
              meal: rule.meal || [],
              countType: rule.discount_rule.count ? '1' : '0',
              countNum: rule.discount_rule.count || '',
              reach: rule.discount_rule.reach || 'disable'
            })
          } else {
            // 扣费规则
            const deduction = this.formatDeductionRule(rule)
            this.formData.deductionList.push(deduction)
          }
        })
      }
    },
    // 格式化扣费规则
    formatDeductionRule(rule) {
      const deduction = {
        meal: rule.meal || [],
        deductionType: rule.rule_type,
        useMealSubsidy: rule.subsidy !== 'close',
        mealSubsidyType: rule.subsidy !== 'close' ? rule.subsidy : 'total',
        mealSubsidyFee: this.divide(rule.subsidy_rule?.total || 0),
        // eslint-disable-next-line camelcase
        consumtionType: rule.discount_rule?.consume_limit || 'normal',
        // eslint-disable-next-line camelcase
        countUpperFee: rule.discount_rule?.consume_limit === 'once' ? this.divide(rule.discount_rule.amount || 0) : '',
        // eslint-disable-next-line camelcase
        dayUpperFee: rule.discount_rule?.consume_limit === 'day' ? this.divide(rule.discount_rule.amount || 0) : '',
        discountMaxOnceFeeType: '',
        debit_type: rule.debit_type || 'normal',
        isCanteen: false,
        isTakeOut: false,
        canteen_take_meal_type: {
          take_out_type: [],
          discountList: []
        },
        takeOut_take_meal_type: {
          take_out_type: [],
          discountList: []
        },
        // eslint-disable-next-line camelcase
        takeMealFirst: !!rule.discount_rule?.take_meal_first
      }

      // 处理餐补数据
      let subsidyList = []
      if (deduction.useMealSubsidy && deduction.mealSubsidyType === 'once') {
        if (rule.subsidy_rule?.rule) {
          subsidyList = rule.subsidy_rule.rule.map((v, i) => {
            return { index: i + 1, value: v > -1 ? this.divide(v) : '', name: `第${i + 1}次`, unit: '元' }
          })
        }
        subsidyList.push({
          index: -1,
          value: rule.subsidy_rule?.after > -1 ? this.divide(rule.subsidy_rule.after) : '',
          name: '之后每次消费',
          unit: '元'
        })
      } else {
        subsidyList = [
          { index: 1, value: '', name: '第1次', unit: '元' },
          { index: -1, value: '', name: '之后每次消费', unit: '元' }
        ]
      }
      deduction.subsidyList = subsidyList

      // 根据扣费类型添加不同的属性
      switch (rule.rule_type) {
        case 'WZK': // 无折扣
          Object.assign(deduction, this.formatWZKRule(rule))
          break
        case 'GD': // 固定
          Object.assign(deduction, this.formatGDRule(rule))
          break
        case 'ZK': // 折扣
          Object.assign(deduction, this.formatZKRule(rule))
          break
      }

      return deduction
    },
    // 格式化无折扣规则
    formatWZKRule(rule) {
      return {
        fixedList: [
          { index: 1, value: '', name: '第1次', unit: '元' },
          { index: -1, value: '', name: '之后每次消费', unit: '元' }
        ],
        fixedCountType: '0',
        fixedLimit: '',
        fixedMaxCountType: 'disable',
        maxLimitType: '0',
        maxLimitFee: '',
        discountFeeType: 'normal',
        discountMealConsumption: 'lte',
        discountRang: '',
        discountFee: '',
        discountMaxFeeType: 'normal',
        discountMaxOnceFeeType: 'o_once',
        discountMaxFee: '',
        discountList: [
          { index: 1, value: '', name: '第1次', unit: '%' },
          { index: -1, value: '', name: '之后每次消费', unit: '%' }
        ]
      }
    },
    // 格式化固定规则
    formatGDRule(rule) {
      const result = {
        fixedLimit: rule.discount_rule?.count || '',
        fixedCountType: (rule.discount_rule?.count !== null && rule.discount_rule?.count >= 0) ? '1' : '0',
        fixedMaxCountType: rule.discount_rule?.reach || 'disable',
        maxLimitType: rule.discount_rule?.amount ? '1' : '0',
        maxLimitFee: rule.discount_rule?.amount ? this.divide(rule.discount_rule.amount) : '',
        discountFeeType: 'normal',
        discountMealConsumption: 'lte',
        discountRang: '',
        discountFee: '',
        discountMaxFeeType: 'normal',
        discountMaxOnceFeeType: 'o_once',
        discountMaxFee: '',
        discountList: [
          { index: 1, value: '', name: '第1次', unit: '%' },
          { index: -1, value: '', name: '之后每次消费', unit: '%' }
        ]
      }

      // 处理固定金额列表
      let fixedList = []
      if (rule.discount_rule?.rule) {
        fixedList = rule.discount_rule.rule.map((v, i) => {
          return { index: i + 1, value: v > -1 ? this.divide(v) : '', name: `第${i + 1}次`, unit: '元' }
        })
      }
      fixedList.push({
        index: -1,
        value: rule.discount_rule?.after > -1 ? this.divide(rule.discount_rule.after) : '',
        name: '之后每次消费',
        unit: '元'
      })
      result.fixedList = fixedList

      return result
    },
    // 格式化折扣规则
    formatZKRule(rule) {
      const result = {
        // eslint-disable-next-line camelcase
        discountFeeType: rule.discount_rule?.order_limit || 'normal',
        discountMaxFeeType: 'normal',
        discountMaxFee: '',
        discountMealConsumption: 'lte',
        discountRang: '',
        discountFee: rule.discount_rule?.money ? this.divide(rule.discount_rule.money) : '',
        fixedMaxCountType: rule.discount_rule?.reach || 'disable',
        maxLimitType: rule.discount_rule?.amount ? '1' : '0',
        maxLimitFee: rule.discount_rule?.amount ? this.divide(rule.discount_rule.amount) : '',
        consumtionType: 'normal',
        countUpperFee: '',
        dayUpperFee: '',
        fixedList: [
          { index: 1, value: '', name: '第1次', unit: '元' },
          { index: -1, value: '', name: '之后每次消费', unit: '元' }
        ],
        fixedCountType: '0',
        fixedLimit: '',
        canteen_take_meal_type: {
          take_out_type: [],
          discountList: []
        },
        takeOut_take_meal_type: {
          take_out_type: [],
          discountList: []
        }
      }

      // 处理最大折扣金额类型
      // eslint-disable-next-line camelcase
      if (rule.discount_rule?.consume_limit && rule.discount_rule.consume_limit === 'o_all') {
        result.discountMaxFeeType = 'o_once'
        result.discountMaxOnceFeeType = 'o_all'
      } else {
        // eslint-disable-next-line camelcase
        result.discountMaxFeeType = rule.discount_rule?.consume_limit || 'normal'
        if (result.discountMaxFeeType === 't_once') {
          result.discountMaxFeeType = 't_all'
        }
        if (result.discountMaxFeeType !== 'normal') {
          // eslint-disable-next-line camelcase
          result.discountMaxOnceFeeType = rule.discount_rule?.consume_limit
        }
      }

      // 设置最大折扣金额
      if (result.discountMaxFeeType !== 'normal') {
        result.discountMaxFee = rule.discount_rule?.fee ? this.divide(rule.discount_rule.fee) : ''
      }

      // 设置餐段内累计消费
      if (result.discountFeeType !== 'normal') {
        result.discountMealConsumption = rule.discount_rule?.option || 'lte'
        // eslint-disable-next-line camelcase
        result.discountRang = rule.discount_rule?.discount_rang || ''
      }

      // 处理折扣列表
      let discountList = []
      if (rule.debit_type !== 'take_meal_type') {
        if (rule.discount_rule?.rule) {
          discountList = rule.discount_rule.rule.map((v, i) => {
            return { index: i + 1, value: v > -1 ? v : '', name: `第${i + 1}次`, unit: '%' }
          })
        }
        discountList.push({
          index: -1,
          value: rule.discount_rule?.after > -1 ? rule.discount_rule.after : '',
          name: '之后每次消费',
          unit: '%'
        })
      } else {
        discountList = [
          { index: 1, value: '', name: '第1次', unit: '%' },
          { index: -1, value: '', name: '之后每次消费', unit: '%' }
        ]
      }
      result.discountList = discountList

      // 处理取餐方式
      // eslint-disable-next-line camelcase
      if (rule.debit_type === 'take_meal_type' && rule.discount_rule?.take_meal_type_rule) {
        const takeMealTypes = Object.keys(rule.discount_rule.take_meal_type_rule)
        const canteenKey = ['on_scene', 'bale']

        takeMealTypes.forEach(type => {
          const mealTypeItem = rule.discount_rule.take_meal_type_rule[type]

          if (canteenKey.includes(type)) {
            // 堂食
            result.isCanteen = true
            console.log("result.canteen_take_meal_type", result.canteen_take_meal_type)
            if (result.canteen_take_meal_type && result.canteen_take_meal_type.take_out_type) {
              result.canteen_take_meal_type.take_out_type.push(type)
            }
            // 处理折扣列表
            let discountList = []
            if (mealTypeItem.rule) {
              discountList = mealTypeItem.rule.map((v, i) => {
                return { index: i + 1, value: v > -1 ? v : '', name: `第${i + 1}次`, unit: '%' }
              })
            }
            discountList.push({
              index: -1,
              value: mealTypeItem.after > -1 ? mealTypeItem.after : '',
              name: '之后每次消费',
              unit: '%'
            })

            // 创建折扣配置项
            const discountListItem = {
              meal_type: type,
              discount: discountList,
              discountMaxFeeType: 'normal',
              discountMaxFee: '',
              discountMaxOnceFeeType: '',
              maxLimitType: mealTypeItem.amount ? '1' : '0',
              maxLimitFee: mealTypeItem.amount ? this.divide(mealTypeItem.amount) : ''
            }

            // 处理每人最大折扣金额类型
            if (mealTypeItem.consume_limit && mealTypeItem.consume_limit === 'o_all') {
              discountListItem.discountMaxFeeType = 'o_once'
              discountListItem.discountMaxOnceFeeType = 'o_all'
            } else {
              discountListItem.discountMaxFeeType = mealTypeItem.consume_limit || 'normal'
              if (discountListItem.discountMaxFeeType === 't_once') {
                discountListItem.discountMaxFeeType = 't_all'
              }
              if (discountListItem.discountMaxFeeType !== 'normal') {
                discountListItem.discountMaxOnceFeeType = mealTypeItem.consume_limit
              }
            }

            // 设置最大折扣金额
            if (discountListItem.discountMaxFeeType !== 'normal') {
              discountListItem.discountMaxFee = mealTypeItem.fee ? this.divide(mealTypeItem.fee) : ''
            }
            if (result.canteen_take_meal_type && result.canteen_take_meal_type.discountList) {
              result.canteen_take_meal_type.discountList.push(discountListItem)
            }
          } else {
            // 外卖
            result.isTakeOut = true
            result.takeOut_take_meal_type.take_out_type.push(type)

            // 处理折扣列表
            let discountList = []
            if (mealTypeItem.rule) {
              discountList = mealTypeItem.rule.map((v, i) => {
                return { index: i + 1, value: v > -1 ? v : '', name: `第${i + 1}次`, unit: '%' }
              })
            }
            discountList.push({
              index: -1,
              value: mealTypeItem.after > -1 ? mealTypeItem.after : '',
              name: '之后每次消费',
              unit: '%'
            })

            // 创建折扣配置项
            const discountListItem = {
              meal_type: type,
              discount: discountList,
              discountMaxFeeType: 'normal',
              discountMaxFee: '',
              discountMaxOnceFeeType: '',
              maxLimitType: mealTypeItem.amount ? '1' : '0',
              maxLimitFee: mealTypeItem.amount ? this.divide(mealTypeItem.amount) : ''
            }

            // 处理每人最大折扣金额类型
            if (mealTypeItem.consume_limit && mealTypeItem.consume_limit === 'o_all') {
              discountListItem.discountMaxFeeType = 'o_once'
              discountListItem.discountMaxOnceFeeType = 'o_all'
            } else {
              discountListItem.discountMaxFeeType = mealTypeItem.consume_limit || 'normal'
              if (discountListItem.discountMaxFeeType === 't_once') {
                discountListItem.discountMaxFeeType = 't_all'
              }
              if (discountListItem.discountMaxFeeType !== 'normal') {
                discountListItem.discountMaxOnceFeeType = mealTypeItem.consume_limit
              }
            }

            // 设置最大折扣金额
            if (discountListItem.discountMaxFeeType !== 'normal') {
              discountListItem.discountMaxFee = mealTypeItem.fee ? this.divide(mealTypeItem.fee) : ''
            }

            result.takeOut_take_meal_type.discountList.push(discountListItem)
          }
        })
      }

      return result
    },
    // 获取支付钱包名称列表
    getWalletNameList(walletTypes) {
      if (!walletTypes || !walletTypes.length) return []

      return walletTypes.map(type => {
        const wallet = this.walletList.find(w => w.key === type)
        return wallet ? wallet.name : type
      })
    },
    // 获取支付方式名称列表
    getPaywayNameList(payways) {
      if (!payways || !payways.length) return []

      return payways.map(payway => {
        const pay = this.paywayList.find(p => p.payway === payway)
        return pay ? pay.name : payway
      })
    },
    // 获取餐段名称列表
    getMealNameList(meals) {
      if (!meals || !meals.length) return []

      return meals.map(meal => {
        const m = this.mealList.find(m => m.value === meal)
        return m ? m.name : meal
      })
    },
    // 获取周几文本
    getWeekDayText(weekdays) {
      if (!weekdays || !weekdays.length) return '无限制'

      return weekdays.map(day => {
        const week = this.weekList.find(w => w.value === day)
        return week ? week.name : day
      }).join('、')
    },
    // 获取扣费类型文本
    getDeductionTypeText(type) {
      const types = {
        'WZK': '无折扣',
        'GD': '固定',
        'ZK': '折扣'
      }
      return types[type] || type
    },
    // 获取订单金额类型文本
    getDiscountOrderTypeText(type) {
      const types = {
        'normal': '不限制',
        'once': '不累计',
        'day': '累计'
      }
      return types[type] || type
    },
    // 获取餐段内消费比较符文本
    getDiscountMealConsumptionText(type) {
      const types = {
        'lte': '小于等于',
        'gt': '大于'
      }
      return types[type] || type
    },
    // 获取折扣范围文本
    getDiscountRangText(type) {
      const types = {
        'out': '仅超出部分',
        'all': '整笔订单'
      }
      return types[type] || type
    },
    // 获取最大折扣金额类型文本
    getDiscountMaxFeeTypeText(type, fee) {
      const types = {
        'normal': '不限制',
        'o_once': '单笔' + (fee ? fee + '元' : ''),
        't_all': '合计' + (fee ? fee + '元' : '')
      }
      return types[type] || type
    },
    // 获取取餐方式文本
    takeMealTypeText(type) {
      return this.takeMealTypeKeys[type] || type
    },
    // 时间格式化
    parseTime(time, format = '{y}-{m}-{d}') {
      if (!time) return ''

      let date = new Date(time)

      const formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay()
      }

      return format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
        let value = formatObj[key]
        if (key === 'a') {
          return ['日', '一', '二', '三', '四', '五', '六'][value]
        }
        if (result.length > 0 && value < 10) {
          value = '0' + value
        }
        return value || 0
      })
    },
    // 除以100
    divide(value) {
      if (!value && value !== 0) return ''
      return (parseFloat(value) / 100).toFixed(2)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-right: 20px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;

  .detail-label {
    min-width: 100px;
    text-align: right;
    line-height: 32px;
    color: #606266;
  }
  .detail-label-big {
    min-width: 130px;
    text-align: right;
    line-height: 32px;
    color: #606266;
  }

  .detail-content {
    flex: 1;
    line-height: 32px;
    margin-left: 10px;
    color: #303133;
    word-break: break-word;
  }
}

.detail-tip {
  margin-left: 100px;
  margin-bottom: 15px;
  font-size: 13px;
  color: #909399;
  line-height: 1.5;
  padding-left: 10px;
}

.item-box-wrapper {
  position: relative;
  margin-bottom: 10px;
}

.form-line {
  height: 1px;
  width: 100%;
  background-color: #EBEEF5;
  margin: 10px 0;
}

.m-b-5 {
  margin-bottom: 5px;
}

.m-b-10 {
  margin-bottom: 10px;
}

.m-t-5 {
  margin-top: 5px;
}

.m-t-10 {
  margin-top: 10px;
}

.m-r-10 {
  margin-right: 10px;
}

.l-title {
  margin-top: 20px;
  margin-bottom: 10px;

  .min-title-h {
    font-size: 16px;
    font-weight: bold;
    position: relative;
    padding-left: 10px;
  }

  .min-title-h :before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background-color: #FF9B45;
    border-radius: 2px;
  }
}

.margin-button {
  margin-bottom: 15px;
}

:deep(.el-tag) {
  margin-bottom: 5px;
}
</style>
