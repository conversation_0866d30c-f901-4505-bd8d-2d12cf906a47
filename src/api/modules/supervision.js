import http from '@/utils/request'
export default {

  /**
   * ['晨检明细']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionCanteenManagementMorningCheckDetailPost  (param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/morning_check_detail', param)
  },

  /**
   * ['晨检明细导出']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionCanteenManagementMorningCheckDetailExportPost (param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/morning_check_detail_export', param)
  },

  /**
   * ['晨检汇总']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionCanteenManagementMorningCheckCollectPost (param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/morning_check_collect', param)
  },

  /**
   * ['晨检汇总导出']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionCanteenManagementMorningCheckCollectExportPost  (param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/morning_check_collect_export', param)
  },
  /**
   * ['智能预警 预警配置']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionWarnManageBusinessWarnListPost  (param) {
    return http.post('/api/background_fund_supervision/warn_manage/business_warn_list', param)
  },

  /**
   * ['智能预警 预警配置']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionWarnManageBusinessWarningDetailPost  (param) {
    return http.post('/api/background_fund_supervision/warn_manage/business_warning_detail', param)
  },
  /**
   * ['智能预警 预警配置修改']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionWarnManageBusinessWarningEditPost  (param) {
    return http.post('/api/background_fund_supervision/warn_manage/business_warning_edit', param)
  },
  /**
   * ['人员排班列表 修改']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionCanteenSafetyManagementModifyPersonSchedulePost  (param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/modify_person_schedule', param)
  },

  /**
 * ['人员排班列表']
 * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
 * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
 */
  apiBackgroundFundSupervisionCanteenSafetyManagementGetPersonSchedulePost  (param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/get_person_schedule', param)
  },
  /**
 * ['历史记录接口']
 * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
 * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
 */
  apiBackgroundFundSupervisionCanteenSafetyManagementGetScheduleHistoryPost  (param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/get_schedule_history', param)
  },
  // 留样记录
  apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordPost(data) {
    return http.post(
      "/api/background_store/retention_record/food_reserved_sample_record",
      data
    )
  },
  // 留样记录导出
  apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordExportPost(data) {
    return http.post(
      "/api/background_store/retention_record/food_reserved_sample_record_export",
      data
    )
  },
  // 获取操作人员人脸信息
  apiBackgroundFundSupervisionChannelCanteenManagementGetAccountFaceUrlPost(data) {
    return http.post(
      "/api/background_store/retention_record/get_account_face_url",
      data
    )
  },
  // 更新未入柜原因
  apiBackgroundFundSupervisionChannelCanteenManagementModifyNotEntryReasonPost(data) {
    return http.post(
      "/api/background_store/retention_record/modify_not_entry_reason",
      data
    )
  },
  // 更新未留样原因
  apiBackgroundFundSupervisionChannelCanteenManagementModifyNotReservedReasonPost(data) {
    return http.post(
      "/api/background_store/retention_record/modify_not_reserved_reason",
      data
    )
  },
  // 留样获取菜谱
  apiBackgroundStoreRetentionRecordMenuList(data) {
    return http.post(
      "/api/background_store/retention_record/menu_list",
      data
    )
  },

  // 陪餐记录
  apiBackgroundFundSupervisionChannelCanteenManagementMealAccompanyingListPost(data) {
    return http.post(
      "/api/background_fund_supervision/channel_canteen_management/meal_accompanying_list",
      data
    )
  },
  // 陪餐记录 导出
  apiBackgroundFundSupervisionChannelCanteenManagementMealAccompanyingListExportPost(data) {
    return http.post(
      "/api/background_fund_supervision/channel_canteen_management/meal_accompanying_list_export",
      data
    )
  },
  // 每日巡查数据列表
  apiBackgroundFundSupervisionDailyPatrolList(data) {
    return http.post(
      "/api/background_fund_supervision/daily_patrol/list",
      data
    )
  },
  // 获取项目配置列表
  apiBackgroundFundSupervisionDailyPatrolGetProjectList(data) {
    return http.post(
      "/api/background_fund_supervision/daily_patrol/get_project_list",
      data
    )
  },
  // 配置项目
  apiBackgroundFundSupervisionDailyPatrolProjectSettings(data) {
    return http.post(
      "/api/background_fund_supervision/daily_patrol/project_settings",
      data
    )
  },
  // 添加巡查记录
  apiBackgroundFundSupervisionDailyPatrolAdd(data) {
    return http.post(
      "/api/background_fund_supervision/daily_patrol/add",
      data
    )
  },
  // 删除巡查记录
  apiBackgroundFundSupervisionDailyPatrolDelete(data) {
    return http.post(
      "/api/background_fund_supervision/daily_patrol/delete",
      data
    )
  },
  // 团餐公司列表
  apiBackgroundAdminGroupMealCompanyList(data) {
    return http.post(
      "/api/background/admin/group_meal_company/list",
      data
    )
  },
  // 修改团餐公司名称
  apiBackgroundAdminGroupMealCompanyModify(data) {
    return http.post(
      "/api/background/admin/group_meal_company/modify",
      data
    )
  },
  // 团餐公司详情
  apiBackgroundAdminGroupMealCompanyDetails(data) {
    return http.post(
      "/api/background/admin/group_meal_company/details",
      data
    )
  },
  // 配餐公司列表
  apiBackgroundAdminCateringCompanyList(data) {
    return http.post(
      "/api/background/admin/catering_company/list",
      data
    )
  },
  // 修改配餐公司名称
  apiBackgroundAdminCateringCompanyModify(data) {
    return http.post(
      "/api/background/admin/catering_company/modify",
      data
    )
  },
  // 配餐公司详情
  apiBackgroundAdminCateringCompanyDetails(data) {
    return http.post(
      "/api/background/admin/catering_company/details",
      data
    )
  }
}
