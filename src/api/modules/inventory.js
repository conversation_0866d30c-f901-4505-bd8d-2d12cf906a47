import http from '@/utils/request'
export default {
  apiBackgroundDrpInventoryInfoMaterailClassificationStatisticsRecordPost (params) {
    return http.post('/api/background_drp/inventory_info/materail_classification_statistics_record', params)
  },
  apiBackgroundDrpInventoryInfoMaterailClassificationStatisticsRecordExportPost (params) {
    return http.post('/api/background_drp/inventory_info/materail_classification_statistics_record_export', params)
  },
  apiBackgroundDrpInventoryInfoFixCostDetailPost (params) {
    return http.post('/api/background_drp/inventory_info/fix_cost_detail', params)
  },
  apiBackgroundDrpInventoryInfoFixCostDetailExportPost (params) {
    return http.post('/api/background_drp/inventory_info/fix_cost_detail_export', params)
  },
  apiBackgroundDrpInventoryInfoRemarkAddPost (params) {
    return http.post('/api/background_drp/inventory_info/remark_add', params)
  },
  apiBackgroundDrpDrpFaceVerificationListPost (params) {
    return http.post('/api/background_drp/drp_face_verification/list', params)
  },
  apiBackgroundDrpDrpFaceVerificationAddPost (params) {
    return http.post('/api/background_drp/drp_face_verification/set', params)
  },
  /**
   * ['商户管理 进销存 入库单']
   * background_drp.entry_info.auto_get_materials_trade 自动关联入库单
   * @param {{count:number, materials_id:number, limit_unit_id:number}} param count 所需出库数量,materials_id 物资id,limit_unit_id 最小单位id
   * @returns {{code:number, msg:string, data:framework_serializer_EmptySerializer}} - rsp
   */
  apiBackgroundDrpEntryInfoAutoGetMaterialsTradeV5Post(param) {
    return http.post('/api/background_drp/entry_info/auto_get_materials_trade_v5', param)
  }

}
