import http from '@/utils/request'

export default {
  apiUserAdminUserListPost (params) {
    return http.post('/api/user/admin/user_list', params)
  },
  apiUserAdminUserInfoPost (params) {
    return http.post('/api/user/admin/user_info', params)
  },
  apiUserAdminUserListExportPost (params) {
    return http.post('/api/user/admin/user_list_export', params)
  },
  // 超管批量分类食材
  apiBackgroundAdminIngredientBatchClassifyPost (params) {
    return http.post('/api/background/admin/ingredient/batch_classify', params)
  },
  // 超管批量分类产品
  apiBackgroundAdminFoodBatchClassifyPost (params) {
    return http.post('/api/background/admin/food/batch_classify', params)
  },
  // 商户端批量分类产品
  apiBackgroundFoodIngredientBatchClassifyPost (params) {
    return http.post('/api/background_food/ingredient/batch_classify', params)
  },
  // 商户端批量分类产品
  apiBackgroundFoodBatchClassifyPost (params) {
    return http.post('/api/background_food/food/batch_classify', params)
  },
  // 超管菜品分类树
  apiBackgroundAdminFoodCategoryAllListPost (params) {
    return http.post('/api/background/admin/food_category/all_list', params)
  },
  // 商户端菜品分类树
  apiBackgroundFoodFoodCategoryAllListPost (params) {
    return http.post('/api/background_food/food_category/all_list', params)
  }
  // apiCardServiceCardUserListPost (params) {
  //   return http.post('/api/card_service/card/user/list', params)
  // }
}
