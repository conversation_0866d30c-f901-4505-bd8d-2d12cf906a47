import publicapi from './modules/public'
import login from './modules/login'
import asyncapi from './modules/asyncapi'
import merchant from './modules/merchant'
import upload from './modules/upload'
// import excel from './modules/excel'
import custom from './modules/custom'
import supervision from './modules/supervision'
import inventory from './modules/inventory' // 进销存
import order from './modules/order' // 订单
import collect from './modules/collect.js'
let api = {
  ...publicapi,
  ...login,
  ...asyncapi,
  ...merchant,
  ...upload,
  ...custom,
  ...supervision,
  ...inventory,
  ...order,
  ...collect
  // ...excel
}

// 自动引入modules下的文件，使用自动引入的是没有语法提示的
// const modulesFiles = require.context('./modules', true, /\.js$/)

// modulesFiles.keys().forEach(path => {
//   // set './app.js' => 'app'
//   // const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
//   const modules = modulesFiles(path)
//   api = Object.assign({}, api, modules.default)
// })
export default api
