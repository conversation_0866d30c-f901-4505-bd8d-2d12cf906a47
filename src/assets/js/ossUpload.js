/*
 *上传文件到阿里云oss
 *@param - uploadParams 上传阿里云form必传参数
 *@param - file :图片的本地资源
 *@param - successc:成功回调
 *@param - failc:失败回调
 */
import {
  Message
} from 'element-ui';
import axios from 'axios'

export const uploadFile = (uploadParams, file, successc, failc) => {
  if (!file) {
    Message.error('图片错误');
    return;
  }
  // console.log('上传图片.....');
  //图片名字 可以自行定义，     这里是采用当前的时间戳 + 150内的随机数来给图片命名的
  const aliyunFileKey = uploadParams.prefix + new Date().getTime() + Math.floor(Math.random() * 150) + '.png';

  axios.post(uploadParams.host, {
      'key': aliyunFileKey,
      'policy': uploadParams.policy,
      'OSSAccessKeyId': uploadParams.OSSAccessKeyId,
      'signature': uploadParams.signature,
      'callback': uploadParams.callback,
      'success_action_status': '200',
      'file': file
    })
    .then(res => {
      if (res.code != 0) {
        failc(new Error('上传错误:' + JSON.stringify(res)))
        return;
      }
      successc(`${uploadParams.host}/${aliyunFileKey}`);
    })
    .catch(err => {
      failc(err);
    });
}
