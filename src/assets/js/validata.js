export let validataPlateAmount = (rule, value, callback) => {
  if (value) {
    let reg = /^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
    if (!reg.test(value)) {
      callback(new Error('金额格式有误'))
    } else {
      callback()
    }
  } else {
    callback(new Error('请输入金额'))
  }
}
// 可以为0
export let validataPlateAmountPrice = (rule, value, callback) => {
  if (value) {
    let reg = /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
    if (!reg.test(value)) {
      callback(new Error('金额格式有误'))
    } else {
      callback()
    }
  } else {
    callback()
    // callback(new Error('请输入金额'))
  }
}
export let validateAccount = (rule, value, callback) => {
  if (value === '' || !value) {
    return callback(new Error('账号不能为空'))
  } else {
    let regNum = /(^\w{5,12}$)/
    if (!regNum.test(value)) {
      callback(new Error('账号长度5到12位，由字母、数字、下划线组成'))
    } else {
      callback()
    }
  }
}

export let validatePass = (rule, value, callback) => {
  if (value === '' || !value) {
    return callback(new Error('密码不能为空'))
  } else {
    let regPass = /^.{6,32}$/
    if (!regPass.test(value)) {
      callback(new Error('密码长度6到32位'))
    } else {
      callback()
    }
  }
}

export let validatePassword = (rule, value, callback) => {
  let regPass = /^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/
  // let regRPass = /(^\w{6,32}$)/;
  if (!value) {
    return callback(new Error("密码不能为空"));
  } else {
    if (!regPass.test(value)) {
      callback(new Error("密码长度8~20位，英文加数字"));
    } else {
      callback();
    }
  }
};

export let validateTelphone = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('手机号不能为空'))
  } else {
    let regTelphone = /^1[3456789]\d{9}$/
    if (!regTelphone.test(value)) {
      callback(new Error('请输入正确手机号'))
    } else {
      callback()
    }
  }
}

export let validateRole = (rule, value, callback) => {
  if (value === '' || !value) {
    return callback(new Error('请选择一个角色'))
  } else {
    callback()
  }
}

export let validataPrice = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('金额有误'))
  } else {
    let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
    if (!reg.test(value)) {
      callback(new Error('金额格式有误'))
    } else {
      callback()
    }
  }
}
export let validataChinese = (rule, value, callback) => {
  if (value) {
    let reg = /[\u4e00-\u9fa5]/gm
    if (reg.test(value)) {
      callback(new Error('请正确输入退款密码'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
export let validateStock = (rule, value, callback) => {
  if (value === '') {
    return callback(new Error('不能为空'))
  } else {
    let number = /^\d+$/
    if (!number.test(value)) {
      callback(new Error('请输入正确数字'))
    } else {
      callback()
    }
  }
}

export let validataSubsidy = (rule, value, callback) => {
  if (value !== '') {
    let reg = /^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
    console.log(value, reg.test(value))
    if (!reg.test(value)) {
      callback(new Error('金额格式有误'))
    } else {
      callback()
    }
  } else {
    callback(new Error('请输入金额'))
  }
}

// 校验不能包含特殊字符，可以包含中文，字母下划线等
export let validateName = (rule, value, callback) => {
  let reg = /^[\u4E00-\u9FA5\w-]+$/
  if (!reg.test(value)) {
    callback(new Error('格式不正确，不能包含特殊字符'))
  } else {
    callback()
  }
}
