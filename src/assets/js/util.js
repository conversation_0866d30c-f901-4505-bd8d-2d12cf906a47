/* eslint-disable camelcase */
/* eslint-disable eqeqeq */
export let formatAmount = x => {
  // 两位小数
  var fx = parseFloat(x)
  if (isNaN(fx)) {
    return false
  }
  var f = Math.round(x * 100) / 100
  var money = f.toString()
  var rs = money.indexOf('.')
  if (rs < 0) {
    rs = money.length
    money += '.'
  }
  while (money.length <= rs + 2) {
    money += '0'
  }

  // 金额千位
  let decimal = String(money).split('.')[1] || '' // 小数部分
  let tempArr = []
  let revNumArr = String(money)
    .split('.')[0]
    .split('')
    .reverse() // 倒序
  for (var i in revNumArr) {
    tempArr.push(revNumArr[i])
    if ((i + 1) % 3 === 0 && i != revNumArr.length - 1) {
      tempArr.push(', ')
    }
  }
  let zs = tempArr.reverse().join('') // 整数部分
  return decimal ? zs + '.' + decimal : zs
}
// 默认时间多久
export let getSevenDateRange = num => {
  if (num > 1) {
    let timeStamp = new Date().getTime() - 86400000 * (num - 1)
    let startDate = [
      new Date(timeStamp).getFullYear(),
      (new Date(timeStamp).getMonth() + 1).toString().padStart(2, '0'),
      new Date(timeStamp)
        .getDate()
        .toString()
        .padStart(2, '0')
    ].join('-')
    let endDate = [
      new Date().getFullYear(),
      (new Date().getMonth() + 1).toString().padStart(2, '0'),
      new Date()
        .getDate()
        .toString()
        .padStart(2, '0')
    ].join('-')
    return [startDate, endDate]
  } else {
    return false
  }
}
// 默认时间多久带时分秒
export let getSevenDateRangeReduce = (num, index) => {
  if (num > 1) {
    let timeStamp = new Date().getTime() - 86400000 * (num - 1)
    let startDate = [
      new Date(timeStamp).getFullYear(),
      new Date(timeStamp).getMonth() + 1,
      new Date(timeStamp).getDate()
    ].join('-')
    let endDate = [
      new Date().getFullYear(),
      new Date().getMonth() + 1,
      new Date().getDate() - index
    ].join('-')
    return [startDate, endDate]
  } else {
    return false
  }
}
export let getSevenDateTimeRange = num => {
  if (num > 1) {
    let timeStamp = new Date().getTime() - 86400000 * num
    let startDate = [
      new Date(timeStamp).getFullYear(),
      (new Date(timeStamp).getMonth() + 1).toString().padStart(2, '0'),
      new Date(timeStamp)
        .getDate()
        .toString()
        .padStart(2, '0')
    ].join('-')
    let endDate = [
      new Date().getFullYear(),
      (new Date().getMonth() + 1).toString().padStart(2, '0'),
      new Date()
        .getDate()
        .toString()
        .padStart(2, '0')
    ].join('-')
    return [startDate + ' 00:00:00', endDate + ' 23:59:59']
  } else {
    return false
  }
}

export let compareDate = (obj1, obj2) => {
  var val1 = obj1.日期
  var val2 = obj2.日期
  if (val1 > val2) {
    return 1
  } else if (val1 < val2) {
    return -1
  } else {
    return 0
  }
}

export let compareNumber = (obj1, obj2) => {
  var val1 = obj1.total
  var val2 = obj2.total
  if (val1 > val2) {
    return 1
  } else if (val1 < val2) {
    return -1
  } else {
    return 0
  }
}

export let dateFtt = (fmt, date) => {
  var o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds() // 毫秒
  }
  if (/(y+)/.test(fmt)) { fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length)) }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      )
    }
  }
  return fmt
}

/**
 * 日期格式化
 * @param time
 * @param format
 * @returns {string}
 */
export const parseTime = function(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  // padStart兼容处理
  if (!String.prototype.padStart) {
    // eslint-disable-next-line no-extend-native
    String.prototype.padStart = function padStart(targetLength, padString) {
      targetLength = targetLength >> 0 // floor if number or convert non-number to 0;
      padString = String(typeof padString !== 'undefined' ? padString : ' ')
      if (this.length > targetLength) {
        return String(this)
      } else {
        targetLength = targetLength - this.length
        if (targetLength > padString.length) {
          padString += padString.repeat(targetLength / padString.length) // append to original to ensure we are longer than needed
        }
        return padString.slice(0, targetLength) + String(this)
      }
    }
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

export const debounce = function(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

export const deepClone = function(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

/**
 * @description to
 * @param {*} promise
 * @param {*} errorExt
 */
export const to = (promise, errorExt) => {
  return promise
    .then(res => [null, res])
    .catch(err => {
      if (errorExt) {
        Object.assign(err, errorExt)
      }
      return [err, undefined]
    })
}

/**
 * @description 去除空格
 * @param {String} str
 * @param {Number} type
 * @returns String
 */
export const trim = (str, type) => {
  let strType = typeof str
  if (strType === 'number') {
    str = str.toString()
  }
  switch (type) {
    case 1:
      return str.replace(/\s+/g, '')
    case 2:
      return str.replace(/(^\s*)|(\s*$)/g, '')
    case 3:
      return str.replace(/(^\s*)/g, '')
    case 4:
      return str.replace(/(\s*$)/g, '')
    default:
      return str
  }
}
