@font-face {
  font-family: "iconfont"; /* Project id 3829356 */
  src: url('iconfont.woff2?t=1671439139856') format('woff2'),
       url('iconfont.woff?t=1671439139856') format('woff'),
       url('iconfont.ttf?t=1671439139856') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-tuoyuan:before {
  content: "\e61d";
}

.icon-gengduo:before {
  content: "\e69a";
}

.icon-nan:before {
  content: "\e6ef";
}

.icon-nv:before {
  content: "\e6f0";
}

