<template>
  <div calss="ps-dialog-doublefactor">
    <el-dialog
      :custom-class="'ps-dialog ' + customClass"
      class="home-confirm-dialog"
      :visible.sync="dialogVisible"
      :width="width"
      :top="top"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :show-close="false"
      @closed="handleClose"
      @open="handleOpen"
    >
    <template slot="title">
      <div class="header flex" v-if="noticeForm">
        <div class="title">{{ noticeForm.title }}</div>
        <div class="btn-group">
          <!-- <el-button type="text" @click="closeDialogHandle(2)">关闭所有重要公告</el-button> -->
          <span class="tips">(关闭所有重要公告)</span>
          <i @click="closeDialogHandle(2)" class="el-icon-close close-icon"></i>
        </div>
      </div>
    </template>
      <div class="notice-form-content" v-if="noticeForm">
        <div class="content margin-b-10" v-html="noticeForm.content"></div>
        <div class="flex" v-if="noticeForm && noticeForm.fileLists && noticeForm.fileLists.length">
          <div class="m-t-10">附件：</div>
          <div>
            <div v-for="item in noticeForm.fileLists" :key="item.uid" style="height: 25px;">
              <span>{{ item.name }}</span>
              <el-button type="text" @click="downloadHandle(item)" style="margin-left: 10px;">下载</el-button>
            </div>
          </div>
        </div>
      </div>
      <template slot="footer">
        <div class="dialog-footer content_center">
          <el-button
            size="medium"
            plain
            v-if="dataList.length > 1"
            :disabled="currentIndex <= 0"
            @click="prevNotice"
          >
            上一条
          </el-button>
          <span style="margin: 0 20px">{{ currentIndex + 1 }}/{{ dataList.length }}</span>
          <el-button
            size="medium"
            type="primary"
            v-if="dataList.length > 1"
            :disabled="isLastNotice"
            @click="handleNextOrClose"
          >
            <!-- {{ isLastNotice ? `关 闭` : '下一条' }} -->
            下一条
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { unescapeHTML, to } from '@/utils'
import FileSaver from 'file-saver'
export default {
  name: 'AnnouncementDialog',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '公告标题'
    },
    width: {
      type: String,
      default: '900px'
    },
    top: {
      type: String,
      default: '20vh'
    },
    customClass: {
      type: String,
      default() {
        return 'ps-dialog-doublefactor'
      }
    },
    dataList: {
      type: Array,
      default() {
        return []
      }
    },
    center: Boolean,
    userInfo: {
      type: Object
    },
    // 侧边弹窗
    showNotifications: Function
  },
  data() {
    return {
      noticeForm: null,
      currentIndex: 0 // 当前显示的通知索引
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    },
    // 判断是否是最后一条公告
    isLastNotice() {
      return this.currentIndex >= this.dataList.length - 1
    }
  },
  watch: {
    // $route: {
    //   handler: function(route) {
    //     const query = route.query
    //   },
    //   immediate: true
    // }
  },
  created () {
    console.log('Inventory')
  },
  mounted () {
  },
  methods: {
    handleClose() {
      // this.showNotifications()
      this.$emit('handleClose')
    },
    // 打开化加载数据
    handleOpen() {
      this.noticeForm = this.dataList[0]
      let content = unescapeHTML(this.noticeForm.content)
      this.noticeForm.content = content
      this.currentIndex = 0 // 每次打开重置为第一条
      this.loadNotice(0)
      this.bulkMsgReadPost([this.noticeForm.id]) // 初始化第一条则已读
    },
    // 切换到上一条
    prevNotice() {
      if (this.currentIndex > 0) {
        this.currentIndex--
        this.loadNotice(this.currentIndex)
      }
    },
    // 处理下一条/关闭操作
    handleNextOrClose() {
      if (this.isLastNotice) {
        this.dialogVisible = false // 关闭弹窗
      } else {
        this.nextNotice() // 切换到下一条
      }
    },
    // 切换到下一条
    async nextNotice() {
      if (this.currentIndex < this.dataList.length - 1) {
        this.currentIndex++
        this.loadNotice(this.currentIndex)
      }
      console.log(this.noticeForm.id, 444)
      // 重要公告时调用关闭并且修改已读
      await this.bulkMsgReadPost([this.noticeForm.id])
    },
    // 加载指定索引的通知
    loadNotice(index) {
      this.noticeForm = this.dataList[index]
      this.initDefaultInfo(this.noticeForm)
    },
    // 右上角-关闭重要公告 type 1 关闭当前 2 关闭所有
    async closeDialogHandle(type) {
      if (type === 1) { // type为1的情况砍掉了 代码先保留
        // 1. 标记当前公告为已读
        console.log(this.noticeForm.id, 111)
        await this.markAsRead([this.noticeForm.id])
        // 2. 如果有下一条，切换到下一条
        if (this.currentIndex < this.dataList.length - 1) {
          this.nextNotice()
          console.log(this.noticeForm.id, 222)
          // await this.markAsRead([this.noticeForm.id])
        } else { // 3. 如果没有下一条，关闭弹窗
          this.dialogVisible = false
        }
      } else {
        // type 2 的情况：直接已读全部并关闭弹窗
        const allMsgIds = this.dataList.map(item => item.id)
        console.log(allMsgIds, 333)
        await this.markAsRead(allMsgIds)
        this.dialogVisible = false
      }
    },
    // 标记公告为关闭状态
    async markAsRead(msgIds) {
      const params = { msg_nos: msgIds }
      const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesBulkMsgCancelPop(params))
      if (err) {
        this.$message.error(err.message)
        throw err // 抛出错误让调用方处理
      }
      if (res.code !== 0) {
        this.$message.error(res.msg)
        throw new Error(res.msg)
      }
      return res
    },
    // 标记公告为关闭并且更改已读状态
    async bulkMsgReadPost(msgIds) {
      const params = { msg_nos: msgIds }
      const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesBulkMsgReadPost(params))
      if (err) {
        this.$message.error(err.message)
        throw err // 抛出错误让调用方处理
      }
      if (res.code !== 0) {
        this.$message.error(res.msg)
        throw new Error(res.msg)
      }
      return res
    },
    // 处理html标签
    initDefaultInfo(resData) {
      let content = unescapeHTML(resData.content)
      this.noticeForm.content = content
      if (resData.resource && resData.resource.length) {
        let files = resData.resource
        let time = new Date().getTime()
        this.noticeForm.fileLists = files.map(v => {
          time += 1
          let fileName = v.url.substring(v.url.lastIndexOf('/') + 1)
          // 后端加了tk校验文件，要把它截掉
          let isSplit = fileName.indexOf('?')
          if (isSplit > -1) {
            fileName = fileName.substring(0, isSplit)
          }
          return {
            name: fileName,
            status: 'success',
            uid: time,
            url: v.url
          }
        })
      }
    },
    // 下载
    async downloadHandle(file) {
      const response = await fetch(file.url)
      const blob = await response.blob()
      FileSaver.saveAs(blob, file.name)
    }
  },
  beforeDestroy() {}
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header{
  padding: 10px 20px 10px !important;
}
::v-deep.home-confirm-dialog .el-dialog__body {
  // min-height: 500px;
  // max-height: calc(100vh - 250px);
  height: calc(100vh - 250px);
  overflow: auto;
  border-top:1px solid #dfdfdf;
  border-bottom:1px solid #dfdfdf;
  background-color: #F8F9FA;
  img {
    max-width: 100% !important;
    height: auto;
  }
}
::v-deep .el-dialog{
  position: fixed;
  height:fit-content;
  left:0 !important;
  right:0 !important;
  top:0 !important;
  bottom:0 !important;
  margin:auto !important;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  position: relative;
  .title {
    flex: 1; /* 占据剩余空间 */
    text-align: center; /* 文字居中 */
    padding: 0 20px; /* 避免贴边 */
  }
  .btn-group {
    position: absolute;
    right: 0px;
    top: 0px;
    display: flex;
    align-items: center;
    .tips{
      position: relative;
      top: -2px;
      color: #999999;
      font-size: 12px;
    }
    .close-icon{
      font-size: 24px;
      margin-left: 5px;
      cursor: pointer;
    }
  }
}
.notice-form-content {
  word-break: break-all;
  img {
    max-width: 100% !important;
    height: auto;
  }
  .title {
    font-size: 18px;
    font-weight: 600;
  }
  .content {
    max-width: 100%;
    word-break: break-all;
    background-color: #F8F9FA;
    p {
      line-height: 1.5;
    }
  }
  .margin-b-10 {
    margin-bottom: 10px;
  }
}
</style>
