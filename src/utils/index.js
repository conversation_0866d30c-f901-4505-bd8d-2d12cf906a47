import NP from 'number-precision' // 解决精确度问题
// import md5 from 'js-md5' // MD5

/* eslint-disable camelcase */
/**
 * @description jsonp
 * @param {*} url
 * @param {*} data
 * @returns PROMISE
 */
export function jsonp(url, data) {
  const headEl = document.getElementsByTagName('head')[0]
  const fnName = `jsonp_${new Date().getTime()}`
  const script = document.createElement('script')
  script.src = `${url}?${paramsToUrlQuerys(data)}&callback=${fnName}`
  script.type = 'text/javascript'
  headEl.appendChild(script)
  return new Promise((resolve, reject) => {
    window[fnName] = function(res) {
      headEl.removeChild(script)
      delete window[fnName]
      resolve(res)
    }
    script.onerror = function() {
      // 异常处理
      headEl.removeChild(script)
      delete window[fnName]
      // eslint-disable-next-line prefer-promise-reject-errors
      reject('jsonp error!')
    }
  })
}

/**
 * @description 格式化数据返回get请求方式的数据
 * @param {*} data
 * @param {*} encode
 * @returns
 */
export function paramsToUrlQuerys(data, encode = false) {
  const queryArr = []
  for (let key in data) {
    if (encode) {
      queryArr.push(encodeURIComponent(key) + '=' + encodeURIComponent(data[key]))
    } else {
      queryArr.push(key + '=' + data[key])
    }
  }
  return queryArr.join('&')
}

/**
 * @description 获取地址栏的参数
 * @param {*} url
 * @returns Object
 */
export const getQueryObject = function(url) {
  url = url || window.location.href
  var request = {}
  let index = url.indexOf('?')
  if (index !== -1) {
    var str = url.substring(index + 1)
    var strs = str.split('&')
    for (var i = 0; i < strs.length; i++) {
      request[strs[i].split('=')[0]] = decodeURI(strs[i].split('=')[1])
    }
  }
  return request
}

/**
 * @description 判断是否是微信环境
 * @returns Boolean
 */
export const isWeixinClient = function() {
  let ua = window.navigator.userAgent.toLowerCase()
  if (ua.indexOf('micromessenger') !== -1) {
    return true
  } else {
    return false
  }
}

/**
 * @description 深拷贝针对object/array
 * @param {*} source
 * @returns
 */
export const deepClone = function(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

/**
 * @description 防抖
 * @param {*} func
 * @param {*} wait
 * @param {*} immediate
 * @returns
 */
export const debounce = function(func, wait, immediate) {
  var timeout, args, context, timestamp, result;

  var later = function() {
    // 现在和上一次时间戳比较
    var last = Date.now() - timestamp;
    // 如果当前间隔时间少于设定时间且大于0就重新设置定时器
    if (last < wait && last >= 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      // 否则的话就是时间到了执行回调函数
      timeout = null;
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  return function() {
    context = this;
    args = arguments;
    // 获得时间戳
    timestamp = Date.now();
    // 如果定时器不存在且立即执行函数
    var callNow = immediate && !timeout;
    // 如果定时器不存在就创建一个
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      // 如果需要立即执行函数的话 通过 apply 执行
      result = func.apply(context, args);
      context = args = null;
    }

    return result;
  };
};

/**
 * 日期格式化
 * @param time
 * @param format
 * @returns {string}
 */
export const parseTime = function(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/').replace(new RegExp(/T/gm), ' ') // "2021-10-27T19:49:43.806066"
      }
    }

    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  // padStart兼容处理
  if (!String.prototype.padStart) {
    // eslint-disable-next-line no-extend-native
    String.prototype.padStart = function padStart(targetLength, padString) {
      targetLength = targetLength >> 0 // floor if number or convert non-number to 0;
      padString = String(typeof padString !== 'undefined' ? padString : ' ')
      if (this.length > targetLength) {
        return String(this)
      } else {
        targetLength = targetLength - this.length
        if (targetLength > padString.length) {
          padString += padString.repeat(targetLength / padString.length) // append to original to ensure we are longer than needed
        }
        return padString.slice(0, targetLength) + String(this)
      }
    }
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @description
 * @param {*} time
 * @returns
 */
export const replaceDate = function(time) {
  if (time) {
    return time.replace(new RegExp(/-/gm), '/')
  }
  return time
}

// 设置cookie
export const setCookie = function(name, value, domain, path, expires) {
  if (expires) {
    expires = new Date(+new Date() + expires)
  }
  var tempcookie =
    name +
    '=' +
    escape(value) +
    (expires ? '; expires=' + expires.toGMTString() : '') +
    (path ? '; path=' + path : '') +
    (domain ? '; domain=' + domain : '')
  if (tempcookie.length < 4096) {
    // 防止数据超标
    document.cookie = tempcookie
  }
}

// 获取cookie
export const getCookie = function(name) {
  // eslint-disable-next-line one-var
  let arr,
    reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')

  if ((arr = document.cookie.match(reg))) return arr[2]
  else return null
}

// 简单检查是否被iframe
export const checkIframe = function(callback) {
  const cookieName = 'isIframe'
  const cookieDomain = '' // top.location.host
  if (top.location !== self.location) {
    // 用于记录被劫持的次数
    let iframeCount
    if (!getCookie(cookieName)) {
      // 设置个保存时间
      setCookie(cookieName, 0, cookieDomain, '', 30 * 864e5)
      iframeCount = 0
    } else {
      iframeCount = parseInt(getCookie(cookieName))
    }

    // 如果连续被劫持的次数大于等于3次,执行回调防止无限重置url，否则累加被劫持次数、重置当前url
    if (iframeCount >= 3) {
      callback(iframeCount)
    } else {
      iframeCount++
      setCookie(cookieName, iframeCount, cookieDomain, '', 30 * 864e5)
      top.location = self.location
    }
  }
  // 每次成功进入页面则计数清0
  setCookie(cookieName, 0, cookieDomain, '', 30 * 864e5)
}

/**
 * @description 获取树状结构到第deep层级的组合的key-value列表
 * @param {*} treeData
 * @param {*} key
 * @param {*} deep 不传默认返回所有层级的组合
 * @returns
 */
export const getTreeDeepkeyList = function(treeData, key, childrenKey, deep) {
  let arr = []
  let deepNo = 1
  key = key || 'key'
  childrenKey = childrenKey || 'children'
  function getTreeDeepHandle(treeData, key, childrenKey, deep) {
    if (treeData instanceof Array) {
      treeData.forEach(treeItem => {
        arr.push(treeItem[key])
        if ((!deep || deepNo < deep) && treeItem[childrenKey] && treeItem[childrenKey].length > 0) {
          deepNo++
          getTreeDeepHandle(treeItem[childrenKey], key, childrenKey, deep)
        }
      })
    }
    return arr
  }
  return getTreeDeepHandle(treeData, key, childrenKey, deep)
}

/**
 * @description 查找树状结构数据中某个id及其父级id所组成的数组
 * @param {*} treeData
 * @param {*} val
 * @param {*} key default id
 * @returns Array
 */
export const getTreeDeepArr = function(treeData, val, key, childkey) {
  let arr = [] // 在递归时操作的数组
  let returnArr = [] // 存放结果的数组
  let depth = 0 // 定义全局层级
  key = key || 'id'
  childkey = childkey || 'children'
  // 定义递归函数
  function childrenEach(childrenData, depthN) {
    for (var j = 0; j < childrenData.length; j++) {
      depth = depthN // 层级赋值
      arr[depthN] = childrenData[j][key]
      if (childrenData[j][key] === val) {
        returnArr = arr.slice(0, depthN + 1) // 拿到当前从零到当前层级的数据
        break
      } else {
        if (childrenData[j][childkey]) {
          depth++
          childrenEach(childrenData[j][childkey], depth)
        }
      }
    }
    return returnArr
  }
  return childrenEach(treeData, depth)
}

/**
 * @description 查找树状结构数据中某个id及其子级id所组成的数组
 * @param {*} treeData
 * @param {*} val
 * @param {*} key default id
 * @param {*} hasId default false
 * @param {*} hasParent default false
 * @returns Array/Object
 */
export const getTreeChildArr = function(treeData, val, opts = {}) {
  let depth = 0
  let returnArr = [] // 存放结果的数组
  let parentArr = [] // 存放的父级数组
  let depthArr = [] // 存放的父级数组
  let key = opts.key || 'id'
  let childkey = opts.childkey || 'children'
  let hasId = opts.hasId || false
  let hasParent = opts.hasParent || false
  // 定义递归函数
  function childrenEach(childrenData, set, depthN) {
    for (var j = 0; j < childrenData.length; j++) {
      depth = depthN // 层级赋值
      depthArr[depthN] = childrenData[j][key]
      if (childrenData[j][key] === val) {
        set = true
        parentArr = depthArr.slice(0, depthN + 1)
        if (hasId) {
          returnArr.push(childrenData[j][key])
        }
        if (childrenData[j][childkey]) {
          childrenEach(childrenData[j][childkey], set, depth)
        }
        break
      } else {
        if (set) {
          returnArr.push(childrenData[j][key])
        }
        if (childrenData[j][childkey]) {
          depth++
          childrenEach(childrenData[j][childkey], set, depth)
        }
      }
    }
    return returnArr
  }
  let result = childrenEach(treeData, false, depth)
  if (hasParent) {
    return {
      childrens: result,
      parents: parentArr,
      total: [...new Set(result.concat(parentArr))]
    }
  } else {
    return result
  }
}

/**
 * @description 获取当前层级及其子级的数据
 * @param  {Array} data     [description]
 * @param  {*} value    [description]
 * @param  {String} key      [description]
 * @param  {String} childkey [description]
 * @return {Array}          [description]
 */
export function getCurrentTree(data, value, key = 'id', childkey = 'children') {
  let result = []
  function traversal(data) {
    data.map(item => {
      if (value === item[key]) {
        result = [item]
      } else {
        if (item[childkey]) {
          if (item[childkey].length > 0) {
            traversal(item[childkey])
          }
        }
      }
    })
  }
  traversal(data)
  return result
}

/**
 * @description 树状结构通过keyList获取相对应label值
 * @param {*} treeData
 * @param {*} key key列表
 * @param {*} keyList key列表
 * @param {*} label
 * @returns
 */
export const getTreeDeepLabelList = function(treeData, keyList, label, key, childrenKey) {
  let arr = []
  label = label || 'label'
  key = key || 'key'
  childrenKey = childrenKey || 'children'
  function getTreeDeepHandle(treeData, keyList, label, key, childrenKey) {
    if (treeData instanceof Array) {
      treeData.forEach(treeItem => {
        if (keyList.indexOf(treeItem[key]) > -1) {
          arr.push(treeItem[label])
        }
        if (treeItem[childrenKey] && treeItem[childrenKey].length > 0) {
          getTreeDeepHandle(treeItem[childrenKey], keyList, label, key, childrenKey)
        }
      })
    }
    return arr
  }
  return getTreeDeepHandle(treeData, keyList, label, key, childrenKey)
}

export const findChildSetting = function(data, keys) {
  let res = []
  for (let i = 0; i < data.length; i++) {
    if (keys.includes(data[i].key) && !data[i].children) {
      res.push(data[i])
    } else if (keys.includes(data[i].key) && data[i].children) {
      let parent = JSON.parse(JSON.stringify(data[i]))
      parent.children = findChildSetting(data[i].children, keys)
      res.push(parent)
    }
  }
  return res
}

/**
 * @description getRouterFlattenList 路由扁平化
 * @param {*} router
 * @param {*} key
 * @param {*} deep
 * @returns
 */
export const getRouterFlattenList = function(router) {
  let arr = []
  function getTreeHandle(treeData, treePath) {
    if (treePath === '/') {
      treePath = ''
    }
    treeData.forEach(treeItem => {
      let currentPath = treePath + '/' + treeItem.path.replace('/:id', '')
      let currentPermission =
        treeItem.meta && treeItem.meta.permission ? treeItem.meta.permission : []
      arr.push({
        permission: Object.assign([], currentPermission),
        path: currentPath
      })
      if (treeItem.children && treeItem.children.length > 0) {
        getTreeHandle(treeItem.children, currentPath)
      }
    })
    return arr
  }
  router.forEach(fullRouter => {
    let fullPath = fullRouter.path
    let permission = fullRouter.meta && fullRouter.meta.permission ? fullRouter.meta.permission : []
    arr.push({
      permission: Object.assign([], permission),
      path: fullRouter.path
    })
    if (fullRouter.children && fullRouter.children.length > 0) {
      getTreeHandle(fullRouter.children, fullPath)
    }
  })
  return arr
}

export function findRoutePermission(routes, path) {
  const splitPath = path.split('/')
  let permissions = []
  if (splitPath.length > 1) {
    let currentNav = '/' + splitPath[1]
    for (let index = 0; index < routes.length; index++) {
      if (routes[index].path === currentNav) {
        permissions =
          routes[index].meta && routes[index].meta.permission ? routes[index].meta.permission : []
        break
      }
    }
  }
  return permissions
}

export function findRouteActiveNav(routes, path, navMenuList) {
  let activeNavKey
  let pathArr = path.split('/')
  for (let index = 0; index < routes.length; index++) {
    if (('/' + pathArr[1]) === routes[index].path) {
      navMenuList.map(nav => {
        let item = routes[index]
        nav.permissions.map(per => {
          if (!item.hidden && item.meta && item.meta.permission && (item.meta.permission.includes(per) || item.meta.no_permission)) {
            activeNavKey = nav.key
          }
        })
      })
    }
  }
  return activeNavKey
}

/**
 * @description async/await 的 try_catch处理吧 参考await-to-js
 * @param {*} promise
 * @param {*} errorExt
 */
export const to = (promise, errorExt) => {
  return promise
    .then(res => [null, res])
    .catch(err => {
      if (errorExt) {
        Object.assign(err, errorExt)
      }
      return [err, undefined]
    })
}

/**
 * @description 去除空格
 * @param {String} str
 * @param {Number} type
 * @returns String
 */
export const trim = (str, type) => {
  let strType = typeof str
  if (strType === 'number') {
    str = str.toString()
  }
  switch (type) {
    case 1: // 去除所有空格
      return str.replace(/\s+/g, '')
    case 2: // 去除首尾空格
      return str.replace(/(^\s*)|(\s*$)/g, '')
    case 3: // 去除首部空格
      return str.replace(/(^\s*)/g, '')
    case 4: // 去除尾部空格
      return str.replace(/(\s*$)/g, '')
    default:
      return str
  }
}

/**
 * @description 获取某年某月的天数
 * @param {*} year
 * @param {*} month
 * @returns
 */
export function getMonthDays(year, month) {
  return new Date(year, month, 0).getDate()
}

/**
 * @description 检查是否是闰年
 * @param {*} fullYear
 * @returns
 */
export function isRunYear(fullYear) {
  return fullYear % 4 === 0 && (fullYear % 100 !== 0 || fullYear % 400 === 0)
}

/**
 *
 * @param {*} base64
 * @param {*} filename
 * @returns
 */
export const dataURLtoFile = function(base64, filename) {
  // 将base64转换为文件
  // eslint-disable-next-line one-var
  let arr = base64.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], filename, { type: mime })
}

/**
 *
 * @param {*} dataurl
 * @param {*} filename
 * @returns
 */
export const base64ToBlob = function(dataurl, filename) {
  var arr = dataurl.split(',')
  var mime = arr[0].match(/:(.*?);/)[1]
  var bstr = atob(arr[1])
  var n = bstr.length
  var u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr], { type: mime })
}

/**
 * 获取文件后缀名
 * @param {String} filename
 */
export function getExt(filename) {
  if (typeof filename === 'string') {
    return filename
      .split('.')
      .pop()
      .toLowerCase()
  } else {
    throw new Error('filename must be a string type')
  }
}

/**
 * 获取url最后一个地址
 * @param {String} filename
 */
export function getUrlExt(url) {
  url = url || location.href
  let hasSign = url.indexOf('/?')
  if (hasSign < 0) {
    hasSign = url.indexOf('?')
  }
  return url
    .substring(0, hasSign)
    .split('/')
    .pop()
    .toLowerCase()
}

/**
 * 休眠xxxms
 * @param {Number} milliseconds
 */
export function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 简单判断两个数组是否相等，先排序再转字符串判断
 * @param {*} arr1
 * @param {*} arr2
 * @returns Boolean
 */
export function arrayEqual(arr1, arr2) {
  return arr1.sort().toString() === arr2.sort().toString()
}

/**
 * @description 获取localStorage
 * @param {*} key
 * @returns *
 */
export const getLocalStorage = function(key) {
  return localStorage.getItem(key)
}

/**
 * @description
 * @param {*} key
 * @param {*} value
 */
export const setLocalStorage = function(key, value) {
  localStorage.setItem(key, value)
}

/**
 * @description
 * @param {*} key
 * @returns
 */
export const removeLocalStorage = function(key) {
  localStorage.removeItem(key)
}

/**
 * @description 获取setLocalStorage
 * @param {*} key
 * @returns *
 */
export const getSessionStorage = function(key) {
  return sessionStorage.getItem(key)
}

/**
 * @description
 * @param {*} key
 * @param {*} value
 */
export const setSessionStorage = function(key, value) {
  sessionStorage.setItem(key, value)
}

/**
 * @description
 * @param {*} key
 * @returns
 */
export const removeSessionStorage = function(key) {
  sessionStorage.removeItem(key)
}

/**
 * @description 获取token
 * @returns string
 */
export const getToken = function() {
  return getSessionStorage('V4TOKEN')
}

export const setToken = function(data) {
  setSessionStorage('V4TOKEN', data)
}

export const removeToken = function(data) {
  removeSessionStorage('V4TOKEN')
  removeSessionStorage('isChoiceOrg')
}

export const getUserInfo = function(data) {
  let userInfo = getSessionStorage('USERINFO', data)
  if (typeof userInfo === 'string') {
    try {
      userInfo = JSON.parse(decodeURIComponent(userInfo))
    } catch (error) {}
  }
  return userInfo
}

/**
 * @param {string} path
 * @returns {Boolean}
 */

export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

export async function loadView(view) {
  return () => import(/* webpackChunkName: "view-[request]" */ '@/views/' + view)
}

// 默认时间多久
export let getSevenDateRange = num => {
  let endDate = [
    new Date().getFullYear(),
    (new Date().getMonth() + 1).toString().padStart(2, '0'),
    new Date()
      .getDate()
      .toString()
      .padStart(2, '0')
  ].join('-')
  if (num > 1) {
    let timeStamp = new Date().getTime() - 86400000 * (num - 1)
    let startDate = [
      new Date(timeStamp).getFullYear(),
      (new Date(timeStamp).getMonth() + 1).toString().padStart(2, '0'),
      new Date(timeStamp)
        .getDate()
        .toString()
        .padStart(2, '0')
    ].join('-')
    return [startDate, endDate]
  } else if (num === 1) {
    return [endDate, endDate]
  } else {
    return false
  }
}
// 默认月份
export let getMonthDateRange = num => {
  if (num < 1) return false;

  // 获取当前日期
  let currentDate = new Date();
  // 计算结束月份（即当前月份）
  let endDate = [
    currentDate.getFullYear(),
    (currentDate.getMonth() + 1).toString().padStart(2, '0')
  ].join('-');

  // 计算开始月份（即前num个月）
  let startDateTemp = new Date(currentDate);
  startDateTemp.setMonth(startDateTemp.getMonth() - (num - 1)); // 回退num-1个月
  let startDate = [
    startDateTemp.getFullYear(),
    (startDateTemp.getMonth() + 1).toString().padStart(2, '0')
  ].join('-');

  if (num === 1) {
    return [endDate, endDate];
  } else {
    return [startDate, endDate];
  }
}

// 测试
console.log(getMonthDateRange(2)); // 输出类似 ["2024-12", "2025-01"]
// 默认时间多久带时分秒
export let getSevenDateRangeReduce = (num, index) => {
  if (num > 1) {
    let timeStamp = new Date().getTime() - 86400000 * (num - 1)
    let startDate = [
      new Date(timeStamp).getFullYear(),
      new Date(timeStamp).getMonth() + 1,
      new Date(timeStamp).getDate()
    ].join('-')
    let endDate = [
      new Date().getFullYear(),
      new Date().getMonth() + 1,
      new Date().getDate() - index
    ].join('-')
    return [startDate, endDate]
  } else {
    return false
  }
}
export let getSevenDateTimeRange = num => {
  if (num > 1) {
    let timeStamp = new Date().getTime() - 86400000 * num
    let startDate = [
      new Date(timeStamp).getFullYear(),
      (new Date(timeStamp).getMonth() + 1).toString().padStart(2, '0'),
      new Date(timeStamp)
        .getDate()
        .toString()
        .padStart(2, '0')
    ].join('-')
    let endDate = [
      new Date().getFullYear(),
      (new Date().getMonth() + 1).toString().padStart(2, '0'),
      new Date()
        .getDate()
        .toString()
        .padStart(2, '0')
    ].join('-')
    return [startDate + ' 00:00:00', endDate + ' 23:59:59']
  } else {
    return false
  }
}

/**
 * @description 继承
 * @param {*} destination
 * @param {*} source
 * @returns
 */
export const extend = function(destination, source) {
  for (let property in source) {
    if (source[property]) {
      destination[property] = source[property]
    }
  }
  return destination
}

/**
 * @description 计算时间范围
 * @param { Number } day
 * @param { Object } opts { date: new Date(), format: '{y}-{m}-{d} {h}:{i}:{s}' }
 * @returns Array
 */
export const getDateRang = function(day, opts) {
  if (typeof day !== 'number') return
  let options = {
    date: new Date(),
    format: '{y}-{m}-{d} {h}:{i}:{s}'
  }
  if (opts) {
    options = extend(options, opts)
  }
  let dateRang = []
  let time = parseTime(options.date, '{y}-{m}-{d}')
  const start = new Date(time + ' 00:00:00')
  const end = new Date(time + ' 23:59:59')
  if (day > 0) {
    end.setTime(end.getTime() + 3600 * 1000 * 24 * day)
  } else {
    start.setTime(start.getTime() + 3600 * 1000 * 24 * day)
  }
  dateRang = [parseTime(start, options.format), parseTime(end, options.format)]
  return dateRang
}

/**
 * 将驼峰转为下划线
 */
export function camelToUnderline(camelStr) {
  return camelStr
    .replace(/[A-Z]/g, function(s) {
      return ' ' + s.toLowerCase()
    })
    .trim()
    .replaceAll(' ', '_')
}

/**
 * 下划线转小驼峰
 */
export function underlineToSmallCamel(str) {
  return str.toLowerCase().replace(/_([a-z])/g, function(s, s1) {
    return s1.toUpperCase()
  })
}

/**
 * 华氏温度(32℉) 转化为摄氏温度(0℃)
 */
export function f2c(s) {
  return s.replace(/(\d+(\.\d*)?)℉/g, function($0, $1, $2) {
    return (($1 - 32) * 5) / 9 + '℃'
  })
}

/**
 * @desc
 * @param {array} arr
 * @return {array}
 **/
export function unique(arr) {
  const newArray = []
  const tmp = {}
  for (let i = 0; i < arr.length; i++) {
    // 使用JSON.stringify()进行序列化
    if (!tmp[typeof arr[i] + JSON.stringify(arr[i])]) {
      // 将对象序列化之后作为key来使用
      tmp[typeof arr[i] + JSON.stringify(arr[i])] = 1
      newArray.push(arr[i])
    }
  }
  return newArray
}

/**
 * @description 数组对象根据key去重
 * @param {array} arr
 * @param {string} key
 * @returns {array}
 */
export function uniqueArrKey(arr, key) {
  key = key || 'id'
  const newArray = []
  const tmp = {}
  for (let i = 0; i < arr.length; i++) {
    //
    if (!tmp[arr[i][key].toString()]) {
      //
      tmp[arr[i][key].toString()] = 1
      newArray.push(arr[i])
    }
  }
  return newArray
}

/**
 * @description 数组对选根据key排序，会修改原数组
 * @param {array} arr 原数组
 * @param {strging} key 排序的key
 * @param {strging} down 是否是降序
 * @return {void}
 */
export function sortArrKeyList(arr, key, down) {
  key = key || 'id'
  arr.sort((a, b) => {
    if (a[key] - b[key] === 0) {
      return 0
    }
    return down ? (a[key] > b[key] ? -1 : 1) : (a[key] > b[key] ? 1 : -1)
  })
}

/**
 * @description 数组转树形结构
 * @param {*} list
 * @param {*} idKey
 * @param {*} parIdKey
 * @param {*} childKey
 * @param {*} parId
 * @returns
 */
export function arrayToTree(list, idKey, parIdKey, childKey, parId) {
  idKey = idKey || 'id'
  parIdKey = parIdKey || 'parent'
  childKey = childKey || 'children'
  parId = parId || 0 // 父级id从哪开始
  let map = {}
  let result = []
  let len = list.length

  // 构建map
  for (let i = 0; i < len; i++) {
    // 将数组中数据转为键值对结构 (这里的数组和obj会相互引用，这是算法实现的重点)
    map[list[i][idKey]] = list[i]
  }

  // 构建树形数组
  for (let i = 0; i < len; i++) {
    let itemParId = list[i][parIdKey]
    // 顶级节点
    if (itemParId === parId) {
      result.push(list[i])
      continue
    }
    // 孤儿节点，舍弃(不存在其父节点)
    if (!map[itemParId]) {
      continue
    }
    // 将当前节点插入到父节点的children中（由于是引用数据类型，obj中对于节点变化，result中对应节点会跟着变化）
    if (map[itemParId][childKey]) {
      map[itemParId][childKey].push(list[i])
    } else {
      map[itemParId][childKey] = [list[i]]
    }
  }
  return result
}

export function array2tree2 (list) {
  const result = []
  const maps = {}
  const len = list.length
  let index = 0
  let item
  while (index < len) {
    item = list[index]
    if (item.pid === 0) {
      result.push(item)
    }
    if (maps[item.pid]) {
      if (maps[item.pid].sub) {
        maps[item.pid].sub.push(item)
      } else {
        maps[item.pid].sub = [item]
      }
    }
    maps[item.id] = item
    index++
  }
  return result
}

// 格式化金额/100
export const divide = money => {
  if (!money) return '0.00'
  if (isNaN(money)) return '0.00'
  if (typeof money === 'number') {
    return NP.divide(money, 100).toFixed(2)
  } else if (typeof money === 'string' && !isNaN(Number(money))) {
    money = Number(money)
    return NP.divide(money, 100).toFixed(2)
  } else {
    return money
  }
}

// 元转分
export const times = money => {
  if (!money) return 0
  if (isNaN(money)) return 0
  return NP.times(money, 100)
}

export const formatWallet = data => {
  data.wallet_balance = []
  data.wallet_subsidy_balance = []
  data.wallet_complimentary_balance = []
  data.wallet.map(walletItem => {
    data.wallet_balance.push({
      source_organization: walletItem.source_organization,
      balance: divide(walletItem.balance)
    })
    data.wallet_subsidy_balance.push({
      source_organization: walletItem.source_organization,
      subsidy_balance: divide(walletItem.subsidy_balance)
    })
    data.wallet_complimentary_balance.push({
      source_organization: walletItem.source_organization,
      complimentary_balance: divide(walletItem.complimentary_balance)
    })
  })

  data.balance_total = 0
  data.subsidy_balance_total = 0
  data.complimentary_balance = 0
  data.wallet_balance.map(balance => {
    data.balance_total = NP.plus(data.balance_total, Number(balance.balance)).toFixed(2)
  })
  data.wallet_subsidy_balance.map(subsidy => {
    data.subsidy_balance_total = NP.plus(
      data.subsidy_balance_total,
      Number(subsidy.subsidy_balance)
    ).toFixed(2)
  })
  data.wallet_complimentary_balance.map(discount => {
    data.complimentary_balance = NP.plus(
      data.complimentary_balance,
      Number(discount.complimentary_balance)
    ).toFixed(2)
  })
  console.log('232323')
  return data
}

export const deleteEmptyGroup = data => {
  function traversal(data) {
    data.map(item => {
      if (item.children_list) {
        if (item.children_list.length > 0) {
          traversal(item.children_list)
        } else {
          delete item.children_list
        }
      } else {
        delete item.children_list
      }
    })
  }
  traversal(data)
  return data
}

export const getRequestParams = (searchFormSetting, page, pageSize) => {
  const params = {}
  Object.keys(searchFormSetting).forEach(key => {
    if (
      key !== 'select_time' &&
      searchFormSetting[key].value !== '' &&
      searchFormSetting[key].value
    ) {
      params[key] = searchFormSetting[key].value
    } else if (typeof searchFormSetting[key].value === 'boolean') {
      params[key] = searchFormSetting[key].value
    }
  })
  if (page !== undefined) {
    params.page = page
  }
  if (pageSize !== undefined) {
    params.page_size = pageSize
  }

  if (searchFormSetting.select_time?.value?.length === 2) {
    params.start_date = searchFormSetting.select_time.value[0]
    params.end_date = searchFormSetting.select_time.value[1]
  }

  return params
}

// 是否是本组织
export const isCurrentOrgs = function(data) {
  return data.organization.includes(this.$store.getters.organization)
}

export const isCurrentOrg = function(data) {
  // const orgs = Object.keys(this.$store.getters.userInfo.orgs)
  return data.organization === this.$store.getters.organization
}

// query参数转下码，防止明文在地址栏显示
export const encodeQuery = function(data) {
  if (typeof data !== 'string') {
    return encodeURIComponent(JSON.stringify(data))
  } else {
    return encodeURIComponent(data)
  }
}
// query参数解码对应 encodeQuery
export const decodeQuery = function(data) {
  let query = null
  data = decodeURIComponent(data)
  try {
    query = JSON.parse(data)
  } catch (error) {
    console.error('解析urlQuery失败！')
  }
  return query
}

// 替换下字符串，防止JSON.parse转换失败, 单引转双引
export const replaceSingleQuote = function(data) {
  return data.replaceAll("'", '"')
}

// 将HTML特殊字符转换成等值的实体
export function escapeHTML(str) {
  var escapeChars = {
    '<': 'lt',
    '>': 'gt',
    '"': 'quot',
    '&': 'amp',
    "'": '#39'
  }
  return str.replace(new RegExp('[' + Object.keys(escapeChars).join('') + ']', 'g'), function(
    match
  ) {
    return '&' + escapeChars[match] + ';'
  })
}

// 实体字符转换为等值的HTML。
export function unescapeHTML(str) {
  var htmlEntities = {
    nbsp: ' ',
    lt: '<',
    gt: '>',
    quot: '"',
    amp: '&',
    apos: "'"
  }
  // eslint-disable-next-line no-useless-escape
  return str.replace(/\&([^;]+);/g, function(match, key) {
    if (key in htmlEntities) {
      return htmlEntities[key]
    }
    return match
  })
}

/**
 * @description a标签下载
 * @param {*} url
 * @param {*} isBlob
 * @param {*} name
 */
export function download(url, isBlob, name) {
  let aTag = document.createElement('a')
  let blob = null
  if (isBlob) { // 当isBlob为true, url传进来的应该是内容
    // blob
    let content = url
    blob = new Blob([content])
    aTag.href = URL.createObjectURL(blob)
    // 释放createObjectURL
  } else {
    let fileName = null
    if (name) {
      fileName = name
    } else {
      // 新版文件会带?tk=xxxx的后缀参数需要截掉
      let spliturl = url.split('/')
      fileName = spliturl[spliturl.length - 1]
      let index = fileName.indexOf('?')
      if (index > -1) {
        fileName = fileName.substring(0, index)
      }
    }
    aTag.download = fileName
    aTag.href = url
  }
  aTag.click()
  if (isBlob) {
    URL.revokeObjectURL(blob)
  }
}

/**
 * @description 删除空字符串字段
 * @param {*} data
 * @returns Object
 */
export const deleteEmptyKey = function(data) {
  Object.keys(data).forEach(key => {
    if (data[key] === '') {
      delete data[key]
    }
  })
  return data
}

/**
 * @description 获取日期是一年中的第几周（与 Element UI 保持一致）
 * @param {Date} date 日期对象
 * @returns {Number} 周数
 */
export function getWeek(date) {
  if (!date) return null;

  // 确保输入是 Date 对象
  const targetDate = new Date(date);

  // 设置为当天 0 点
  targetDate.setHours(0, 0, 0, 0);

  // 计算当前日期是一周中的第几天（0 是周日，1 是周一，...）
  const day = targetDate.getDay() || 7;

  // 将日期调整到本周的周四（ISO 8601 标准使用周四来确定一周属于哪一年）
  targetDate.setDate(targetDate.getDate() + 1 - day);

  // 计算今年第一天
  const firstDayOfYear = new Date(targetDate.getFullYear(), 0, 1);

  // 计算目标日期与今年第一天的天数差
  const dayDiff = Math.floor((targetDate - firstDayOfYear) / (24 * 60 * 60 * 1000));

  // 计算周数（第一周的周四与第一天的天数差除以7，再加1）
  const weekNumber = Math.floor(dayDiff / 7) + 1;

  return weekNumber;
}

// 获取文件后缀名
export function getSuffix(filename) {
  let pos = filename.lastIndexOf('.')
  let suffix = ''
  if (pos !== -1) {
    suffix = filename.substring(pos).toLowerCase()
  }
  return suffix
}

/**
 * @description 获取url文件名
 * @param {string} url
 * @returns string
 */
export function getUrlFilename(url) {
  let pos = url.lastIndexOf('/')
  let suffix = ''
  if (pos !== -1) {
    suffix = url.substring(pos + 1).toLowerCase()
  }
  return suffix
}

/**
 * 清除空节点方法
 * @param {*} data
 */
export function setRemoveEmptyLevel(data, leveLName) {
  if (data !== null && Array.isArray(data) && data.length > 0) {
    data.forEach(v => {
      if (Reflect.has(v, leveLName) && (v[leveLName] === null || (Array.isArray(v[leveLName]) && v[leveLName].length === 0))) {
        delete v[leveLName]
      } else {
        setRemoveEmptyLevel(v[leveLName], leveLName)
      }
    })
  }
}

// 格式化文字 超过多少个字显示...
export function textFormat(text, number) {
  if (!text) return
  let subStr = text.slice(0, number)
  subStr = subStr + (text.length > number ? '...' : '')
  return subStr
}
/**
 * 大驼峰转下划线
 */
export function bigCamelToUnderline(str) {
  var temp = str.replace(/[A-Z]/g, function (match) {
    return "_" + match.toLowerCase();
  });
  if (temp.slice(0, 1) === '_') { // 如果首字母是大写，执行replace时会多一个_，这里需要去掉
    temp = temp.slice(1);
  }
  return temp;
}

// 判断是否是移动端
export function isMobile() {
  let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i);
  // 小屏幕也调到移动端
  if (!flag) {
    flag = window.matchMedia("(max-width: 500px)").matches
  }
  return flag;
}

/**
 * @description 判断时间or数字范围是否重叠, true重叠，false不重叠
 * @param {array} arr1 [start, end]
 * @param {array} arr2 [start, end]
 * @returns Boolean
 */
export function checkArrayOverlap (arr1, arr2) {
  const max = [arr1[0], arr2[0]];
  const min = [arr1[1], arr2[1]];
  return (Math.max.apply(null, max) <= Math.min.apply(null, min))
}

// 转换 例如10000 1w 113000 11.3w
export function formatNumberWithUnit(number) {
  const units = ['', 'w'];
  if (number) {
    if (number >= 100000) {
      const unitIndex = 1;
      const value = number / Math.pow(10000, unitIndex);
      const formattedValue = value.toFixed(1);
      const unit = units[unitIndex];
      return `${formattedValue}${unit}`;
    } else {
      const formattedNumber = number.toFixed(1);
      return formattedNumber;
    }
  } else {
    return 0;
  }
}
// 生成13位数  22年份 0825当天 16:59:00当前时间段 一个随机数
export function generateUniqueID() {
  // 获取当前日期和时间
  const currentDate = new Date();
  // 获取年份
  const year = String(currentDate.getFullYear()).slice(2); // 获取年份的后两位
  // 获取月份，如果月份小于 10，前面添加 0
  const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
  // 获取日期，如果日期小于 10，前面添加 0
  const day = currentDate.getDate().toString().padStart(2, '0');
  // 获取当前时间的小时、分钟和
  const hours = currentDate.getHours().toString().padStart(2, '0');
  const minutes = currentDate.getMinutes().toString().padStart(2, '0');
  // 毫秒
  const milliseconds = String(currentDate.getMilliseconds()).padStart(3, '0').substring(0, 2);
  // 生成一个 1 位的随机数
  const randomDigit = Math.floor(Math.random() * 10);
  // 拼接所有部分生成 13 位数
  const uniqueID = year + month + day + hours + minutes + milliseconds + randomDigit;
  return uniqueID;
}

/**
 * @description 获取一个str字符串的字节长度。
 * @params {String} str 字符串。
 * @returns {Number} 字符串的字节长度。
 */
export function getStringByte(str) {
  // 当前url的字节长度。
  let byteLength = 0;
  for (let i = 0, len = str.length; i < len; i++) {
    // 转化为Unicode编码
    let charCode = str.charCodeAt(i);
    if (charCode < 0x007f) {
      // ASCII字符，占一个字节
      byteLength++;
    } else if ((charCode >= 0x0080) && (charCode <= 0x07ff)) {
      // 2字节的UTF-8字符
      byteLength += 2;
    } else if ((charCode >= 0x0800) && (charCode <= 0xffff)) {
      // 3字节的UTF-8字符
      byteLength += 3;
    } else if ((charCode >= 0x10000) && (charCode <= 0x1FFFFF)) {
      // 4字节的UTF-8字符
      byteLength += 4;
    }
  }
  return byteLength;
}

/**
 * @description 判断url字符串的字节长度是否过长
 * @param {String} url url字符串
 * @returns {Boolean}
 */
export function urlIsLong(url) {
  if (typeof url !== 'string') {
    throw new Error('url must be a string!');
  }
  let strByte = getStringByte(url)
  console.log('url byte：' + strByte)
  // 一般情况不允许超过2000吧（这是考虑ie的情况，限制都不兼容id浏览器了）
  return strByte > 2000
}

/**
 * @deprecated 文件大小格式化
 * @param {*} a
 * @param {*} b
 * @returns string
 */
export function bytesToSize(a, b) {
  if (a === 0) {
    return "0 Bytes";
  }
  // eslint-disable-next-line one-var
  let c = 1024,
    d = b || 2,
    e = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"],
    f = Math.floor(Math.log(a) / Math.log(c));
  return parseFloat((a / Math.pow(c, f)).toFixed(d)) + " " + e[f]
}

/**
 * @description 处理下form prop数据，返回切割的arr
 * @param {*} path
 * @returns array | boolean
 */
export function getPropByPath(path) {
  if (path) {
    // path 的数据格式可能有aa[1].bb，aa.1.bb，.aa 等三种
    path = path.replace(/\[(\w+)\]/g, '.$1');
    path = path.replace(/^\./, '');
    return path.split('.');
  } else {
    return false
  }
};

/**
 * @description 身份证反显年龄
 * @param {String}
 */
export function getAgeToIden(iden) {
  let val = iden.length
  let myDate = new Date()
  let month = myDate.getMonth() + 1
  let day = myDate.getDate()
  let age = 0
  if (val === 18) {
    age = myDate.getFullYear() - iden.substring(6, 10) - 1
    if (
      iden.substring(10, 12) < month ||
      (iden.substring(10, 12) === month && iden.substring(12, 14) <= day)
    ) {
      age++
    }
  }
  return age
}
