<template>
  <div class="verify-main">
    <el-popover placement="bottom" title="" v-model="visible">
      <div class="verify-container" :style="{ width: `${width}px` }">
        <div class="verify-head">请完成安全认证</div>
        <div class="refresh" @click="refresh">
          <svg t="1637315258145" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
            p-id="2420" width="20" height="20">
            <path
              d="M960 416V192l-73.056 73.056a447.712 447.712 0 0 0-373.6-201.088C265.92 63.968 65.312 264.544 65.312 512S265.92 960.032 513.344 960.032a448.064 448.064 0 0 0 415.232-279.488 38.368 38.368 0 1 0-71.136-28.896 371.36 371.36 0 0 1-344.096 231.584C308.32 883.232 142.112 717.024 142.112 512S308.32 140.768 513.344 140.768c132.448 0 251.936 70.08 318.016 179.84L736 416h224z"
              p-id="2421" fill="#8a8a8a"></path>
          </svg>
        </div>
        <div class="pic">
          <img :src="bgImg">
          <canvas class="canvas" ref="canvas" :width="width" :height="height" @click="createPointer"></canvas>
          <span class="pointer" v-for="(item, index) in pointer" :style="{ left: `${item.x}px`, top: `${item.y}px` }"
            :key="index">
            <i>{{ index + 1 }}</i>
          </span>
        </div>
        <div :class="['toolbar', state]">
          <p v-if="state === 'fail'">验证失败</p>
          <p v-else-if="state === 'success'">验证通过</p>
          <p v-else>请顺序点击<span v-for="(item, index) in tips" :key="index">{{ "【" + item.character + "】" }}</span></p>
        </div>
      </div>
    </el-popover>
  </div>
</template>
<script>
import { deepClone } from '@/utils'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    width: {
      type: Number,
      default: 320
    },
    height: {
      type: Number,
      default: 160
    },
    fontStrContent: {
      type: String,
      default: '赵钱孙李周吴郑王朱秦尤许何吕施张孔曹严华金魏陶姜戚谢邹喻柏水窦章云苏潘葛奚范彭郎鲁韦昌马苗凤花方俞任袁柳酆鲍史唐'
    },
    fontNum: { // 显示几个
      type: Number,
      default: 4
    },
    checkNum: { // 点击验证数
      type: Number,
      default: 2
    },
    accuracy: { // 精度
      type: Number,
      default: 15
    },
    isNumber: {
      type: Boolean,
      default: false
    },
    images: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      bgImg: null, // 背景图
      ctx: null, // 背景画笔
      fontArr: [], // 显示的字符
      tips: [], // 提示文字
      pointer: [], // 点击序号
      state: '', // success fail active
      timeIns: null,
      imagesList: [
        require('@/assets/img/ic_bg_img1.png'), require('@/assets/img/ic_bg_img2.png'), require('@/assets/img/ic_bg_img3.png'), require('@/assets/img/ic_bg_img4.png')
      ],
      fontStr: this.fontStrContent,
      answerList: [] // 答案
    }
  },
  watch:{
    visible(newValue){
      if (newValue) {
        this.reset()
      }
    }
  },
  mounted() {
    console.log("mounted verfy code");
    this.init()
  },
  beforeDestroy() {
    this.reset()
    clearTimeout(this.timeIns)
  },
  methods: {
    init() {
      this.ctx = this.$refs.canvas.getContext('2d');
      console.log("init");
      this.getImg();
    },
    getImg() {
      const imagesLen = this.imagesList.length;
      const randomIndex = Math.floor(Math.random() * imagesLen);
      this.bgImg = this.imagesList[randomIndex];
      this.draw();
      console.log(this.bgImg)
    },
    draw() {
      this.fontStr = this.isNumber ? '0123456789' : this.fontStrContent
      // 设置结果集
      let resultList = []
      // 如果有答案，直接使用答案
      if (this.answerList.length > 0) {
        resultList = deepClone(this.answerList)
      }
      // 获取答案以外的随机字符
      for (let i = 0; i < (this.fontNum - this.answerList.length); i++) {
        resultList.push(this.getRandomCharacter(resultList))
      }
      console.log("resultList", resultList, this.answerList);
      // 打乱结果集
      resultList = this.shuffleArray(resultList)
      // 给结果集增加随机xy坐标
      for (let i = 0; i < resultList.length; i++) {
        let character = resultList[i]
        console.log(character)
        const fontSize = this.randomNum(36, this.height * 1 / 4);
        const fontWeight = 'bold'
        const fontStyle = 'italic'
        const fontFamily = 'sans-serif'
        const x = this.width / this.fontNum * i + 10;
        const y = Math.random() * (this.height - fontSize);

        this.ctx.fillStyle = this.randomColor(0, 255);
        this.ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`;
        this.ctx.textBaseline = 'top';
        this.ctx.fillText(character, x, y);

        this.fontArr.push({
          character,
          // fontSize,
          x,
          y
        })
      }

      console.log("this.fontArr", this.fontArr)
      // 展示结果集内容
      if (this.answerList.length > 0) {
        for (let i = 0; i < this.answerList.length; i++) {
          const randomIndex = this.fontArr.findIndex(item => {
            return item.character === this.answerList[i];
          })
          const character = this.fontArr.splice([randomIndex], 1)[0];
          this.tips.push(character);
          // console.log(character, this.fontArr)
        }
      } else {
        for (let i = 0; i < this.checkNum; i++) {
          const randomIndex = Math.floor(Math.random() * this.fontArr.length);
          const character = this.fontArr.splice([randomIndex], 1)[0];
          this.tips.push(character);
          // console.log(character, this.fontArr)
        }
      }
      console.log(this.tips)
    },
    // 获取随机字符
    getRandomCharacter(list) {
      const fontStrLen = this.fontStr.length;
      const randomIndex = Math.floor(Math.random() * fontStrLen);
      const character = this.fontStr.charAt([randomIndex]);
      // debugger
      const isSome = list.some(item => {
        console.log("isSome", item, character);
        return parseInt(item) === parseInt(character);
      })
      if (isSome) {
        console.log(`>>>${character}已存在>>>`)
        return this.getRandomCharacter(list);
      } else {
        return character;
      }
    },
    randomColor(min, max) {
      // let r = this.randomNum(min, max)
      // let g = this.randomNum(min, max)
      // let b = this.randomNum(min, max)
      let colors = ['#FF3030', '#FB32FF', '#FFFFFF', '#4AFF67', '#150F0D', '#9D50FF', '#12FFD4', '#FFF500', '#2238FF']
      let color = colors[Math.floor(Math.random() * colors.length)]
      // return 'rgb(' + r + ',' + g + ',' + b + ')'
      return color
    },
    randomNum(min, max) {
      return Math.floor(Math.random() * (max - min) + min)
    },
    createPointer(e) {
      // console.log(e)
      // const canvasRect = this.$refs.canvas.getBoundingClientRect();
      const x = e.offsetX - 15;
      const y = e.offsetY - 15;

      if (this.pointer.length < this.tips.length) {
        this.pointer.push({ x, y });
        // console.log(this.pointer.length)
        // this.verify()
        this.state = 'active'
      }
      if (this.pointer.length === this.tips.length) {
        const isPass = this.verify();
        if (isPass) {
          this.state = 'success';
          this.timeIns = setTimeout(() => {
            this.reset()
            this.$emit("success", this.tips)
          }, 500)
        } else {
          this.state = 'fail';
          // 如果失败则1000毫秒后重置
          this.timeIns = setTimeout(() => {
            this.reset()
          }, 1000)
        }
      }
    },
    // 判断精度
    verify() {
      console.log("验证")
      const result = this.pointer.every((item, index) => {
        const _left = item.x > this.tips[index].x - this.accuracy;
        const _right = item.x < this.tips[index].x + this.accuracy;
        const _top = item.y > this.tips[index].y - this.accuracy;
        const _bottom = item.y < this.tips[index].y + this.accuracy;
        return _left && _right && _top && _bottom;
      })
      console.log(result)
      return result;
    },
    // 重置
    reset() {
      this.fontArr = [];
      this.tips = [];
      this.pointer = [];
      this.state = '';
      this.ctx.clearRect(0, 0, this.width, this.height);
      this.getImg();
    },
    // 打乱数据
    shuffleArray(array) {
      for (let i = array.length - 1; i > 0; i--) {
        // 生成一个从0到i的随机索引
        const j = Math.floor(Math.random() * (i + 1));
        // 交换当前元素与随机索引处的元素
        [array[i], array[j]] = [array[j], array[i]];
      }
      return array;
    },
    // 刷新
    refresh() {
      this.$emit('refresh')
    },
    // 设置答案
    setAnswerList(list) {
      if (!list || !Array.isArray(list)) {
        return
      }
      this.answerList = deepClone(list)
    }
  }
}
</script>
<style lang="scss" scoped>
.verify-main {

  ::v-deep.el-popover {
    background-color: transparent;
    border: none !important;
    padding: 0 !important;
  }
}

.verify-container {
  /*border: 1px solid #e4e4e4;*/
  position: relative;
  overflow: hidden;
  user-select: none;
  z-index: 997;
  box-shadow: 0 0 11px 0 #999;
  border-radius: 5px;
  background-color: white;
}

.pic {
  position: relative;

  & img {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 320px;
    height: 160px;
  }
}

.canvas {
  display: block;
}

.pointer {
  background: #1abd6c;
  border-radius: 50%;
  padding: 15px;
  position: absolute;
}

.pointer i {
  color: #fff;
  font-style: normal;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}

.toolbar {
  width: 100%;
  height: 40px;
  border: 1px solid #e4e4e4;
  background: #f7f7f7;
  color: #666;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.toolbar.active {
  color: #fff;
  background: #1991FA;
  border: 1px solid #1991FA;
}

.toolbar.success {
  color: #fff;
  background: #52CCBA;
  border: 1px solid #52CCBA;
}

.toolbar.fail {
  color: #fff;
  background: #f57a7a;
  border: 1px solid #f57a7a;
}

.refresh {
  display: flex;
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 2;
  cursor: pointer;
}

.verify-head {
  padding: 10px;
  background-color: white;
}
</style>
