<template>
  <el-select
    v-model="selectData"
    class="user-group_select ps-select"
    :popper-class="popperClass"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-option
      v-for="item in groupList"
      :key="item[options.value]"
      :label="item[options.label]"
      :value="item[options.value]"
    ></el-option>
  </el-select>
</template>

<script>
export default {
  name: 'OrganizationConsumeList',
  props: {
    value: {
      type: [String, Array, Number]
    },
    popperClass: {
      type: String,
      default: 'ps-popper-select'
    },
    options: {
      type: Object,
      default: () => ({
        label: 'label',
        value: 'id'
      })
    }
  },
  data() {
    return {
      groupList: []
    }
  },
  computed: {
    selectData: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('update:input', val)
      }
    }
  },
  mounted() {
    this.requestOrganizationConsumeList()
  },
  methods: {
    async requestOrganizationConsumeList() {
      const res = await this.$apis.apiBackgroundOrganizationOrganizationConsumeListPost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        this.groupList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
