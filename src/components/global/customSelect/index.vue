<template>
  <el-select
    v-model="selectval"
    class="custom-select ps-select"
    :multiple="multiple"
    popper-class="ps-popper-select custom-popper-select"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-option v-for="item in selectList" :key="item[valkey]" :label="item[name]" :value="item[valkey]">
      <slot>
        <div v-if="shape === 'square'" class="custom-inner">
          <span class="custom-label">{{ item[name] }}</span>
          <span :class="['custom-checked', { 'is-select': selectValHandle(item[valkey]) }]"></span>
        </div>
      </slot>
    </el-option>
  </el-select>
</template>

<script>
export default {
  inheritAttrs: false,
  name: 'customSelect',
  props: {
    selectList: {
      type: Array,
      default() {
        return []
      }
    },
    select: {
      type: [String, Array, Number],
      require: true
    },
    valkey: {
      type: String,
      default: 'id'
    },
    name: {
      type: String,
      default: 'name'
    },
    multiple: {
      type: Boolean,
      default: false
    },
    shape: { // 自定义形状
      type: String,
      default: '' // square 显示方形
    }
  },
  data() {
    return {}
  },
  computed: {
    selectval: {
      get() {
        return this.select
      },
      set(val) {
        this.$emit('update:select', val)
      }
    },
    isLoading: {
      get() {
        return this.loading
      },
      set(val) {
        this.$emit('update:loading', val)
      }
    }
  },
  watch: {
    // $route: {
    //   handler: function(route) {
    //     const query = route.query
    //   },
    //   immediate: true
    // }
  },
  created() {},
  mounted() {},
  updated() {
  },
  methods: {
    selectValHandle(key) {
      if (this.multiple) {
        return this.select.includes(key)
      } else {
        return this.select === key
      }
    }
  }
}
</script>

<style lang="scss">
.custom-select {
}
.custom-popper-select {
  .selected .custom-label {
    // color: #ff9b45;
    color: #606266;
  }
  .custom-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .custom-checked {
      display: inline-block;
      position: relative;
      border: 1px solid #dcdfe6;
      border-radius: 2px;
      box-sizing: border-box;
      width: 14px;
      height: 14px;
      background-color: #fff;
      z-index: 1;
      transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46),
        background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);
      &:after {
        box-sizing: content-box;
        content: ' ';
        border: 1px solid #fff;
        border-left: 0;
        border-top: 0;
        height: 7px;
        left: 4px;
        position: absolute;
        top: 1px;
        transform: rotate(45deg) scaleY(0);
        width: 3px;
        transition: transform 0.15s ease-in 0.05s;
        transform-origin: center;
      }
      &.is-select{
        background-color: #ff9b45;
        border-color: #ff9b45;
        &:after {
          transform: rotate(45deg) scaleY(1);
        }
      }
    }
  }
  .selected{
    background-color: rgba(255, 155, 69, 0.18);
    color: #606266;
  }
}
</style>
