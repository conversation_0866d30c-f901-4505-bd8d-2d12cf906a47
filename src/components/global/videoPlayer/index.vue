<template>
  <div class="videoPlayer">
    <video :width="width" :height="height" controls>
      <source
        :src="src"
        type="video/mp4"
      />
    </video>
  </div>
</template>

<script>
export default {
  name: 'videoPlayer',
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    src: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    }
  },
  data() {
    return {}
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>
<style lang="scss" scoped></style>
