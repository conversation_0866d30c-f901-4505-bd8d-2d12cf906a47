<template>
  <div calss="ps-dialog-box">
    <el-dialog
      custom-class="ps-page-dialog ps-dialog"
      :title="title"
      :visible.sync="visible"
      :width="width"
      :top="top"
      :close-on-click-modal="false"
      :show-close="!isLoading"
      @closed="handleClose">
      <div class="content" v-loading="isLoading">
        <import-page ref="importPageRef" :loading.sync="isLoading" :show-title="false" :show-import-result="showResult" :tools-border="toolsBorder" :show-operation="showOperation" :open-excel-page="openExcelPage" :show-footer="showFooter" :max-table-height="maxTableHeight" :confirm="importPageConfirm" :is-upload="isUpload" v-bind="$attrs"></import-page>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="isLoading" class="ps-cancel-btn" @click="clickCancleHandle">{{cancelText}}</el-button>
        <el-button :disabled="isLoading" class="ps-origin-btn" type="primary" @click="clickConfirmHandle">{{confirmText}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import importPage from "../importPage";
export default {
  name: 'importPageDialog',
  components: { importPage },
  props: {
    show: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      default: '导入'
    },
    width: {
      type: String,
      default: '850px'
    },
    top: {
      type: String,
      default: '8vh'
    },
    loading: {
      type: Boolean,
      default: false
    },
    confirmText: {
      type: String,
      default: '确 定'
    },
    cancelText: {
      type: String,
      default: '取 消'
    },
    showResult: {
      type: Boolean,
      default: false
    },
    toolsBorder: {
      type: Boolean,
      default: true
    },
    showOperation: {
      type: Boolean,
      default: true
    },
    openExcelPage: {
      type: Boolean,
      default: true
    },
    showFooter: {
      type: Boolean,
      default: false
    },
    maxTableHeight: {
      type: String,
      default: '400px'
    },
    isUpload: { // 是否需要上传文件
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      time: new Date().getTime(),
      isLoading: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  watch: {
    loading (val) {
      this.isLoading = val
    }
  },
  created () {
  },
  mounted () {
  },
  methods: {
    importPageConfirm(e) {
      this.$emit('confirm')
      this.visible = false
    },
    clickConfirmHandle() {
      const result = this.$refs.importPageRef.uploadStartHandle()
      console.log(result, '9584565')
      if (result) {
        this.$emit('confirm', result)
      }
      if (!this.isUpload) {
        // this.$refs.importPageRef.resetHandle()
        // this.visible = false
      }
    },
    reset() {
      this.$refs.importPageRef.resetHandle()
    },
    clickCancleHandle() {
      this.$refs.importPageRef.resetHandle()
      this.visible = false
      this.$emit('cancel')
    },
    handleClose(e) {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss">
.ps-page-dialog{
  .el-dialog__body{
    padding-top: 0;
  }
  .import-page{
    box-shadow: none;
    .import-content{
      padding: 10px 0;
    }
    .table-container{
      margin: 0;
      padding-bottom: 0;
    }
  }
  .dialog-footer{
    width: 100%;
    text-align: right;
  }
}
</style>
