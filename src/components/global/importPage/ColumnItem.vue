<template>
  <el-table-column
    :prop="col.key"
    :label="col.label"
    :align="col.align?col.align:''"
    :fixed="col.fixed?col.fixed:false"
    >
    <template v-if="col.children">
      <column-item v-for="(item, index) in col.children" :key="index" :col="item" v-on="$listeners"></column-item>
    </template>
    <template slot-scope="scope">
      <el-button
        v-if="col.type === 'operation' && !(noDeleteFirst && scope.$index === 0)"
        type="text"
        size="small"
        class="ps-warn-text"
        @click="deleteHandle(scope.row, scope.$index)"
      >删除</el-button>
      <span v-else>{{ scope.row[col.key] }}</span>
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: "columnItem",
  props: {
    col: {
      type: Object
    },
    noDeleteFirst: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    deleteHandle(row, index) {
      this.$emit('delete', { row, index })
    }
  }
}
</script>

<style scoped>
</style>
