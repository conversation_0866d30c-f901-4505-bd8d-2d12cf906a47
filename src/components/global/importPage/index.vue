<template>
  <div class="import-page" v-loading="isLoading" element-loading-text="导入数据中...">
    <!-- title start -->
    <div v-if="showTitle" class="import-title">
      <span>{{ title }}</span>
    </div>
    <!-- title end -->
    <!-- tools start -->
    <div class="import-content">
      <div class="import-tools">
        <div class="tools-tips">{{ tips }}</div>
        <div :class="['tools-wrapper', toolsBorder?'tools-border':'']">
          <div class="step-line"></div>
          <div class="tools-l step">
            <div class="step-icon">
              <div class="step-icon-box">
                <span :class="{'active': step===1}">1</span>
              </div>
            </div>
            <div class="step-tips">下载数据模板</div>
            <div class="step-btn">
              <el-button
                size="mini"
                type="primary"
                class="ps-green-btn"
                icon="el-icon-download"
                :loading="templateLoading"
                @click="downloadExcel(templateUrl)"
              >
                下载
              </el-button>
              <!-- <button-icon color="green" type="down">下载</button-icon> -->
            </div>
          </div>
          <div class="tools-r step">
            <div class="step-icon">
              <div class="step-icon-box">
                <span :class="{'active': step===2}">2</span>
              </div>
            </div>
            <div class="step-tips">导入文件</div>
            <div class="step-btn">
              <parse-excel titleName="选择本地文件" :initial="initial" @excel="getXlsxData" btnType="">
                选择本地文件
              </parse-excel>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- tools end -->
    <div class="status-tools clearfix" v-if="showResult">
      <div class="status-tab float-l">
        <div :class="['tab', tabType===1?'active':'']" @click="clickTabHandler(1)">导入成功</div>
        <div :class="['tab', tabType===0?'active':'']" @click="clickTabHandler(0)">导入失败</div>
      </div>
      <button-icon class="float-r" color="origin" type="export" @click="exportHandler">导出EXCEL</button-icon>
    </div>
    <!-- slot start-->
    <!-- default -->
    <slot>
      <div class="table-container">
        <el-table ref="tableRef" :key="mergesList.length"  border :data="currentTableData" :max-height="maxTableHeight">
          <el-table-column v-if="tabType===0 && importData.length" label="失败原因" prop="result" align="center">
            <template slot-scope="scope">
              <span class="warn-text">{{ scope.row.result }}</span>
            </template>
          </el-table-column>
          <column-item v-for="item in tableSetting" :key="item.key" :col="item" @delete="deleteHandle"></column-item>
        </el-table>
        <div class="pageSizeItem" v-if="importData.length" >
          <pagination
            :onPaginationChange="onPaginationChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :layout="'total, prev, pager, next, jumper'"
            :total="importData.length"
          ></pagination>
        </div>
      </div>
    </slot>
    <!-- footer -->
    <slot name="footer">
      <div
        v-if="showFooter && showImportBtn && importData.length"
        class="footer"
      >
        <!-- <el-button style="width: 120px;">取消</el-button> -->
        <el-button
          class="ps-origin-btn"
          style="width: 120px;"
          type="primary"
          @click="uploadStartHandle"
        >
          确定导入
        </el-button>
      </div>
    </slot>
    <!-- slot end -->
  </div>
</template>

<script>
import FileSaver from 'file-saver'
// import http from 'axios'
import http from '@/utils/request'
import XLSX from 'xlsx'
import { to, deepClone } from '@/utils'
// import xlsxData from './header.json'
// import merges from './merges.json'
import columnItem from './ColumnItem'
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: 'importPage',
  mixins: [exportExcel],
  components: {
    columnItem
  },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    type: String,
    // 显示title
    showTitle: {
      type: Boolean,
      default: true
    },
    title: {
      // title
      type: String,
      default: '导入'
    },
    tips: {
      // 提示
      type: String,
      default:
        '注意：导入的数据模板，需要按照平台提供的模板进行！导入前，请检查导入表格的格式和内容！'
    },
    showImportResult: { // 是否显示导入结果，true在页面显示结果，false自动下周excel表
      type: Boolean,
      default: true
    },
    templateUrl: {
      // 模板地址
      type: String,
      default:
        location.origin + '/api/temporary/template_excel/%E5%8D%A1%E5%8A%A1%E6%A8%A1%E6%9D%BF/%E5%AF%BC%E5%85%A5%E7%94%A8%E6%88%B7.xls'
    },
    url: {
      // 导入的接口
      // require: true,
      type: String,
      default: ''
    },
    paramsKey: { // 传给接口的key
      type: String,
      default: 'oss_url'
    },
    initial: { // 返回原始数据
      type: Boolean,
      default: true
    },
    headerLen: { // 设置模板头部有多少行，必须设，不然没法算, 目前只支持三级合并表头，即tableSetting的children的deep为3
      type: Number,
      default: 0
    },
    // 顶部下载操作栏边框
    toolsBorder: {
      type: Boolean,
      default: false
    },
    // 是否显示操作栏
    showOperation: {
      type: Boolean,
      default: false
    },
    // 是否打开查询导入结果界面，优先级比showImportResult这些高
    openExcelPage: {
      type: Boolean,
      default: false
    },
    // 是否显示底部操作栏
    showFooter: {
      type: Boolean,
      default: true
    },
    maxTableHeight: {
      type: String,
      default: 'auto'
    },
    confirm: Function,
    paramsData: { // 导入链接的参数，有些导入链接需要传参数的。就在这个字段传咯
      type: Object,
      default: () => {
        return {}
      }
    },
    resultLabel: { // 结果表头名称
      type: String,
      default: '导入结果'
    },
    resultExampleLine: { // 例子默认的模板行数，一般都是一行，有些是两行
      type: Number,
      default: 1
    },
    isResultMulTitle: { // 是否是多表头列表表头结果，如果是的话，要设置这个字段为true
      type: Boolean,
      default: false
    },
    isUrlDownloadResult: {
      type: Boolean, //  是否使用url的方式下载结果，默认false
      default: false
    },
    isDeleteFirst: {
      type: Boolean, //  是否删除第一条数据 “例如”
      default: false
    },
    // 默认要删除第几行数据的下标，注意合并表格的数据不能使用这个方法
    deleteIndexs: {
      type: Array,
      default: () => []
    },
    isUpload: { // 是否需要上传文件
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isLoading: false,
      step: 1, // 步进条
      templateLoading: false, // 模板的下载状态
      prefix: '', // 根据文件名定义的参数
      uploadParams: {
        prefix: 'xlsx'
      }, // 阿里云的上传参数
      uploadUrl: '', // 阿里云的上传地址
      uploadFileName: '', // 文件名
      filePrefix: '', // 阿里云文件上传的的路径
      tableSetting: [], // table动态生成的字段
      importData: [], // 导入的数据/显示的数据
      resultData: [], // 导入结果的数据
      initialXlsxData: [], // 读取到的xlsx原始值
      xlsxData: [], // 转换后端值
      showResult: false, // 是否显示导入结果，true在页面显示结果，false自动下载excel表
      showImportBtn: true, // 是否显示导入按钮
      tabType: 1, // tab 类型，1成功0失败
      timer: null, // 定时器
      queryId: '', // queryid
      downLoadExcelUrl: '', // 下载的链接
      currentPage: 1,
      pageSize: 12,
      totalCount: 0,
      mergesList: [],
      uploadStep: '' // 上传到哪一步了 read，upload
    }
  },
  computed: {
    // 当前的分页数据
    currentTableData() {
      return this.importData.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize)
    }
  },
  watch: {
    isLoading(val) {
      console.log('isLoading', val)
      this.$emit('update:loading', val)
    }
  },
  created() {},
  mounted() {
    // this.getResultXlsx('https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/cashier_v4/import/xlsx/20230810/export/20230810/%E8%8F%9C%E8%B0%B1%E5%AF%BC%E5%85%A5%E8%8F%9C%E5%93%81168990dba74ff95.xlsx?_tk=bc5d321d60a26fb6586be23ee3b7437f')
    // this.getResultXlsx(
    //   'http://192.168.50.147:8080/api/temporary/generate/xlsx/20221208/16414c6ec78948b.xlsx?_tk=bab6f6e1a1367e59c8452709483a3b98'
    // )
    // this.initXlsx()
  },
  methods: {
    // 下载模板
    downloadExcel(url) {
      this.step = 1
      // window.open(this.templateUrl);
      let spliturl = url.split('/')
      let filsename = spliturl[spliturl.length - 1]
      FileSaver.saveAs(url, filsename)
    },
    // 重置
    resetHandle() {
      this.showResult = false
      this.tabType = 1
      this.currentPage = 1
      this.mergesList = []
      this.tableSetting = []
      this.uploadFileName = []
      this.resultData = []
      this.importData = []
    },
    // 读取xlsx内容并初始化
    getHeaderRows(sheet) {
      const range = XLSX.utils.decode_range(sheet['!ref'])
      let xlsxData = []
      for (let R = range.s.r; R <= range.e.r; ++R) {
        xlsxData[R] = []
        for (let C = range.s.c; C <= range.e.c; ++C) {
          let cellAddress = { c: C, r: R };
          /* if an A1-style address is needed, encode the address */
          let cellRef = XLSX.utils.encode_cell(cellAddress);
          let dd = {
            cell_ddress: cellAddress,
            cell_ref: cellRef,
            data: sheet[cellRef] ? sheet[cellRef].w : null
          }
          xlsxData[R][C] = dd
        }
      }
      console.log(1111111122222, xlsxData)
      if (this.isDeleteFirst) {
        xlsxData.splice(1, 1)
      }
      let newXlsxData = []
      xlsxData.forEach((v, index) => {
        if (!this.deleteIndexs.includes(index)) {
          newXlsxData.push(v)
        }
      })
      this.xlsxData = deepClone(newXlsxData)
      let mergeObj = {}
      let headerSetting = [] // 表格头
      let merges = sheet['!merges'] ? sheet['!merges'] : []
      console.log(merges)
      if (merges.length > 0) { // 有合并表头的
        range.e.r = Math.min(range.e.r, this.headerLen) // 重新设下当前行哪个小拿哪个
        for (let R = range.s.r; R <= range.e.r; ++R) {
          mergeObj[R] = []
          merges.forEach(item => {
            if (item.e.r === R) {
              mergeObj[R].push(item)
            }
          })
        }
        for (let R = range.s.r; R <= range.e.r; ++R) {
          let last = range.e.r - R // 倒序进行赋值
          if (!headerSetting.length) {
            headerSetting = newXlsxData[last].map((v, i) => {
              return { label: v.data, key: 'item-' + i }
            })
            let setting = deepClone(headerSetting)
            mergeObj[last].forEach((k, ii) => {
              if (k.s.c === k.e.c) { // 处理合并列 col
                let start = k.s.c
                let end = k.e.c
                let len = end - start + 1
                setting.splice(k.s.c, len, { label: newXlsxData[k.s.r][k.s.c].data, key: 'item-' + k.s.c })
              }
            })
            headerSetting = deepClone(setting)
          } else {
            let setting = deepClone(headerSetting)
            let reduceLen = 0
            mergeObj[last].forEach((k, ii) => {
              if (k.s.r === k.e.r) { // 处理合并行 row
                let start = k.s.c - reduceLen
                let end = k.e.c - reduceLen
                let len = end - start + 1
                reduceLen += len - 1
                let mmm = setting.slice(start, end + 1)
                setting.splice(start, len, { label: newXlsxData[last][k.s.c].data, key: 'merge-' + k.s.c, children: mmm })
              } else if (k.s.c === k.e.c) { // 这种情况没考虑好，先不做
                // let start = k.s.c
                // let end = k.e.c
                // let len = end - start + 1
                // setting.splice(k.s.c, len, { label: newXlsxData[k.s.r][k.s.c].data, key: k.s.c })
              }
            })
            headerSetting = deepClone(setting)
          }
        }
        return {
          headerSetting: headerSetting,
          data: xlsxData.slice(this.headerLen + 1, newXlsxData.length)
        }
      } else { // 没有合并表头的
        headerSetting = newXlsxData.splice(0, 1)[0].map((item, index) => {
          let key = 'item-' + index
          return {
            label: item.data,
            key: key
          }
        })
        return {
          headerSetting: headerSetting,
          data: newXlsxData
        }
      }
    },
    // 设置table数据
    setTableData(result, type) {
      console.log("result", result);
      if (type) {
        console.log("type", type);
        this.updateTableData(result)
      } else {
        if (this.uploadStep === 'read') {
          this.tableSetting = result.headerSetting
          this.$nextTick(_ => {
            result.data.map((v, index) => {
              let obj = {}
              let isSet = false
              v.map((k, j) => {
                if (k.data !== null) {
                  isSet = true
                }
                obj['item-' + j] = k.data
              })
              if (isSet) {
                this.importData.push(obj)
              }
            })
          })
        } else {
          // result.headerSetting[0].key = 'result'
          // result.headerSetting.splice(this.resultIndex, 1) // 删除第一个结果的表头
          this.$nextTick(_ => {
            let resultIndex = 0
            for (let index = 0; index < result.headerSetting.length; index++) {
              const item = result.headerSetting[index];
              if (item.label === '结果') {
                resultIndex = index
                break
              }
            }
            result.headerSetting.splice(resultIndex, 1)
            this.tableSetting = result.headerSetting
            this.resultData = result.data.map(v => {
              let obj = {}
              let vlen = v.length
              v.map((k, j) => {
                if (resultIndex === -1) { // -1是特殊情况需要特别处理
                  if (j === (vlen - 1)) {
                    obj.result = k.data
                  } else {
                    obj['item-' + j] = k.data
                  }
                } else {
                  if (j === resultIndex) {
                    obj.result = k.data
                  } else {
                    obj['item-' + j] = k.data
                  }
                }
              })
              return obj
            })
            this.clickTabHandler(this.tabType)
          })
        }
      }
    },
    // 获取选择的excel的表数据
    async getXlsxData(workbook, name) {
      this.resetHandle()
      if (this.showImportResult) this.showImportBtn = true
      this.uploadStep = 'read'
      this.step = 2
      this.uploadFileName = name
      this.tableSetting = []
      this.importData = []
      // await this.$sleep(1000)
      const firstSheetName = workbook.SheetNames[0]
      const sheet = workbook.Sheets[firstSheetName]
      this.mergesList = sheet['!merges'] ? sheet['!merges'] : []
      let result = this.getHeaderRows(sheet)
      // console.log(333, XLSX.utils.sheet_to_html(sheet))
      console.log('result', result)
      this.initialXlsxData = Object.freeze(workbook)
      this.setHeaderOperation(result.headerSetting)
      this.tableSetting = result.headerSetting
      this.setTableData(result)
    },
    // 添加操作栏
    setHeaderOperation(data) {
      // 统一加
      if (this.showOperation) {
        data.push({
          label: '操作',
          key: 'operation',
          type: 'operation',
          align: 'center',
          fixed: 'right'
        })
      }
    },
    // 删除
    deleteHandle({ row, index }) {
      // 不能根据内容相同去删除对应的数据，有可能存在导入的数据中有多个数据是一模一样的，这样点击操作栏删除就不知道是要删除哪个了，需要计算对应的index
      // 计算当前数据在importData中的index
      const currentIndex = this.currentPage * this.pageSize - (this.pageSize - index)
      // 获取xlsxData中的index
      const xlsxIndex = currentIndex + this.headerLen + (this.isDeleteFirst ? 0 : 1)
      this.importData.splice(currentIndex, 1)
      this.xlsxData.splice(xlsxIndex, 1)
    },
    // 获取oss token
    async getUploadToken() {
      const res = await this.$apis.getUploadToken()
      if (res.code === 0) {
        this.uploadUrl = res.data.host
        this.prefix = res.data.prefix
        this.uploadParams = {
          key: '',
          policy: res.data.policy,
          OSSAccessKeyId: res.data.accessid,
          success_action_status: '200', // 让服务端返回200,不然，默认会返回204
          callback: res.data.callback,
          signature: res.data.signature
        }
      }
    },
    // 获取文件后缀名
    getSuffix(filename) {
      let pos = filename.lastIndexOf('.')
      let suffix = ''
      if (pos !== -1) {
        suffix = filename.substring(pos)
      }
      return suffix
    },
    aoaToXlsx(json, merges) {
      let wsname = 'Sheet1'
      let wb = XLSX.utils.book_new()
      let ws = XLSX.utils.aoa_to_sheet(json)
      XLSX.utils.book_append_sheet(wb, ws, wsname)
      if (merges) ws['!merges'] = merges
      let opts = {
        bookType: 'xlsx', // 要生成的文件类型
        bookSST: false,
        type: 'binary' // 二进制格式
      }
      let wbout = XLSX.write(wb, opts) // 生成xlsx格式的数据
      let xlsxblob = new Blob([this.s2ab(wbout)], {
        // 生成数据流格式
        type: 'application/octet-stream'
      })
      // 字符串转ArrayBuffer
      return xlsxblob
    },
    s2ab(s) {
      var buf = new ArrayBuffer(s.length)
      var view = new Uint8Array(buf)
      for (var i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
      return buf
    },
    // 生成blob数据
    createBlob(data, type) {
      return new Blob([data], {
        // 生成数据流格式
        type: type
      })
    },
    // 导入
    uploadStartHandle() {
      if (this.isLoading) return
      this.isLoading = true
      if (this.importData.length === 0) {
        this.isLoading = false
        return this.$message.error('暂无数据，请导入数据')
      }
      // let opts = {
      //   bookType: 'xlsx', // 要生成的文件类型
      //   bookSST: false,
      //   type: 'binary' // 二进制格式
      // }
      // let wbout = XLSX.write(this.initialXlsxData, opts) // 生成xlsx格式的数据
      // // console.log(this.initialXlsxData)
      // let xlsxblob = new Blob([this.s2ab(wbout)], {
      //   // 生成数据流格式
      //   type: 'application/octet-stream'
      // })
      let result = []
      this.xlsxData.forEach(item => {
        let obj = []
        let isSet = false
        item.forEach(v => {
          if (v.data !== null) {
            isSet = true
          }
          obj.push(v.data)
        })
        if (isSet) {
          result.push(obj)
        }
      })
      if (!this.isUpload) {
        this.isLoading = false
        return result
      }
      this.uploadXlsxblob(this.aoaToXlsx(result, this.mergesList))
    },
    async uploadXlsxblob(data) {
      // await this.getUploadToken() // 获取oss上传参数
      this.uploadParams.key =
        this.prefix +
        '-' +
        new Date().getTime() +
        '-' +
        Math.floor(Math.random() * 10) +
        this.getSuffix(this.uploadFileName)
      this.$apis.apiBackgroundFileUploadPost({
        ...this.uploadParams,
        file: data
      })
        .then(res => {
          if (res.code === 0) {
            if (!res.data.public_url) {
              this.$message.error('还没上传完毕或未上传')
              return
            }
            // 打开查询导入结果页面
            this.showImportBtn = false
            if (this.openExcelPage) {
              this.gotoExport(res.data.public_url)
            } else {
              this.getQueryid(res.data.public_url)
            }
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch(err => {
          this.isLoading = false
          this.$emit('update:loading', false)
          console.log(err)
        })
    },
    // 获取queryid
    async getQueryid(url) {
      let params = {}
      if (this.paramsData && typeof this.paramsData === 'object' && Object.keys(this.paramsData).length > 0) {
        params = this.paramsData
      }
      if (!url) return this.$message.error('缺少上传接口！')
      params[this.paramsKey] = url
      const [err, res] = await to(
        this.$apis[this.url](params)
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.queryId = res.data.query_id
        this.startQueryHandle(res.data.query_id)
      } else {
        this.$message.error(res.msg)
      }
    },
    startQueryHandle() {
      this.getExcelUrl(this.queryId)
      this.timer = setInterval(() => {
        this.getExcelUrl(this.queryId)
      }, 5000)
    },
    async getExcelUrl(queryId) {
      try {
        const res = await this.$apis.apiBackgroundBaseTasksExportQueryPost({
          query_id: queryId
        })
        if (res.code === 0) {
          if (res.data.status === 'success') {
            this.downLoadExcelUrl = res.data.url
            // window.location.href = res.url;
            if (this.showImportResult) {
              this.$message.success('成功，请查看导入结果！')
              this.getResultXlsx(this.downLoadExcelUrl)
            } else {
              this.downloadExcel(this.downLoadExcelUrl)
            }
            this.isLoading = false
            clearInterval(this.timer)
          } else if (res.data.status === 'failure') {
            clearInterval(this.timer)
            this.isLoading = false
            this.$message.error('查询导入结果失败！')
          }
        } else {
          this.$message.error(res.msg)
          clearInterval(this.timer)
        }
      } catch (error) {
        this.$message.error('出错啦')
        clearInterval(this.timer)
      }
    },
    // 获取导出结果excel
    getResultXlsx(url) {
      http
        .get(url, { responseType: 'blob' }) // arraybuffer, blob
        .then(async res => {
          var blob = new Blob([res], { type: 'application/octet-stream;charset=utf-8' })
          if (this.showImportBtn) this.showResult = true
          this.uploadStep = 'upload'
          await this.loadFile(blob)
        })
        .catch(err => {
          this.isLoading = false
          console.log(err)
        })
    },
    // 解析数据
    loadFile(obj) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = async e => {
          const data = e.target.result
          const workbook = XLSX.read(data, { type: 'array' })
          const firstSheetName = workbook.SheetNames[0]
          const results = XLSX.utils.sheet_to_json(workbook.Sheets[firstSheetName])
          this.mergesList = workbook.Sheets[firstSheetName]['!merges'] ? workbook.Sheets[firstSheetName]['!merges'] : []
          const result = this.getHeaderRows(workbook.Sheets[firstSheetName])
          this.initialXlsxData = Object.freeze(workbook)
          this.setTableData(result, this.isResultMulTitle)
          this.resultData = deepClone(results)
          resolve(results)
        }
        reader.readAsArrayBuffer(obj)
      })
    },
    // tab click
    clickTabHandler(type) {
      this.tabType = type
      this.showResult = true
      console.log("this.resultData", this.resultData);
      if (type === 1) {
        this.importData = this.resultData.filter(item => {
          return item && item.result && item.result.indexOf('成功') > -1
        })
      } else {
        this.importData = this.resultData.filter(item => {
          return item && item.result && item.result.indexOf('成功') < 0
        })
      }
    },
    // 导出excel
    exportHandler() {
      if (this.isUrlDownloadResult) {
        console.log("this.downLoadExcelUrl", this.downLoadExcelUrl);
        this.downloadResultXlsx(this.downLoadExcelUrl)
        return
      }
      let filename = this.tabType ? 'success_' : 'error_'
      filename += `${new Date().getTime()}.xlsx`

      let tableElt = this.$refs.tableRef.$el
      let workbook = XLSX.utils.table_to_book(tableElt);
      // eslint-disable-next-line dot-notation
      let ws = workbook.Sheets["Sheet1"];
      XLSX.utils.sheet_to_csv(ws);
      XLSX.writeFile(workbook, filename);
      // return
      // let result = this.importData.map(v => {
      //   let res = {}
      //   if (this.tabType === 0) {
      //     res['失败原因'] = v.result
      //   }
      //   this.tableSetting.forEach(item => {
      //     res[item.label] = v[item.key]
      //   })
      //   return res
      // })
      // FileSaver.saveAs(this.aoaToXlsx(result), filename)
    },
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
    },
    // 跳转导出结果页面
    gotoExport(publicUrl) {
      if (this.confirm) this.confirm(publicUrl)
      this.isLoading = false
      const option = {
        type: this.type,
        message: '下载导入结果?',
        immediate: true, // 立刻执行不弹窗
        url: this.url,
        params: {
          ...this.paramsData,
          url: publicUrl
        }
      }
      this.exportHandle(option)
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    // 更新结果数据表
    updateTableData(result) {
      this.$nextTick(_ => {
        let resultIndex = 0
        var headerSetting = result && Reflect.has(result, 'headerSetting') ? result.headerSetting : []
        for (let index = 0; index < headerSetting.length; index++) {
          const item = headerSetting[index];
          if (item.label === this.resultLabel) {
            resultIndex = index
            break
          }
        }
        console.log("resultIndex", resultIndex);
        headerSetting.splice(resultIndex, 1)
        this.tableSetting = headerSetting
        var data = result && Reflect.has(result, 'data') ? result.data : []
        console.log("this.resultExampleLine", this.resultExampleLine);
        if (this.resultExampleLine && data && data.length > this.resultExampleLine) {
          data.splice(0, this.resultExampleLine)
        }
        this.resultData = data.map(v => {
          let obj = {}
          let vlen = v.length
          v.map((k, j) => {
            if (j === (vlen - 1)) {
              obj.result = k.data
            } else {
              obj['item-' + j] = k.data
            }
          })
          return obj
        })
        this.clickTabHandler(this.tabType)
      })
    },
    //  下载结果excel 泽森说要用他的结果不是处理过后的结果
    downloadResultXlsx(url) {
      this.isLoading = true
      http
        .get(url, { responseType: 'blob' }) // arraybuffer, blob
        .then(async res => {
          var blob = new Blob([res], { type: 'application/octet-stream;charset=utf-8' })
          if (this.showImportBtn) this.showResult = true
          const downloadElement = document.createElement('a')
          const href = window.URL.createObjectURL(blob)
          downloadElement.href = href
          var fileName = '导入结果_' + `${new Date().getTime()}.xlsx`
          downloadElement.download = fileName // 文件名（自己随意设置）
          document.body.appendChild(downloadElement)
          downloadElement.click()
          document.body.removeChild(downloadElement) // 下载完成移除元素
          window.URL.revokeObjectURL(href) // 释放掉blob对象
          this.isLoading = false
        })
        .catch(err => {
          this.isLoading = false
          console.log(err)
        })
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  }
}
</script>

<style lang="scss">
.import-page {
  position: relative;
  // padding: 15px 0;
  box-shadow: 6px 6px 10px 0px rgb(202 210 221 / 30%), inset 2px 2px 0px 0px #ffffff;
  border-radius: 12px;
  background-color: #ffffff;
  .import-title {
    position: relative;
    padding: 10px 0;
    span {
      border-left: 4px solid #ff9b45;
      padding-left: 18px;
      font-size: 20px;
      color: #23282d;
    }
    &:after {
      content: '';
      position: absolute;
      left: 24px;
      right: 24px;
      bottom: 0;
      height: 1px;
      background-color: #e7ecf2;
    }
  }
  .import-content {
    padding: 10px 25px;
    .import-tools {
      .tools-tips {
        font-size: 16px;
        color: #fd4d4d;
      }
      .tools-wrapper {
        position: relative;
        margin-top: 15px;
        padding: 10px 0;
        &.tools-border{
          border: 1px solid #e0e6eb;
          border-radius: 6px;
        }
        .step-line {
          position: absolute;
          width: 100%;
          top: 27px;
          height: 1px;
          background-color: #e0e6eb;
          z-index: 0;
        }
        .step {
          display: inline-block;
          width: 50%;
          text-align: center;
          .step-icon {
            position: relative;
            text-align: center;
            .step-icon-box {
              display: inline-block;
              padding: 0 15px;
              background-color: #fff;
            }
            span {
              display: inline-block;
              width: 32px;
              height: 32px;
              line-height: 32px;
              text-align: center;
              border: solid 2px #fd953c;
              border-radius: 50%;
              color: #fd953c;
              &.active{
                color: #fff;
                background-color: #fd953c;
              }
            }
          }
          .step-tips {
            margin: 10px 0;
            font-size: 16px;
            color: #23282d;
          }
        }
      }
    }
  }
  .status-tools {
    padding: 0 25px;
    .status-tab {
      .tab {
        display: inline-block;
        width: 90px;
        margin-right: 15px;
        padding: 2px 0;
        text-align: center;
        font-size: 16px;
        color: #7b7c82;
        border-radius: 14px;
        border: solid 1px #dae1ea;
        cursor: pointer;
        &.active {
          color: #fff;
          background-color: #fd953c;
        }
      }
    }
  }
  .table-container {
    margin: 0 25px;
    padding-bottom: 25px;
  }
  .warn-text{
    color: red;
  }
  .footer{
    margin:0 25px;
    padding-bottom: 25px;
    text-align: center;
  }
}
</style>
