<template>
  <div>
    <el-dialog
      custom-class="ps-dialog-message ps-dialog"
      :title="title"
      :visible.sync="visible"
      :width="width"
      :top="top"
      :close-on-click-modal="false"
      :show-close="!isLoading"
      @closed="handleClose">
      <div class="content" v-loading="isLoading">
        <slot>
          <div style="color:red;text-align: left;">注意：导入的数据模板，需要按照<strong>平台提供的模板进行</strong>！导入前，请检查导入表格的格式和内容</div>
          <div style="border: 1px #DAE1EB solid;border-radius: 5px;margin:15px 0">
            <div class="line-num">
              <div class="line"></div>
              <div class="active-num num">1</div>
              <div class="line"></div>
              <div :class="['num',tableData.length?'active-num':'']">2</div>
              <div class="line"></div>
            </div>
            <div class="buttons">
              <div class="btn-item">
                <div class="text active-text">下载数据模板</div>
                <div><el-button size="mini" type="primary" class="ps-green-btn" icon="el-icon-download" @click="download">下载</el-button></div>
              </div>
              <div class="btn-item">
                <div class="text active-text">导入文件</div>
                <parse-excel titleName="选择本地文件" @excel="getXlsxData">选择本地文件</parse-excel>
              </div>
            </div>
          </div>
          <div class="table">
            <el-table
              ref="tableRef"
              :data="tableData"
              header-row-class-name="ps-table-header-row"
              >
              <template v-for="item in tableSetting">
                <el-table-column :key="item.key" :label="item.label" :prop="item.key" align="center">
                </el-table-column>
              </template>
              <el-table-column label="操作" width="60" align="center">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    class="delete-txt-btn"
                    @click="deleteImportData(scope.row)"
                  >移除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
            <el-pagination
              @size-change="setImportFromSize"
              @current-change="setImportFromCurrent"
              :current-page="importFrom.currentPage"
              :page-sizes="[5, 10, 15, 20]"
              :page-size="importFrom.pageSize"
              layout="total, prev, pager, next"
              :total="importFrom.totalCount"
              background
              class="ps-text"
              popper-class="ps-popper-select"
            ></el-pagination>
          </div>
        </slot>
      </div>
      <span slot="footer" v-if="showFooter" class="dialog-footer">
        <el-button :disabled="isLoading" class="ps-cancel-btn" @click="clickCancleHandle">{{cancelText}}</el-button>
        <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="clickConfirmHandle">{{confirmText}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import toUploadXlsx from "@/mixins/toUploadXlsx";
export default {
  name: 'importDialog',
  props: {
    importType: {
      type: String,
      default: 'excel'
    },
    show: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      default: '导入'
    },
    width: {
      type: String,
      default: '850px'
    },
    top: {
      type: String,
      default: '15vh'
    },
    loading: {
      type: Boolean,
      default: false
    },
    confirmText: {
      type: String,
      default: '确 定'
    },
    cancelText: {
      type: String,
      default: '取 消'
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    templateUrl: {
      type: String,
      default: ''
    },
    tableSetting: {
      type: Array,
      default: () => {}
    },
    openExcelType: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      time: new Date().getTime(),
      excelObj: '',
      tableData: [],
      importFrom: {
        pageSize: 5, // 每页数量
        totalCount: 0, // 总条数
        currentPage: 1, // 第几页
        allData: [],
        currentPageData: [],
        selectData: {} // 选择的数据用于分页显示
      }
    }
  },
  mixins: [toUploadXlsx],
  computed: {
    visible: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    },
    isLoading: {
      get() {
        return this.loading
      },
      set(val) {
        this.$emit('update:loading', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        this.tableData = []
        this.importFrom = {
          pageSize: 5, // 每页数量
          totalCount: 0, // 总条数
          currentPage: 1, // 第几页
          allData: [],
          currentPageData: [],
          selectData: {} // 选择的数据用于分页显示
        }
      }
    }
  },
  created () {
  },
  mounted () {
  },
  methods: {
    download() {
      window.open(this.templateUrl);
    },
    getXlsxData(json, name) {
      this.excelObj = {}
      this.tableSetting.forEach(item => {
        this.excelObj[item.label] = item.key
      })
      json.splice(0, 1);
      let keyobj = this.excelObj
      let result = json.map(item => {
        let data = {}
        for (let key in item) {
          if (keyobj[key]) {
            data[keyobj[key]] = item[key]
          }
        }
        return data
      })
      this.importFrom.allData = []
      result.map(item => {
        let flag = false
        for (let key in item) {
          if (!(item[key].toString().match(/^[ ]*$/))) {
            flag = true
            break
          }
        }
        if (flag) {
          this.importFrom.allData.push(item)
        }
      })
      this.setXlsxCurrentPageData()
    },
    setXlsxCurrentPageData() {
      // 根据导入的数据进行分页显示
      let start = (this.importFrom.currentPage - 1) * this.importFrom.pageSize
      let end = (this.importFrom.currentPage - 1) * this.importFrom.pageSize + this.importFrom.pageSize
      this.importFrom.totalCount = this.importFrom.allData.length
      this.importFrom.currentPageData = [].concat(this.importFrom.allData.slice(start, end));
      this.tableData = this.importFrom.currentPageData
    },
    // 检查当前页码是否满足分页
    checkedCurrentPage(data, opts) {
      if (opts.currentPage > 1) {
        let lit = data.length % opts.pageSize
        if (lit < 1) {
          opts.currentPage--;
        }
      }
    },
    setImportFromSize(e) {
      this.importFrom.pageSize = e;
      this.setXlsxCurrentPageData()
    },
    setImportFromCurrent(e) {
      this.importFrom.currentPage = e
      this.setXlsxCurrentPageData();
    },
    deleteImportData(row) {
      let data = this.importFrom.allData
      for (let i = 0; i < data.length; i++) {
        if (data[i].person_no === row.person_no) {
          this.importFrom.allData.splice(i, 1)
          break;
        }
      }
      // 重置分页数据
      this.checkedCurrentPage(this.importFrom.allData, this.importFrom)
      this.setXlsxCurrentPageData()
    },
    clickConfirmHandle() {
      if (this.isLoading) return;
      this.isLoading = true
      if (this.importType === 'excel') {
        if (this.importFrom.allData.length === 0) {
          return this.$message.error("暂无数据，请导入数据");
        }
        let dataList = this.importFrom.allData.map((v) => {
          let res = {};
          for (let key in this.excelObj) {
            res[key] = v[this.excelObj[key]]
          }
          return res;
        });
        console.log(dataList)
        this.visible = false
        this.jsonToXlsx(dataList);
      } else {
        this.$emit('confirm', this.importFrom)
      }
    },
    clickCancleHandle() {
      this.visible = false
      this.$emit('cancel')
      this.tableData = []
      this.importFrom.allData = []
    },
    handleClose(e) {
      this.isLoading = false
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss">
.ps-dialog-message{
  .content{
    .line-num{
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      .line{
        border-top: 1px #DAE1EB solid;
        width:150px;
      }
      .num{
        background-color: #DFE5EB;
        width: 23px;
        height: 23px;
        line-height: 23px;
        border-radius: 20px;
        color: #fff;
      }
      .active-num{
        background-color: #ff9b45;
      }
    }
    .buttons{
      display: flex;
      justify-content: space-between;
      padding: 0 173px 20px;
      .text{
        margin-bottom:5px;
        font-size: 16px;
      }
      .active-text{
        color: #ff9b45;
      }
      .btn-item{
        width: 130px;
      }
    }
  }
  .dialog-footer{
    width: 100%;
    text-align: right;
  }
}
</style>
