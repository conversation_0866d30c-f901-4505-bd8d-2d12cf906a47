<template>
  <div class="verificationCode">
    <el-input
      v-model="verificationCode"
      :placeholder="placeholder"
      clearable
      class="ps-input"
      :maxlength="6"
      style="width:160px;margin-right:8px;"
      @input="changeCode"
    >
    </el-input>
    <el-button :disabled="disabled" v-if="sendAuthCode" class="phone-code-btn" @click="getPhoneCode">{{ codeTipText }}</el-button>
    <el-button v-if="!sendAuthCode" class="ps-plain-btn" style="padding:12px 0;font-size:12px;width: 112px;">{{countDownText}}后重新获取</el-button>
  </div>
</template>

<script>
export default {
  name: 'verificationCode',
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    sendAuthCode: {
      type: Boolean,
      default: true
    },
    counNum: {
      type: Number,
      default: 60
    },
    placeholder: {
      type: String,
      default: '请输入验证码'
    },
    resetHandle: Function
  },
  data() {
    return {
      countDown: 0,
      countDownText: '',
      verificationCode: '',
      authTimetimer: null,
      codeTipText: '获取验证码'
    }
  },
  watch: {
    sendAuthCode: function(val) {
      if (!val) {
        this.countDownHandle()
      }
    }
  },
  created() {},
  mounted() {
  },
  methods: {
    async getPhoneCode() {
      this.$emit('click')
    },
    // countDown
    countDownHandle() {
      this.countDown = this.counNum;
      this.setCountDownText()
      this.authTimetimer = setInterval(() => {
        this.countDown--;
        this.setCountDownText()
        if (this.countDown <= 0) {
          this.codeTipText = '重新获取'
          this.resetHandle() // 重置下
          this.countDownText = ''
          if (this.authTimetimer) {
            clearInterval(this.authTimetimer);
          }
        }
      }, 1000);
    },
    // 设置倒计时的文字
    setCountDownText() {
      const ONE_MINUTE = 60
      let minute = Math.floor(this.countDown / ONE_MINUTE)
      let countText = ''
      if (minute > 1) { // 1 分钟就不要显示这个啦，显示60秒多好
        countText = `${minute}分`
        let second = Math.floor(this.countDown % ONE_MINUTE)
        if (second > 0) {
          countText += `${second}秒`
        } else {
          countText += `钟`
        }
        this.countDownText = countText
      } else {
        this.countDownText = `${this.countDown}秒`
      }
    },
    // 点击倒计时按钮
    changeCode() {
      this.$emit('input', this.verificationCode)
    }
  },
  beforeDestroy () { // 销毁前记得清空倒计时
    if (this.authTimetimer) {
      clearInterval(this.authTimetimer);
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.verificationCode {
  display: flex;
}
.phone-code-btn{
  width: 112px;
  &:hover{
    color: #ff9b45;
    border-color: #ff9b45;
    background-color: #fff;
  }
  &:focus{
    color: #ff9b45;
    border-color: #ff9b45;
    background-color: #fff;
  }
  &:active{
    color: #e58b3e;
    border-color: #e58b3e;
    background-color: #fff;
  }
  &.is-disabled, &.is-disabled:focus, &.is-disabled:hover {
    color: #C0C4CC;
    cursor: not-allowed;
    background-image: none;
    background-color: #FFF;
    border-color: #EBEEF5;
  }
}
</style>
