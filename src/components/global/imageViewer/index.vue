<template>
  <div v-if="preview" class="preview-list-box">
    <el-image-viewer
      v-if="showViewer"
      :url-list="previewSrcList"
      :initial-index="imageIndex"
      v-bind="$attrs"
      v-on="$listeners"
    />
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
let prevOverflow = '';

export default {
  inheritAttrs: false,
  components: {
    ElImageViewer
  },
  name: 'ImageViewer',
  props: {
    value: {
      // type: [String, Array],
      required: true
    },
    previewSrcList: {
      type: Array,
      default: () => []
    },
    src: {
      type: String,
      default: ''
    },
    // 以下属性使用透传的方式传入吧
    // urlList: {
    //   type: Array,
    //   default: () => []
    // },
    // zIndex: {
    //   type: Number,
    //   default: 2000
    // },
    // onSwitch: {
    //   type: Function,
    //   default: () => {}
    // },
    // onClose: {
    //   type: Function,
    //   default: () => {}
    // },
    initialIndex: {
      type: Number,
      default: 0
    },
    // appendToBody: {
    //   type: Boolean,
    //   default: true
    // },
    // maskClosable: {
    //   type: Boolean,
    //   default: true
    // }
  },

  data() {
    return {
    }
  },
  computed: {
    showViewer: {
      get() {
        if (this.value) {
          prevOverflow = document.body.style.overflow;
          document.body.style.overflow = 'hidden';
        } else {
          document.body.style.overflow = prevOverflow;
        }
        return this.value
      },
      set(val) {
        this.$emit('update:input', val)
      }
    },
    imageIndex() {
      let previewIndex = 0;
      const initialIndex = this.initialIndex;
      if (initialIndex >= 0) {
        previewIndex = initialIndex;
        return previewIndex;
      }
      const srcIndex = this.previewSrcList.indexOf(this.src);
      if (srcIndex >= 0) {
        previewIndex = srcIndex;
        return previewIndex;
      }
      return previewIndex;
    },
    preview() {
      const { previewSrcList } = this;
      return Array.isArray(previewSrcList) && previewSrcList.length > 0;
    },
  },
  watch: {

  },
  methods: {
  },
  mounted() {

  },
  destroyed() {

  }
};
</script>
