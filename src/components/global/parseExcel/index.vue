<script>
import XLSX from 'xlsx'
import { trim } from '@/utils'
export default {
  name: 'parseExcel',
  props: {
    type: {
      type: String,
      default: 'button'
    },
    name: {
      type: String,
      default: 'file'
    },
    data: Object,
    accept: {
      // 默认可选择的文件类型
      type: String,
      default: () => {
        return '.xlsx, .xls, .csv'
      }
    },
    // 目前没用到，留着先吧
    // onStart: Function,
    // onProgress: Function,
    // onError: Function,
    showloading: {
      type: Boolean,
      default: true
    },
    disabled: Boolean,
    btnType: {
      type: String,
      default: 'primary'
    },
    initial: { // 是否返回原始数据
      type: Boolean,
      default: false
    }
  },

  mounted() {
    this.fileName = ''
    // console.log(XLSX)
  },

  data() {
    return {
      fileName: '',
      isLoading: false
    }
  },
  destroyed() {
    window.removeEventListener("focus", this);
  },
  methods: {
    handleClick() {
      if (!this.disabled) {
        this.showloading && (this.isLoading = true)
        this.$refs.input.value = null
        this.$refs.input.click()
        var that = this
        // 加个focus 的监听，如果用户取消或者关闭就关闭loading
        window.addEventListener(
          'focus',
          () => {
            console.log("focus");
            // 取消逻辑处理
            that.isLoading = false
          },
          { once: true }
        )
      }
    },
    handleChange(ev) {
      const file = ev.target.files[0]
      if (!file) return
      // 设置下文件名
      this.fileName = file.name
      let fileType = this.getSuffix(file.name)
      if (!this.accept.split(', ').includes(fileType)) {
        this.isLoading = false
        return this.$message.error('请上传excel相关格式的文件！')
      }
      if (this.initial) {
        this.loadFile(file)
      } else {
        this.importData(file)
      }
    },
    // 获取文件后缀名
    getSuffix(filename) {
      let pos = filename.lastIndexOf('.')
      let suffix = ''
      if (pos !== -1) {
        suffix = filename.substring(pos)
      }
      return suffix
    },
    loadFile(obj) {
      /**
       * 合并单元格元素(decode_range方法解析数据格式)
       {
          s: { //s start 开始
            c: 1,//cols 开始列
            r: 0 //rows 开始行
          },
          e: {//e end  结束
            c: 4,//cols 结束列
            r: 0 //rows 结束行
          }
        }
      */
      const reader = new FileReader()
      reader.onload = async e => {
        const data = e.target.result
        const workbook = XLSX.read(data, { type: 'array' })
        console.log(workbook)
        const firstSheetName = workbook.SheetNames[0]
        if (this.initial) { // 直接返回workbook
          this.$emit('excel', workbook, this.fileName)
        } else { // 返回转json的格式，仅适用于无合并表格的
          // 以字符串形式读取 { raw: false }, 如果是金额的需要去除前后空格
          // trim
          let results = XLSX.utils.sheet_to_json(workbook.Sheets[firstSheetName], { raw: false })
          console.log('json', results)
          // 发现金额这些会多个空格结尾，现在需要去除首尾空格
          results = results.map(v => {
            Object.keys(v).forEach(key => {
              v[key] = trim(v[key], 2)
            })
            return v
          })
          this.$emit('excel', results, this.fileName)
        }
        this.isLoading = false
      }
      reader.readAsArrayBuffer(obj)
    },
    // 导入数据
    importData(obj) {
      console.log(obj);
      let that = this;
      var reader = new FileReader();
      var binary = "";
      var wb; // Read completed data
      var outdata;
      reader.onload = async function (e) {
        var bytes = new Uint8Array(e.currentTarget.result);
        var length = bytes.byteLength;
        for (var i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i]);
        }
        wb = XLSX.read(binary, {
          type: "binary"
        });
        console.log("wb", wb);
        outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]], { raw: false });
        // { raw: false } 需要去除首尾空格
        outdata = outdata.map(v => {
          Object.keys(v).forEach(key => {
            v[key] = trim(v[key], 2)
          })
          return v
        })
        console.log("outdata", outdata);
        that.$emit('excel', outdata, that.fileName)
        that.isLoading = false
      };
      reader.readAsArrayBuffer(obj);
    }
  },
  render(h) {
    let { handleClick, type, name, handleChange, accept, isLoading, disabled, btnType } = this
    const data = {
      class: {
        'parse-excel': true
      },
      on: {
        click: handleClick
      }
    }
    return (
      <div {...data}>
        {type === 'button' ? (
          <el-button disabled={disabled} loading={isLoading} size="mini" type={btnType}>
            {this.$slots.default}
          </el-button>
        ) : (
          this.$slots.default
        )}
        <input
          class="parse-excel_input"
          type="file"
          ref="input"
          name={name}
          on-change={handleChange}
          accept={accept}
        ></input>
      </div>
    )
  }
}
</script>

<style lang="scss">
.parse-excel {
  display: inline-block;
  text-align: center;
  cursor: pointer;
  outline: none;
  .parse-excel_input {
    display: none;
  }
}
</style>
