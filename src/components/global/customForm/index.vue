<template>
  <div class="custom-form">
    <el-form
      ref="customformRef"
      :model="formData"
      :rules="formRules"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <custom-form-item v-for="(item, index) in formOptions" :key="item.key + index" :col="item" :index="index" :value.sync="formData[item.key]" />
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'CustomForm',
  components: {
  },
  props: {
    // 表单配置项（配置项用数组还是对象呢？这里用数组吧）
    // 如果用对象的话可以直接用formOptions作为form的model，用数组的话还需要转一层
    formOptions: {
      type: Array,
      default: () => [
        // type 类型：
        // 1、常规类型 input、input-number、button、checkbox、radio、tree、text、select、switch、upload、date（参考element时间选择类型）、rate、cascader等等
        // 2、其它复杂类型，不做（没考虑好）
        //    2.1 columns (分组)使用el-row进行布局配置放到extra { gutter: 20, span:6 }, children, attr
        {
          label: 'test',
          key: '', // 必须
          type: 'input',
          prop: '',
          rules: '', // 自定义form-item校验使用的rule
          attr: {}, // 原有el-input支持的属性
          extra: {}, // 其它额外的设置，作为form-item的配置项
          class: '',
          dataList: [
            { label: '', value: '', disabled: false }
          ],
          children: []
        }
      ]
    },
    // 表单数据，无怎更加formOptions的key字段生成
    formData: {
      type: Object,
      default: () => {}
    },
    // 表单校验规则
    formRules: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  computed: {
    editFormData: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('update:input', val)
      }
    }
  },
  watch: {
  },
  created() {

  },
  mounted() {

  },
  methods: {

  }
};
</script>

<style scoped lang="scss">

</style>
