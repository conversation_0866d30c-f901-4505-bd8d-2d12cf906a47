<template>
  <el-form-item
    :label="col.label"
    :prop="col.prop"
    :label-width="col.labelWidth"
    :disabled="col.disabled"
    :show-message="col.showMessage"
  >
    <!-- 自定义插槽 -->
    <slot
      v-if="col.type === 'slot'"
      :name="col.slotName"
      :col="col"
      :index="scope.$index"
      :column="scope.column"
    ></slot>
    <components v-else :row="scope.row" :prop="col.key" :col="col" :is="`form-${col.type}`"></components>
  </el-form-item>
</template>

<script>
// 动态引入components中的组件
// const modules = {}
// function capitalizeFirstLetter(str) {
//   return str.charAt(0).toUpperCase() + str.slice(1)
// }
// function validateFileName(str) {
//   return /^\S+\.vue$/.test(str) && str.replace(/^\S+\/(\w+)\.vue$/, (rs, $1) => capitalizeFirstLetter($1))
// }
// const files = require.context('./components', true, /\.vue$/)
// files.keys().forEach(filePath => {
//   const componentConfig = files(filePath)
//   const fileName = validateFileName(filePath)
//   const componentName =
//     fileName.toLowerCase() === 'index' ? capitalizeFirstLetter(componentConfig.default.name) : fileName
//   modules[`Form${componentName}`] = files(filePath).default || componentName
// })
// console.log(modules, 'modules')

export default {
  name: 'CustomFormItem',
  components: {
    // 组件动态注册
    // ...modules
  },
  props: {
    value: {
      // type: [String, Array],
      required: true
    },
    // 表单配置项
    col: {
      type: Object
    },
    // 表单数据
    formData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  computed: {
    itemValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('update:input', val)
      }
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style scoped lang="scss"></style>
