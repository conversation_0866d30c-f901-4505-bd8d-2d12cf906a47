<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    :width="width"
    :top="top"
    class="importFilesDialog"
    @close="handlerClose"
    @cancel="clickCancleHandle"
    @confirm="clickConfirmHandle"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-form :model="formData" :rules="formRule" class="import-face-form-wrapper" label-width="80px" size="small">
      <el-form-item label="导入模板">
        <el-link type="primary" :href="templateUrl">点击下载</el-link>
      </el-form-item>
      <el-form-item label="选择文件" style="margin-bottom: 0;">
        <slot name="upload" prop="fileLists">
          <file-upload
            drag
            ref="fileUploadRef"
            :fileList="fileLists"
            :list-type="listType"
            :prefix="prefix"
            :show-file-list="showFileList"
            :accept="accept"
            :rename="rename"
            :multiple="multiple"
            :limit="limit"
            :before-upload="beforeUploadHandle"
            @fileLists="getFileLists"
          >
            <template v-slot="scope">
              <!-- {{ scope }} -->
              <!-- <el-button :loading="scope.loading" class="ps-origin" size="small" type="text">
                上传{{ scope.loading ? '中' : '文件' }}
              </el-button> -->
              <div :loading="scope.loading" class="">
                <i class="el-icon-upload"></i>
                <div v-if="!scope.loading" class="el-upload__text">
                  将文件拖到此处，或
                  <em>点击上传</em>
                </div>
                <div v-else class="el-upload__text">
                  上传文件中...
                </div>
              </div>
            </template>
          </file-upload>
        </slot>
      </el-form-item>
      <el-form-item label="">
        <slot name="tip">
          <div class="el-upload__tip">只能上传{{accept}}文件</div>
        </slot>
      </el-form-item>
    </el-form>
  </dialog-message>
</template>

<script>
import { getSuffix, getUrlFilename, trim } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import FileSaver from 'file-saver'

export default {
  name: 'importFiles',
  mixins: [exportExcel],
  props: {
    importType: {
      type: String,
      default: 'excel'
    },
    show: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      default: '导入'
    },
    width: {
      type: String,
      default: '500px'
    },
    top: {
      type: String,
      default: '15vh'
    },
    loading: {
      type: Boolean,
      default: false
    },
    templateUrl: {
      type: String,
      default: ''
    },
    api: {
      type: String,
      default: ''
    },
    prefix: {
      type: String,
      default: 'import-file'
    },
    listType: {
      type: String,
      default: 'text'
    },
    accept: {
      type: String,
      default: '.xls,.xlsx'
    },
    limit: {
      type: Number,
      default: 1
    },
    showFileList: {
      type: Boolean,
      default: true
    },
    fileLists: {
      type: Array,
      default() {
        return []
      }
    },
    rename: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default() {
        return {}
      }
    },
    // 传给接口的key
    paramsKey: {
      type: String,
      default: 'url'
    },
    // 是否打开查询导入结果界面
    openExcelPage: {
      type: Boolean,
      default: true
    },
    beforeUpload: Function
  },
  data() {
    return {
      time: new Date().getTime(),
      formData: {
        fileLists: []
      },
      formRule: {
        fileLists: [{ required: true, message: '请先上传文件', trigger: 'change' }]
      },
      timer: null, // 定时器
      queryId: '', // queryid
      downLoadExcelUrl: '' // 下载的链接
    }
  },
  computed: {
    visible: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    },
    isLoading: {
      get() {
        return this.loading
      },
      set(val) {
        this.$emit('update:loading', val)
      }
    }
  },
  watch: {
    // show(val) {
    //   if (val) {
    //     this.formData.fileLists = []
    //     this.$refs.fileUploadRef && this.$refs.fileUploadRef.clearHandle()
    //   }
    // }
  },
  created () {
  },
  mounted () {
  },
  methods: {
    download() {
      window.open(this.templateUrl);
    },
    // 文件上传前的判断事件
    beforeUploadHandle(file) {
      if (this.beforeUpload) {
        return this.beforeUpload(file)
      }
      let uploadType = trim(this.accept, 1).split(',')
      if (!uploadType.includes(getSuffix(file.name))) {
        this.$message.error('请检查上传文件格式！')
        return false
      }
      const isLt2M = file.size / 1024 / 1024 <= 2
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 2M')
      }
      return isLt2M
    },
    getFileLists(e) {
      this.formData.fileLists = e
    },
    clickConfirmHandle() {
      let fileList = this.formData.fileLists.map(v => v.url)
      if (this.api) {
        this.sendFileHandle(this.multiple ? fileList : fileList[0])
      }
      this.$emit('confirmUpload', fileList)
    },
    clickCancleHandle() {
      this.visible = false
    },
    handlerClose(e) {
      this.formData.fileLists = []
      this.$refs.fileUploadRef && this.$refs.fileUploadRef.clearHandle()
    },
    sendFileHandle(url) {
      if (this.openExcelPage) {
        this.gotoExport(url)
      } else {
        this.getQueryid(url)
      }
    },
    gotoExport(publicUrl) {
      this.isLoading = false
      const option = {
        type: this.type,
        message: '下载导入结果?',
        immediate: true, // 立刻执行不弹窗
        url: this.api,
        params: {
          ...this.params,
          url: publicUrl
        }
      }
      this.exportHandle(option)
      this.visible = false
    },
    // 获取queryid
    async getQueryid(url) {
      let params = {}
      if (this.params && typeof this.params === 'object' && Object.keys(this.params).length > 0) {
        params = this.params
      }
      if (!url) return this.$message.error('缺少上传参数！')
      params[this.paramsKey] = url
      const [err, res] = await this.$to(
        this.$apis[this.api](params)
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.queryId = res.data.query_id
        this.startQueryHandle(res.data.query_id)
      } else {
        this.$message.error(res.msg)
      }
    },
    startQueryHandle() {
      this.getExcelUrl(this.queryId)
      this.timer = setInterval(() => {
        this.getExcelUrl(this.queryId)
      }, 5000)
    },
    async getExcelUrl(queryId) {
      try {
        const res = await this.$apis.apiBackgroundBaseTasksExportQueryPost({
          query_id: queryId
        })
        if (res.code === 0) {
          if (res.data.status === 'success') {
            this.downLoadExcelUrl = res.data.url
            this.downloadExcel(this.downLoadExcelUrl)
            this.isLoading = false
            clearInterval(this.timer)
          } else if (res.data.status === 'failure') {
            clearInterval(this.timer)
            this.isLoading = false
            this.$message.error('查询导入结果失败！')
          }
        } else {
          this.$message.error(res.msg)
          clearInterval(this.timer)
        }
      } catch (error) {
        this.$message.error('出错啦')
        clearInterval(this.timer)
      }
    },
    // 下载模板
    downloadExcel(url) {
      // window.open(this.templateUrl);
      let filsename = getUrlFilename(url)
      FileSaver.saveAs(url, filsename)
      this.visible = false
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  }
}
</script>

<style lang="scss">
.importFilesDialog{
  .el-upload-dragger {
    width: 300px;
    height: 130px;
    .el-icon-upload {
      margin: 18px 16px;
    }
  }
}
</style>
