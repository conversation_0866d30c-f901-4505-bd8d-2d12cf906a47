<template>
  <div class="buttonIcon">
    <!-- 多个 -->
    <div v-if="buttonData.length" class="buttonData">
      <div v-for="(item, index) in buttonData" :key="index" class="buttonItem">
        <el-button
          v-permission="item.permission?item.permission:[]"
          :type="item.color === 'plain' ? '' : 'primary'"
          size="mini"
          :class="[item.color ? 'ps-' + item.color + '-btn' : '']"
          @click="buttonsClick(item.click)"
        >
          <i v-if="item.type" class="btn-icon">
            <img class="icon-img" :src="require('@/assets/img/' + item.type + '-' + (item.color === 'plain' ? 'black' : 'white') + '.png')" alt="" />
          </i>
          {{ item.name }}
        </el-button>
      </div>
    </div>
    <!-- 单个 -->
    <div v-else style="display:inline-block; margin: 5px;">
      <el-button
        v-permission="permission?permission:[]"
        v-show="show"
        :type="color === 'plain' ? '' : 'primary'"
        size="mini"
        :disabled="disabled"
        :class="[color ? 'ps-' + color + '-btn' : '']"
        @click="btnClick()"
      >
        <i v-if="type" class="btn-icon">
          <img class="icon-img" :src="require('@/assets/img/' + type + '-' + (color === 'plain' ? 'black' : 'white') + '.png')" alt="" />
        </i>
        <slot> {{ name }} </slot>
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'buttonIcon',
  props: {
    buttonData: {
      type: Array,
      default: () => []
    },
    permission: {
      type: Array,
      default: () => []
    },
    show: {
      type: Boolean,
      default: true
    },
    color: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  created() {},
  mounted() {},
  methods: {
    buttonsClick(click) {
      this.$emit(click)
    },
    btnClick() {
      this.$emit('click')
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.buttonIcon {
  display:inline;
  .buttonData {
    display: flex;
    justify-content: flex-end;
    .buttonItem{
      margin-left: 10px;
    }
  }
  .btn-icon{
    display: inline-block;
    position: relative;
    width: 16px;
    vertical-align: middle;
    .icon-img{
      position: absolute;
      display: inline-block;
      left: 0;
      top: -9px;
    }
  }
}
</style>
