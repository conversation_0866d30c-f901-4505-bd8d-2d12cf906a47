<template>
  <div class="lazy_selectwrapper">
    <el-select
      :value="selectValue"
      class="lazy_select ps-select"
      popper-class="ps-popper-select"
      :filterable="filterable"
      v-bind="$attrs"
      v-on="$listeners"
      :filter-method="filterMethod"
      :loading="remoteLoading"
      @visible-change="visibleChange"
    >
      <div v-infinite-scroll="loadScroll" :infinite-scroll-disabled="isLoading">
        <el-option
          v-for="item in selectList"
          :key="item[extraOpttions.value]"
          :label="item[extraOpttions.label]"
          :value="item[extraOpttions.value]"
        ></el-option>
        <p v-if="isLoading" class="lazy_select_p">
          加载中
          <i class="el-icon-loading"></i>
        </p>
      </div>
    </el-select>
  </div>
</template>

<script>
import { debounce, to, uniqueArrKey, deepClone } from '@/utils'

export default {
  name: 'LazySelect',
  props: {
    // value
    value: {
      // type: [String, Array],
      required: true
    },
    // 接口参数
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 额外参数
    extraOpttions: {
      type: Object,
      default: () => {
        return {
          label: 'name',
          value: 'id'
        }
      }
    },
    // 是否开启搜索
    filterable: {
      type: Boolean,
      default: true
    },
    // 搜索的key，如果为空则使用本地搜索
    filterKey: {
      type: String,
      default: 'name'
    },
    // 是否懒加载
    isLazy: {
      type: Boolean,
      default: true
    },
    // 接口
    apiUrl: {
      type: String,
      default: ''
    },
    // 返回数据拿取的字段
    resultKey: {
      type: String,
      default: 'results'
    }
  },
  data() {
    return {
      isLoading: false, // 加载状态
      remoteLoading: false, // 远程加载状态
      disabledScroll: false, // 是否禁止滚动
      hasmore: true, // 是否有更多数据
      pageSize: 20,
      currentPage: 1,
      totalCount: 0, // 总条数
      selectList: [], // select数据
      oldSelectList: [], // select的备份数据
      filterList: [], // 筛选的数据
      useFilter: false // 是否搜索过了， 搜索过的话需要对原数据去重处理
    }
  },
  computed: {
    selectValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('update:input', val)
      }
    }
  },
  watch: {
    // selectval: function(val, old){
    //   console.log(val)
    // }
  },
  created() {
    this.remoteLoading = true
    this.loadHandle()
  },
  mounted() {
  },
  methods: {
    // 加载数据
    async loadHandle(name) {
      if (this.isLoading || !this.apiUrl) return
      this.isLoading = true
      let params = {}
      if (name) { // filter
        params = {
          ...this.params,
          page: 1,
          page_size: 999999
        }
        params[this.filterKey] = name
        // this.oldSelectList = deepClone(this.selectList)
        this.remoteLoading = true
      } else { // no filter
        params = {
          ...this.params,
          page: this.currentPage,
          page_size: this.isLazy ? this.pageSize : 999999 // 非isLazy状态直接加载全部
        }
      }
      const [err, res] = await to(this.$apis[this.apiUrl](params))
      this.isLoading = false
      this.remoteLoading = false
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const result = this.resultKey ? res.data[this.resultKey] : res.data
        if (name) {
          this.filterList = result
          this.selectList = result
        } else {
          this.totalCount = res.data.count
          if (this.selectList.length > 0) {
            if (this.useFilter) { // 搜索过的都执行下去重
              this.selectList = uniqueArrKey(this.selectList.concat(result), 'id')
            } else {
              this.selectList = this.selectList.concat(result)
            }
          } else {
            this.selectList = result
          }
        }
        // 当获取的数据条数和总条数相等时表示已加载所有数据，滚动加载可以禁止了
        if (result.length === res.data.count) {
          this.hasmore = false
        } else {
          this.currentPage++
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 加载更多load事件
    loadScroll() {
      // 搜索时会重置hasmore为true，所以非搜索的数据不能根据hasmore来判断
      if (this.filterValue) {
        if (this.hasmore && this.isLazy) {
          this.loadHandle()
        }
      } else {
        if (this.selectList.length < this.totalCount && this.isLazy) {
          this.loadHandle()
        }
      }
    },
    // 筛选
    filterMethod(val) {
      this.filterValue = val
      this.useFilter = true
      this.loadRemoteHandle()
    },
    // 节下流
    loadRemoteHandle: debounce(function() {
      if (this.filterValue) {
        this.hasmore = true
        if (this.oldSelectList.length) {
          this.oldSelectList = deepClone(uniqueArrKey(this.oldSelectList.concat(this.selectList), this.extraOpttions.value))
        } else {
          this.oldSelectList = deepClone(this.selectList)
        }
        // filter字段
        if (this.filterKey) {
          if (!this.isLazy || this.oldSelectList.length === this.totalCount) {
            this.filterLoadHandle()
          } else {
            this.loadHandle(this.filterValue)
          }
        } else {
          this.filterLoadHandle()
        }
      } else {
        this.resetPervList()
      }
    }, 160),
    // 下拉框出现/隐藏
    visibleChange(e) {
      if (!e) {
        this.resetPervList()
      }
    },
    // 还原上次列表数据，给个loading
    async resetPervList() {
      // 加个状态显示，体验更好
      this.remoteLoading = true
      await this.$sleep(300)
      if (this.useFilter) {
        if (this.selectList.length < this.totalCount) {
          this.hasmore = true
        }
        this.selectList = uniqueArrKey(this.oldSelectList.concat(this.filterList), this.extraOpttions.value)
        this.filterList = []
        this.filterValue = ''
        // this.oldSelectList = []
      }
      this.remoteLoading = false
    },
    // 本地数据filter，给个loading
    async filterLoadHandle() {
      // 加个状态显示，体验更好
      this.remoteLoading = true
      await this.$sleep(300)
      this.hasmore = false
      this.selectList = this.oldSelectList.filter(item => {
        return item[this.extraOpttions.label].indexOf(this.filterValue) > -1
      })
      this.remoteLoading = false
    }
  }
}
</script>

<style lang="scss">
.lazy_selectwrapper {
  .el-select {
    display: block;
  }
  .lazy_select {
    // width: 180px;
  }
}
.lazy_select_p {
  text-align: center;
  color: #ff9b45;
  font-size: 14px;
  line-height: 34px;
}
</style>
