<template>
  <div calss="ps-dialog-m">
    <el-drawer
      append-to-body
      modal-append-to-body
      custom-class="ps-import-dialog-message"
      :title="title"
      :visible.sync="visible"
      :size="size"
      :wrapperClosable="closeModal"
      :show-close="!isLoading"
      :direction="direction"
      @closed="handleClose">
      <div class="content" v-loading="isLoading">
        <slot>
          <!--下载模板-->
          <div class="ps-flex m-t-30 m-b-40">
            <div class="active-num num">1</div>
            <div class="text w-200 text-left m-l-10">导入请先下载数据模板</div>
            <div><el-button size="mini" type="primary" class="ps-green-btn m-l-40 w-80"  @click="download">下载模板</el-button></div>
          </div>
          <!--提示-->
          <div v-if="isShowCustomWarn" style="color:red;text-align: left;">注意：导入的数据模板，需要按照<strong>平台提供的模板进行</strong>！导入前，请检查导入表格的格式和内容</div>
          <div v-else style="color:red;text-align: left;" >{{ warnTip  }}</div>
          <!--上传文件-->
          <div class="ps-flex m-t-40 m-b-40">
            <div :class="['num',tableData.length?'active-num':'']">2</div>
            <div class="text w-200 text-left m-l-10">上传导入文件</div>
            <div class="m-l-40"><parse-excel titleName="选择文件" @excel="getXlsxData">选择文件</parse-excel></div>
          </div>
          <div class="table">
            <!-- 在table 上方插入内容 -->
          <slot name="content"></slot>
            <el-table
              ref="tableRef"
              :data="tableData"
              header-row-class-name="ps-table-header-row"
              >
                <el-table-column :label="item.label" :prop="item.key" align="center" v-for="item in tableSetting" :key="item.key">
                </el-table-column>
              <el-table-column label="操作" width="60" align="center">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    class="delete-txt-btn"
                    @click="deleteImportData(scope.row)"
                  >移除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
            <el-pagination
              @size-change="setImportFromSize"
              @current-change="setImportFromCurrent"
              :current-page="importFrom.currentPage"
              :page-sizes="[8, 16]"
              :page-size="importFrom.pageSize"
              layout="total, prev, pager, next"
              :total="importFrom.totalCount"
              background
              class="ps-text"
              popper-class="ps-popper-select"
            ></el-pagination>
          </div>
        </slot>
      </div>
      <span v-if="showFooter" class="dialog-footer-drawer">
        <el-button :disabled="isLoading" class="ps-cancel-btn w-100" @click="clickCancleHandle">{{cancelText}}</el-button>
        <el-button :disabled="isLoading" class="ps-btn m-l-40 w-100" type="primary" @click="clickConfirmHandle">{{confirmText}}</el-button>
      </span>
    </el-drawer>
  </div>
</template>

<script>
import toUploadXlsx from "@/mixins/toUploadXlsx";
export default {
  name: 'importDialogDrawer',
  props: {
    importType: {
      type: String,
      default: 'excel'
    },
    show: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      default: '导入'
    },
    width: {
      type: String,
      default: '50%'
    },
    loading: {
      type: Boolean,
      default: false
    },
    confirmText: {
      type: String,
      default: '确认并导入'
    },
    cancelText: {
      type: String,
      default: '取 消'
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    templateUrl: {
      type: String,
      default: ''
    },
    tableSetting: {
      type: Array,
      default: () => {}
    },
    openExcelType: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default() {
        return {}
      }
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    closeModal: { // 点击遮罩层关闭drawer
      type: Boolean,
      default: false
    },
    warnTip: { // 自定义的提示语言
      type: String,
      default: ''
    },
    isShowCustomWarn: { // 是否显示自定义提示 默认展示，别影响其他项目
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      time: new Date().getTime(),
      excelObj: '',
      tableData: [],
      importFrom: {
        pageSize: 8, // 每页数量
        totalCount: 0, // 总条数
        currentPage: 1, // 第几页
        allData: [],
        currentPageData: [],
        selectData: {} // 选择的数据用于分页显示
      },
      size: this.width,
      importTypeRef: '', // 通过ref传进来 实例：资产信息-上传 需要插入内容，不影响原有功能
      paramsRef: {} // 通过ref传进来 实例：资产信息-上传 需要插入内容，不影响原有功能
    }
  },
  mixins: [toUploadXlsx],
  computed: {
    visible: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    },
    isLoading: {
      get() {
        return this.loading
      },
      set(val) {
        this.$emit('update:loading', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        this.tableData = []
        this.importFrom = {
          pageSize: 8, // 每页数量
          totalCount: 0, // 总条数
          currentPage: 1, // 第几页
          allData: [],
          currentPageData: [],
          selectData: {} // 选择的数据用于分页显示
        }
        console.log("visible", this.tableSetting.length);
        // this.size = this.tableSetting && this.tableSetting.length > 4 && this.tableSetting.length < 15 ? 120 * this.tableSetting.length + 'px' : this.width
        this.size = this.width // 要50% 黄健和-建议
      }
    }
  },
  created () {
  },
  mounted () {
  },
  methods: {
    download() {
      window.open(this.templateUrl);
    },
    getXlsxData(json, name) {
      this.excelObj = {}
      this.tableSetting.forEach(item => {
        this.excelObj[item.label] = item.key
      })
      json.splice(0, 1);
      let keyobj = this.excelObj
      let result = json.map(item => {
        let data = {}
        for (let key in item) {
          if (keyobj[key]) {
            data[keyobj[key]] = item[key]
          }
        }
        return data
      })
      this.importFrom.allData = []
      result.map(item => {
        let flag = false
        for (let key in item) {
          if (!(item[key].toString().match(/^[ ]*$/))) {
            flag = true
            break
          }
        }
        if (flag) {
          this.importFrom.allData.push(item)
        }
      })
      this.setXlsxCurrentPageData()
    },
    setXlsxCurrentPageData() {
      // 根据导入的数据进行分页显示
      let start = (this.importFrom.currentPage - 1) * this.importFrom.pageSize
      let end = (this.importFrom.currentPage - 1) * this.importFrom.pageSize + this.importFrom.pageSize
      this.importFrom.totalCount = this.importFrom.allData.length
      this.importFrom.currentPageData = [].concat(this.importFrom.allData.slice(start, end));
      this.tableData = this.importFrom.currentPageData
    },
    // 检查当前页码是否满足分页
    checkedCurrentPage(data, opts) {
      if (opts.currentPage > 1) {
        let lit = data.length % opts.pageSize
        if (lit < 1) {
          opts.currentPage--;
        }
      }
    },
    setImportFromSize(e) {
      this.importFrom.pageSize = e;
      this.setXlsxCurrentPageData()
    },
    setImportFromCurrent(e) {
      this.importFrom.currentPage = e
      this.setXlsxCurrentPageData();
    },
    deleteImportData(row) {
      let data = this.importFrom.allData
      for (let i = 0; i < data.length; i++) {
        if (data[i].person_no === row.person_no) {
          this.importFrom.allData.splice(i, 1)
          break;
        }
      }
      // 重置分页数据
      this.checkedCurrentPage(this.importFrom.allData, this.importFrom)
      this.setXlsxCurrentPageData()
    },
    clickConfirmHandle() {
      if (this.isLoading) return;
      this.isLoading = true
      // importTypeRef 通过ref 传
      if (this.importType === 'excel' || this.importTypeRef) {
        if (this.importFrom.allData.length === 0) {
          return this.$message.error("暂无数据，请导入数据");
        }
        let dataList = this.importFrom.allData.map((v) => {
          let res = {};
          for (let key in this.excelObj) {
            res[key] = v[this.excelObj[key]]
          }
          return res;
        });
        console.log(dataList)
        this.visible = false
        this.jsonToXlsx(dataList);
      } else {
        console.log(this.importFrom)
        this.$emit('confirm', this.importFrom)
      }
    },
    clickCancleHandle() {
      this.visible = false
      this.$emit('cancel')
      this.tableData = []
      this.importFrom.allData = []
    },
    handleClose(e) {
      this.isLoading = false
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
 ::v-deep .el-drawer__header {
    margin-bottom: 0;
    padding: 23px 20px;
    background: #e7e9ef !important;
  }
.ps-import-dialog-message{

  .content{
    padding: 0 20px;
      .line{
        border-top: 1px #DAE1EB solid;
        width:150px;
      }
      .num{
        background-color: #DFE5EB;
        width: 23px;
        height: 23px;
        line-height: 23px;
        border-radius: 20px;
        color: #fff;
        text-align: center;
      }
      .active-num{
        background-color: #ff9b45;
      }
    .buttons{
      display: flex;
      justify-content: space-between;
      padding: 0 173px 20px;
      .text{
        margin-bottom:5px;
        font-size: 16px;
      }
      .active-text{
        color: #ff9b45;
      }
      .btn-item{
        width: 130px;
      }
    }
  }
  .dialog-footer-drawer{
    width: 100%;
    text-align: right;
    margin-left: 20px;
  }
  .m-l-40 {
    margin-left: 40px;
  }
  .m-t-20 {
    margin-top: 20px;
  }
  .m-t-40 {
    margin-top: 40px;
  }
  .m-b-20 {
    margin-bottom: 20px;
  }
  .m-b-40 {
    margin-bottom: 40px;
  }
  .w-80 {
    width: 80px;
  }
  .w-100 {
    width: 100px;
  }
  .w-200 {
    width: 200px;
  }
  .w-300 {
    width: 300px;
  }
  .text-left {
    text-align: left;
  }
}
</style>
