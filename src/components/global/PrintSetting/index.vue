<template>
  <customDrawer
    title="设置"
    :show.sync="dialogVisible"
    custom-class="print-setting"
    :size="width"
    @confirm="submitDialogHandle"
  >
    <div class="drawer-container">
      <div class="table-type">
        <div :class="['table-type-btn', tableType === 'list' ? 'active-btn' : '']" @click="changeTableType('list')">
          列表设置
        </div>
        <div :class="['table-type-btn', tableType === 'print' ? 'active-btn' : '']" @click="changeTableType('print')">
          打印设置
        </div>
      </div>
      <div class="drawer-content">
        <div v-if="tableType === 'list'" class="ps-tree-checkbox">
          <el-tree
            :data="settingData"
            show-checkbox
            default-expand-all
            node-key="key"
            ref="printTree"
            highlight-current
            :check-on-click-node="true"
            :default-checked-keys="tableCheckedSetting"
            @check="checkHandle"
            :props="propsOption"
          ></el-tree>
        </div>
        <div class="drawer-print" v-if="tableType === 'print'">
          <el-form
            :model="drawerFormData"
            @submit.native.prevent
            status-icon
            ref="drawerFormDataRef"
            :rules="drawerFormDataRuls"
            label-width="80px"
          >
            <div class="line-wrapp">
              <span class="line-title p-r-20">签证拦配置</span>
              <span class="async-text">签字栏上限是五条数据。</span>
              <el-button type="text" :disabled="drawerFormData.signatureList.length >= 5" @click="clickAddSignature">
                添加
              </el-button>
            </div>
            <div class="p-t-20">
              <el-form-item
                :label="`签字${index + 1}:`"
                v-for="(item, index) in drawerFormData.signatureList"
                :key="index"
                :prop="'signatureList[' + index + '].value'"
              >
                <!-- :rules="drawerFormData.signature" -->
                <el-input
                  class="ps-input"
                  v-model="item.value"
                  placeholder="请输入内容"
                  maxlength="10"
                  style="width: 300px"
                ></el-input>
                <i
                  @click="clickAddSignature"
                  v-if="drawerFormData.signatureList.length && drawerFormData.signatureList.length < 5"
                  class="add-icon el-icon-circle-plus"
                ></i>
                <i
                  @click="deleteSignature(index)"
                  v-if="drawerFormData.signatureList.length"
                  class="remove-icon el-icon-remove"
                ></i>
              </el-form-item>
            </div>
            <div class="p-t-10 line-wrapp">
              <span class="line-title p-r-20">同步设置</span>
              <span class="async-text">同步信息仅同步打印设置，如不勾选则仅修改当前功能表</span>
            </div>
            <div class="p-t-20">
              <el-form-item label="同步操作:">
                <el-select
                  v-model="drawerFormData.exportSignNameData"
                  class="ps-select"
                  style="width: 300px"
                  popper-class="ps-popper-select"
                  placeholder="请选择同步的功能表"
                  multiple
                  collapse-tags
                  filterable
                  :disabled="asyncAllCheckbox"
                >
                  <el-option
                    v-for="(item, key, index) in exportSignNameList"
                    :key="index"
                    :label="item"
                    :value="key"
                  ></el-option>
                </el-select>
                <el-checkbox class="p-l-20" v-model="asyncAllCheckbox" @change="changeaSyncAllCheckbox">同步所有功能表</el-checkbox>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <!-- <span slot="footer" class="dialog-footer">
      <el-button class="ps-cancel-btn" @click="canceDialogHandle">取 消</el-button>
      <el-button class="ps-btn" type="primary" @click="submitDialogHandle">确 定</el-button>
    </span> -->
  </customDrawer>
</template>

<script>
import { getTreeDeepkeyList, findChildSetting } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  name: 'PrintSetting',
  props: {
    tableSetting: {
      // tree 数据
      type: Array,
      default: () => []
    },
    defaultCheckedSetting: {
      // 打开弹窗时，默认选中的数据
      type: Array,
      default: () => []
    },
    extraParams: {
      // 额外的数据
      type: Object,
      default: () => {}
    },
    width: {
      // dialog宽度
      type: String,
      default: '800px'
    },
    show: {
      type: Boolean,
      required: true
    }
  },
  // watch: {
  //   dialogVisible() {
  //     this.initDefaultSettingCheck()
  //   }
  // },
  computed: {
    ...mapGetters(['userInfo']),
    dialogVisible: {
      get() {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  data() {
    return {
      propsOption: {
        children: 'children',
        label: 'label'
      },
      settingData: [],
      tableCheckedSetting: [], // 默认选中的数据
      halfCheckedKeys: [], // 子级没有全选的key
      settings: null,
      tableType: 'list',
      drawerFormData: {
        exportSignNameData: [],
        signatureList: [{ value: '主管签字：' }, { value: '制表人签字：' }, { value: '审核人签字：' }]
      },
      drawerFormDataRuls: {
        signature: [{ required: true, message: '请输入内容', trigger: 'blur' }]
      },
      exportSignNameList: {}, // 获取其他表的key
      asyncAllCheckbox: false
    }
  },
  created() {
    this.initDefaultSettingCheck()
    this.getReportExportName()
    this.getAccountPrintSignInfo()
  },
  methods: {
    changeaSyncAllCheckbox(e) {
      this.drawerFormData.exportSignNameData = []
      if (e) {
        for (const key in this.exportSignNameList) {
          this.drawerFormData.exportSignNameData.push(key)
        }
      }
    },
    // 获取数据
    async getReportExportName() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrganizationAccountGetReportExportSignNamePost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        console.log(res)
        this.exportSignNameList = res.data
        // this.$message.success('设置成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取签名数据
    async getAccountPrintSignInfo() {
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOrganizationAccountGetAccountPrintSignInfoPost({
          id: this.userInfo.account_id,
          // 需要签名数据 key 后端要重新定义新的key，所以在后面加个Sign 写死的
          print_sign_key: this.extraParams && this.extraParams.printType ? this.extraParams.printType + 'Sign' : ''
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (Array.isArray(res.data)) {
          this.drawerFormData.signatureList = res.data
        }
        // this.$message.success('设置成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    changeTableType(type) {
      this.tableType = type
    },
    getSetting(data) {
      let res = []
      for (let i = 0; i < data.length; i++) {
        if (!data[i].hidden && !data[i].children) {
          res.push(data[i])
        } else if (!data[i].hidden && data[i].children) {
          let parent = JSON.parse(JSON.stringify(data[i]))
          parent.children = this.getSetting(data[i].children)
          res.push(parent)
        }
      }
      return res
    },
    // 初始化默认参数
    initDefaultSettingCheck() {
      // 所有的key值
      const allKeys = getTreeDeepkeyList(this.defaultCheckedSetting)
      let keysList = this.getAllChildrenKeyList(this.tableSetting) // 获取子级的key list
      allKeys.forEach((key, i) => {
        // tree 树中如果子级不是全选的话需要把父级key去掉
        if (keysList[key]) {
          let isAll = keysList[key].every(v => allKeys.includes(v))
          if (!isAll) {
            allKeys.splice(i, 1)
          }
        }
      })
      this.tableCheckedSetting = allKeys
      this.settingData = this.getSetting(this.tableSetting)

      this.settings = this.defaultCheckedSetting
    },
    getAllChildrenKeyList(list) {
      let resultObj = {}
      function mapSetting(data, resultObj) {
        data.forEach(item => {
          if (item.children && item.children.length) {
            resultObj[item.key] = item.children.map(v => {
              if (v.children && v.children.length) {
                mapSetting(v.children, resultObj)
              }
              return v.key
            })
          }
        })
        return resultObj
      }
      mapSetting(list, resultObj)
      return resultObj
    },
    // tree check事件
    checkHandle(row, data) {
      let list = this.$refs.printTree.getCheckedNodes()
      this.tableCheckedSetting = list.map(item => {
        return item.key
      })
      // 子级没有全选的key
      let halfCheckedKeys = data.halfCheckedKeys
      const allChecks = [].concat(this.tableCheckedSetting, halfCheckedKeys)
      this.settings = findChildSetting(this.settingData, allChecks)
    },
    // 取消事件
    canceDialogHandle() {
      this.dialogVisible = false
    },
    // 确定事件
    submitDialogHandle() {
      this.dialogVisible = false
      let params = {
        print_sign_list: []
      }
      if (this.drawerFormData.signatureList.length) {
        this.drawerFormData.signatureList.forEach(v => {
          if (v.value) {
            params.print_sign_list.push(v)
          }
        })
      }
      if (this.drawerFormData.exportSignNameData.length) {
        params.key_list = this.drawerFormData.exportSignNameData
      }
      if (this.extraParams && this.extraParams.printType) {
        params.print_sign_key = this.extraParams.printType + 'Sign'
      }
      this.$emit('confirm', this.settings, params)
    },
    clickAddSignature() {
      this.drawerFormData.signatureList.push({ value: '' })
    },
    deleteSignature(index) {
      this.drawerFormData.signatureList.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.print-setting {
  .drawer-container {
    .drawer-print {
      padding: 20px;
      .line-wrapp {
        // position: relative;
        .line-title {
          font-weight: bold;
        }
        // .line-title::before {
        //   content: '';
        //   position: absolute;
        //   left: -10px;
        //   top: 0px;
        //   height: 20px;
        //   width: 2px;
        //   background-color: #ff9b45;
        // }
      }
      .async-text {
        color: #838b90;
        font-size: 14px;
      }
      .add-icon {
        font-size: 20px;
        cursor: pointer;
        color: #fda04d;
        padding-left: 20px;
      }
      .remove-icon {
        font-size: 20px;
        cursor: pointer;
        color: red;
        padding-left: 20px;
      }
    }
  }
  .table-type {
    padding: 20px;
    display: flex;
    font-size: 16px;
    .table-type-btn {
      width: 120px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      color: #ff9b45;
      border-radius: 5px;
      margin-right: 10px;
      border: 1px #ff9b45 solid;
      cursor: pointer;
    }
    .active-btn {
      color: #fff;
      background-color: #ff9b45;
      border: none;
    }
  }
}
</style>
