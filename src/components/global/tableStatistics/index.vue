<template>
  <!-- 报表底下的统计 start -->
  <ul class="total">
    <li v-for="item in statistics" :key="item.key" :class="[item.block?'block':'', item.class]">
      {{ item.label }}
      <span v-if="item.type === 'money'">{{ item.value | formatMoney }}</span>
      <span v-else-if="item.type === 'moneyKey'">¥{{ item.value | formatMoney }}</span>
      <span v-else-if="item.type === 'moneyFloat'">¥{{ item.value | formatMoneyFloat }}</span>
      <span v-else-if="item.type === 'moneyRmb'">{{ item.value | formatMoney }} {{ item.unit?item.unit:'' }}</span>
      <span v-else>{{ item.value + (item.unit?item.unit:'') }}</span>
      <span v-if="item.dot">，</span>
    </li>
  </ul>
  <!-- end -->
</template>

<script>
export default {
  name: 'TableStatistics',
  props: {
    statistics: {
      type: Array,
      default: () => [] // [{label:1,key:1,block:true,value:1}]
    }
  },
  data() {
    return {

    }
  },
  computed: {

  },
  watch: {

  },
  created() {

  },
  mounted() {

  },
  methods: {

  }
};
</script>

<style scoped lang="scss">

</style>
