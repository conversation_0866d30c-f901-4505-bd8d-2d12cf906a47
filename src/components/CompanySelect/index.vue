<template>
  <el-select
    v-model="selectData"
    class="user-group_select ps-select"
    :popper-class="popperClass"
    :filterable="filterable"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-option
      v-for="item in selectList"
      :key="item[options.value]"
      :label="item[options.label]"
      :value="item[options.value]"
    ></el-option>
  </el-select>
</template>

<script>
export default {
  name: 'CompanySelect',
  props: {
    value: {
      type: [String, Array, Number]
    },
    popperClass: {
      type: String,
      default: 'ps-popper-select'
    },
    filterable: {
      type: Boolean,
      default: true
    },
    options: {
      type: Object,
      default: () => {
        return {
          label: 'name',
          value: 'id'
        }
      }
    },
    companyKey: {
      type: String,
      default: ''
    },
    isSelectAll: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      selectList: []
    }
  },
  computed: {
    selectData: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('update:input', val)
        this.getSelectHandle(val)
      }
    }
  },
  watch: {
  },
  created() {
    this.getOrganizationList()
  },
  mounted() {
  },
  methods: {
    async getOrganizationList() {
      const res = await this.$apis.apiBackgroundAdminOrganizationListPost({
        parent__is_null: '1',
        status: 'enable',
        ...this.params,
        page: 1,
        page_size: 999999
      })
      if (res.code === 0) {
        if (this.companyKey === 'all') {
          // 用户健康档案 公司筛选需要 全局用户
          this.selectList = [{ name: "全局用户", company: 1 }, ...res.data.results]
        } else {
          this.selectList = res.data.results
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    getSelectHandle(e) {
      let selectItem = null
      for (let index = 0; index < this.selectList.length; index++) {
        const element = this.selectList[index];
        if (e === element[this.options.value]) {
          selectItem = {
            name: element[this.options.label],
            id: element[this.options.value]
          }
          break
        }
      }
      this.$emit('getselect', { select: e, item: selectItem })
    },
    getAllSelectData() {
      if (!this.isSelectAll) {
        let ids = []
        console.log('this.selectList', this.selectList)
        this.selectList.forEach(item => {
          ids.push(item.company)
        })
        this.$emit('selectAll', ids)
      } else {
        this.$emit('selectAll', [])
      }
    }
  }
}
</script>

<style lang="scss" scope>
// .user-group_select {
// }
</style>
