<template>
  <el-dialog  :visible.sync="visible" :title="title" :width="width"  @close="handlerClose">
    <div class="dialog-content">
      <i class="el-icon-caret-left" style="font-size: 50px; cursor: pointer;" @click="previewImg" v-if="picList && picList.length>0 && currentIndex >0"></i>
      <div class="img-wrapper">
        <div class="img-box">
          <!-- <div class="img-item-title">张三</div> -->
          <img :src="imgUrl" alt=""  class="img-style">
          <div class="img-item-index">{{ currentIndex + 1 }}/{{ picList.length }}</div>
        </div>
      </div>
      <i class="el-icon-caret-right" style="font-size: 50px; cursor: pointer;" @click="nextImg" v-if="picList && picList.length>0 && currentIndex < (picList.length-1)"></i>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handlerClose">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
export default {
  name: 'ImageViewPreview',
  props: {
    loading: Boolean,
    isshow: Boolean,
    title: { // 标题
      type: String,
      default: '查看详情'
    },
    width: { // 宽度
      type: String,
      default: '600px'
    },
    picList: { // 图片列表
      type: Array,
      default: () => []
    },
    dialogType: { // 类型
      type: String,
      default: 'default'
    },
    current: {
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      isLoading: false,
      currentIndex: this.current,
      imgUrl: ''
    }
  },
  computed: {
    visible: {
      get() {
        console.log("visible", this.isshow);
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    current: function(val) {
      this.currentIndex = this.current
    },
    visible() {
      if (this.visible) {
        this.imgUrl = this.picList[this.currentIndex]
        console.log("imgUrl", this.imgUrl);
      }
    }
  },
  mounted() { },
  methods: {
    // 关闭
    handlerClose() {
      this.visible = false
      this.currentIndex = 0
      this.imgUrl = ''
      this.$emit('close', false)
    },
    // 上一张图片
    previewImg() {
      this.currentIndex = this.currentIndex - 1
      this.imgUrl = this.picList[this.currentIndex]
    },
    // 下一张图片
    nextImg() {
      this.currentIndex = this.currentIndex + 1
      this.imgUrl = this.picList[this.currentIndex]
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content{
  display: flex;
  justify-content: center;
  align-items: center;
}
.img-style {
  width:400px ;
}
.img-item-title{
  text-align: center;
  margin-bottom: 10px;
}
.img-item-index{
  margin-top: 10px;
  text-align: center;
}
.dialog-footer {
  text-align: center;
}
</style>
