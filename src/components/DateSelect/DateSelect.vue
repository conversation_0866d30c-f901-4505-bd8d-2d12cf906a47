<template>
  <div id="myDate">
    <div v-if="type === 'ONE_RELEASE'">
      <el-select
        v-model="yearsModel"
        @change="dateChange(1)"
        placeholder="请选择"
        style="width:100px;"
        class="ps-select"
        popper-class="ps-popper-select"
      >
        <el-option
          v-for="item in years"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          :disabled="item.disabled"
        ></el-option>
      </el-select>
      <el-select
        v-model="monthsModel"
        @change="dateChange(2)"
        placeholder="请选择"
        style="width:100px;"
        class="ps-select"
        popper-class="ps-popper-select"
      >
        <el-option
          v-for="item in months"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          :disabled="item.disabled"
        ></el-option>
      </el-select>
      <el-select
        class="ps-select"
        popper-class="ps-popper-select"
        v-model="daysModel"
        @change="dateChange(3)"
        placeholder="请选择"
        style="width:100px;"
      >
        <el-option
          v-for="item in days"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          :disabled="item.disabled"
        ></el-option>
      </el-select>
    </div>
    <div v-else-if="type === 'MONTH_RELEASE'">
      <el-select
        class="ps-select"
        popper-class="ps-popper-select"
        v-model="daysModel"
        @change="dateChange(3)"
        placeholder="请选择"
        style="width:100px;"
      >
        <el-option
          v-for="item in days"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </div>

    <div v-if="type === 'yearMonth'">
      <el-select
        v-model="yearsModel"
        @change="dateChange(1)"
        placeholder="请选择"
        style="width:100px;"
        class="ps-select"
        popper-class="ps-popper-select"
      >
        <el-option
          v-for="item in years"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-select
        v-model="monthsModel"
        @change="dateChange(2)"
        placeholder="请选择"
        style="width:100px;"
        class="ps-select"
        popper-class="ps-popper-select"
      >
        <el-option
          v-for="item in months"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </div>
  </div>
</template>

<script>
/* eslint-disable camelcase */
export default {
  props: {
    type: {
      type: String
    },
    increase: {
      // 年份增or减
      type: Boolean,
      default: true
    },
    defaultdate: {
      // 默认时间，可能是异步获取的
      type: String
    }
  },
  data() {
    return {
      yearsModel: null,
      years: [],
      monthsModel: null,
      months: [],
      daysModel: null,
      days: [],
      once: true, // 用于判断首次进来是否有值
      nowdate: ''
    }
  },
  mounted() {
    this.nowdate = new Date() // 当前时间，用于判断日期的disabled状态
    if (this.defaultdate) {
      // 如果是异步获取的当前的是没值的
      this.once = false
    }
    this.init() // 初始化数据
  },
  watch: {
    defaultdate: function(val, old) {
      // 如果是异步数据需要监听重新初始化
      this.init()
      if (this.defaultdate) {
        // 如果是异步获取的当前的是没值的
        this.once = false
      }
    }
  },
  methods: {
    init() {
      var myDate, year, month, day
      myDate = new Date()
      year = myDate.getFullYear() // 获取当前年
      month = myDate.getMonth() + 1 // 获取当前月
      day = myDate.getDate() // 获取当前日

      if (this.defaultdate) {
        myDate = new Date(this.defaultdate)
        year = myDate.getFullYear() // 获取当前年
        month = myDate.getMonth() + 1 // 获取当前月
        day = myDate.getDate() // 获取当前日
      } else {
        myDate = new Date()
        year = myDate.getFullYear() // 获取当前年
        month = myDate.getMonth() + 1 // 获取当前月
        day = myDate.getDate() // 获取当前日
      }
      this.yearsModel = year
      this.monthsModel = month
      this.daysModel = day

      // 生成年份列表
      this.initSelectYear(year)
      // 生成月份列表
      this.initSelectMonth(year, month)
      // 生成日列表
      this.initSelectDay(year, month, day)

      let obj = {
        year: this.yearsModel,
        month: this.monthsModel,
        day: this.daysModel
      }
      // 初始化会触发相关change事件
      this.dateChange(obj)
    },
    initSelectYear(year) {
      this.years = []
      let startyear = year
      let len = 30
      let mix = this.nowdate.getFullYear() - year
      if (mix > 0) {
        len += mix
      } else {
        startyear = this.nowdate.getFullYear()
      }
      for (let i = 0; i < len; i++) {
        if (this.increase) {
          // this.years.push({ value: year + i, label: year + i + "年" });
          if (this.defaultdate) {
            let disabled = false
            if (year + i < this.nowdate.getFullYear()) {
              disabled = true
              this.monthsModel = ''
              this.daysModel = ''
            }
            this.years.push({
              value: startyear + i,
              label: startyear + i + '年',
              disabled: disabled
            })
          } else {
            this.years.push({
              value: startyear + i,
              label: startyear + i + '年'
            })
          }
        } else {
          this.years.push({
            value: startyear - i,
            label: startyear - i + '年'
          })
        }
      }
    },
    initSelectMonth(year, month) {
      console.log(this.defaultdate, year)
      // this.monthsModel = ''
      this.months = []
      // this.yearsModel = year;
      // this.monthsModel = month;
      // this.daysModel = day;
      // this.months.push({value:0,label:"全部"});
      for (let i = 1; i <= 12; i++) {
        // this.months.push({ value: i, label: i + "月" });
        if (this.defaultdate || year) {
          let disabled = false
          if (this.yearsModel < this.nowdate.getFullYear()) {
            disabled = true
          } else if (this.yearsModel === this.nowdate.getFullYear()) {
            if (this.nowdate.getMonth() + 1 > i) {
              disabled = true
            }
          }
          this.months.push({ value: i, label: i + '月', disabled: disabled })
        } else {
          this.months.push({
            value: i,
            label: i + '月',
            disabled: this.nowdate.getMonth() + 1 > i
          })
        }
      }

      if (
        this.yearsModel <= this.nowdate.getFullYear() &&
        Number(this.monthsModel) < this.nowdate.getMonth() + 1
      ) {
        this.monthsModel = this.nowdate.getMonth() + 1
      }
    },
    initSelectDay(year, month, day) {
      var maxDay = this.getMaxDay(year, month)
      this.days = []
      // this.days.push({value:0,label:"全部"});
      for (var i = 1; i <= maxDay; i++) {
        // this.days.push({ value: i, label: i + "日" });
        let disabled = false
        if (this.defaultdate && this.type !== 'MONTH_RELEASE') {
          if (this.yearsModel < this.nowdate.getFullYear()) {
            disabled = true
          } else if (this.yearsModel === this.nowdate.getFullYear()) {
            // console.log(month, this.nowdate.getMonth() + 1)
            if (this.monthsModel < this.nowdate.getMonth() + 1) {
              disabled = true
            } else if (this.monthsModel === this.nowdate.getMonth() + 1) {
              if (i < this.nowdate.getDate()) {
                disabled = true
              }
            }
          }
          this.days.push({ value: i, label: i + '日', disabled: disabled })
        } else if (this.type === 'MONTH_RELEASE') {
          this.days.push({ value: i, label: i + '日' })
        } else {
          if (
            this.monthsModel === this.nowdate.getMonth() + 1 &&
            this.yearsModel === this.nowdate.getFullYear()
          ) {
            if (i < this.nowdate.getDate()) {
              disabled = true
            }
          }
          this.days.push({ value: i, label: i + '日', disabled: disabled })
        }
      }
    },
    // initChange() {
    //   // 生成月份列表
    //   this.initSelectMonth(year, month)
    //   // 生成日列表
    //   this.initSelectDay(year, month, day)
    // },
    dateChange(type) {
      // 1年 2月 3日 4 左 5右
      if (type === 1) {
        if (!this.once) {
          // this.monthsModel = 1
          // this.daysModel = 1
          if (this.nowdate.getFullYear() === this.yearsModel) {
            this.monthsModel = this.nowdate.getMonth() + 1
            this.daysModel = this.nowdate.getDate()
          }
        }
      }
      if (type === 2) {
        // this.daysModel = 1
        // if (
        //   this.nowdate.getFullYear() == this.yearsModel &&
        //   this.monthsModel == this.nowdate.getMonth() + 1&&!this.daysModel
        // ) {
        //   this.daysModel = this.nowdate.getDate();
        // }
        // 添加默认值 如果相同
        if (
          this.nowdate.getFullYear() === this.yearsModel &&
          this.nowdate.getMonth() + 1 === Number(this.monthsModel)
        ) {
          this.daysModel = this.nowdate.getDate()
        }
        this.initSelectDay(this.yearsModel, this.monthsModel)
      }
      if (type === 1 || type === 2) {
        this.initSelectMonth(this.yearsModel)
        if (this.monthsModel === 0) {
          this.daysModel = ''
          this.initSelectDay(this.yearsModel, 1)
        } else {
          this.initSelectDay(this.yearsModel, this.monthsModel)
        }
        // 添加默认值 如果相同
        if (
          this.nowdate.getFullYear() === this.yearsModel &&
          this.nowdate.getMonth() + 1 === Number(this.monthsModel)
        ) {
          this.daysModel = this.nowdate.getDate()
        }
      }
      if (type === 4) {
        this.dayleft()
      }
      if (type === 5) {
        this.dayright()
      }
      let dateChangeValue = `${this.yearsModel}-${this.monthsModel < 10 ? '0' + this.monthsModel : this.monthsModel}-${this.daysModel < 10 ? '0' + this.daysModel : this.daysModel}`
      this.$emit('dateChange', dateChangeValue)
      this.$emit('dateChangeYear', this.yearsModel)
      this.$emit('dateChangeMonth', this.monthsModel)
      this.$emit('dateChangeDay', this.daysModel)
    },
    dayleft() {
      var tmpYear = this.yearsModel
      var tmpMonth = this.monthsModel
      var tmpDay = this.daysModel
      if (tmpYear == null) {
        var myDate = new Date()
        var year = myDate.getFullYear() // 获取当前年
        var month = myDate.getMonth() + 1 // 获取当前月
        var day = myDate.getDate() // 获取当前日
        this.yearsModel = year
        this.monthsModel = month
        this.daysModel = day
        return
      }
      if (tmpMonth == null) {
        tmpMonth = 0
      }
      if (tmpDay == null) {
        tmpDay = 0
      }

      var yearV = tmpYear
      var monthV = tmpMonth
      var dayV = tmpDay

      if ((tmpMonth === 0 || tmpMonth === 1) && (tmpDay === 0 || tmpDay === 1)) {
        yearV = tmpYear - 1
        monthV = 12
        dayV = this.getMaxDay(tmpYear, tmpMonth)
      }
      if (!(tmpMonth === 0 || tmpMonth === 1) && (tmpDay === 0 || tmpDay === 1)) {
        monthV = tmpMonth - 1
        dayV = this.getMaxDay(tmpYear, tmpMonth)
      }
      if ((tmpMonth === 0 || tmpMonth === 1) && !(tmpDay === 0 || tmpDay === 1)) {
        dayV = tmpDay - 1
      }
      if (!(tmpMonth === 0 || tmpMonth === 1) && !(tmpDay === 0 || tmpDay === 1)) {
        dayV = tmpDay - 1
      }
      this.yearsModel = yearV
      this.monthsModel = monthV
      this.daysModel = dayV
    },
    dayright() {
      var myDate = new Date()
      var year = myDate.getFullYear() // 获取当前年
      var month = myDate.getMonth() + 1 // 获取当前月
      var day = myDate.getDate() // 获取当前日

      var tmpYear = this.yearsModel
      var tmpMonth = this.monthsModel
      var tmpDay = this.daysModel

      if (tmpYear == null) {
        myDate = new Date()
        year = myDate.getFullYear() // 获取当前年
        month = myDate.getMonth() + 1 // 获取当前月
        day = myDate.getDate() // 获取当前日
        this.yearsModel = year
        this.monthsModel = month
        this.daysModel = day
        return
      }

      if (tmpMonth == null) {
        tmpMonth = 0
      }
      if (tmpDay == null) {
        tmpDay = 0
      }
      if (tmpYear > year) {
        this.yearsModel = year
        this.monthsModel = month
        this.daysModel = day
        return
      }
      if (tmpYear === year) {
        if (
          tmpMonth > month ||
          tmpMonth === 0 ||
          (tmpMonth === month && (tmpDay >= day || tmpDay === 0))
        ) {
          this.yearsModel = year
          this.monthsModel = month
          this.daysModel = day
          return
        }
      }

      var maxDay = this.getMaxDay(tmpYear, tmpMonth)
      var yearV = tmpYear
      var monthV = tmpMonth
      var dayV = tmpDay

      if ((tmpMonth === 0 || tmpMonth === 12) && (tmpDay === 0 || tmpDay === maxDay)) {
        yearV = tmpYear + 1
        monthV = 1
        dayV = 1
      }
      if (!(tmpMonth === 0 || tmpMonth === 12) && (tmpDay === 0 || tmpDay === maxDay)) {
        monthV = tmpMonth + 1
        dayV = 1
      }
      if (!(tmpMonth === 0 || tmpMonth === 12) && !(tmpDay === 0 || tmpDay === maxDay)) {
        dayV = tmpDay + 1
      }

      this.yearsModel = yearV
      this.monthsModel = monthV
      this.daysModel = dayV
    },
    getMaxDay(year, month) {
      var new_year = year // 取当前的年份
      var new_month = month++ // 取下一个月的第一天，方便计算（最后一天不固定）
      if (month > 12) {
        // 如果当前大于12月，则年份转到下一年
        new_month -= 12 // 月份减
        new_year++ // 年份增
      }
      var new_date = new Date(new_year, new_month, 1) // 取当年当月中的第一天
      return new Date(new_date.getTime() - 1000 * 60 * 60 * 24).getDate() // 获取当月最后一天日期
    }
  },
  created() {
    // console.log(this.type);
    // this.init();
  }
}
</script>

<style lang="scss" scoped></style>
