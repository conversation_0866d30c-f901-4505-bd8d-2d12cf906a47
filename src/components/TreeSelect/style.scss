$origin: #ff9b45;

.vue-treeselect{
  .vue-treeselect__value-container, .vue-treeselect__multi-value{
    line-height: normal;
  }
  .vue-treeselect__multi-value-item{
    color: #33383c;
    background-color: #f3f6f8;
    .vue-treeselect__value-remove{
      color: #ccc;
    }
  }
  .vue-treeselect__option{
    &:hover{
      .vue-treeselect__checkbox{
        border-color: $origin;
      }
    }
    .vue-treeselect__checkbox--checked{
      color: $origin;
      background-color: $origin;
      border-color: $origin;
    }
    .vue-treeselect__checkbox--indeterminate{
      color: $origin;
      background-color: $origin;
      border-color: $origin;
    }
  }
}

.vue-treeselect__multi-value-item, .vue-treeselect--single, .vue-treeselect--multi{
  color: #606266 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

//兼容单项label过长时，被顶开成多行的情况，limit最好设置为1
.vue-treeselect__multi-value{
  display: flex;
  align-items: center;
}
.vue-treeselect__multi-value-item-container{
  display: block;
  max-width: 90%;
}
.vue-treeselect__multi-value-item{
  display: flex;
  max-width: 100%;
  align-items: center;
}
.vue-treeselect__multi-value-label{
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
  overflow: hidden;
  display: block;
}