<template>
    <el-table v-loading="isLoading" ref="tableRef" :data="tableData">
      <el-table-column
        v-for="item in tableSetting"
        :key="item.key"
        :label="item.label"
        :prop="item.key"
        align="center"
        :show-overflow-tooltip="item.tooltip"
        :class-name="item.tooltip ? `is-ellipsis ${item.class}` : item.class"
      >
        <template v-if="item.children">
          <el-table-column
            v-for="child in item.children"
            :key="child.key"
            :label="child.label"
            :prop="child.key"
            align="center"
            :show-overflow-tooltip="item.tooltip"
            :class-name="item.tooltip ? `is-ellipsis ${item.class}` : item.class"
          ></el-table-column>
        </template>
      </el-table-column>
    </el-table>
</template>

<script>
export default {
  name: 'PrintTable',
  props: {
    tableSetting: {
      type: Array,
      default: () => []
    },
    tableData: {
      type: Array,
      default: () => []
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>

<style lang="scss">
</style>
