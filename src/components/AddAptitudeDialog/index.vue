<template>
  <!-- 添加/编辑 -->
  <custom-drawer
    :title="title"
    :show.sync="visible"
    :direction="direction"
    :wrapperClosable="wrapperClosable"
    :size="size"
    class="aptitude-drawer-wrapper"
    v-bind="$attrs"
    v-on="$listeners"
    confirm-text="确定"
    @close="handlerClose"
    @cancel="clickCancleHandle"
    @confirm="clickConfirmHandle"
  >
    <el-form
      :model="formData"
      ref="formRef"
      :rules="formDataRules"
      label-width="160px"
      class="dialog-form m-t-20"
      v-loading="isLoading"
      :status-icon="false"
      size="small"
    >
      <el-form-item label="资质类型" prop="aptitude">
        <el-select v-model="formData.aptitude" class="ps-select form-item-w" popper-class="ps-popper-select" placeholder="请选择" :disabled="type === 'detail'" @change="setRules">
          <el-option v-for="option in aptitudeList" :key="option.value" :label="option.label" :value="option.value" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="经营者名称" prop="name">
        <el-input v-model="formData.name" class="form-item-w" :disabled="type === 'detail'"></el-input>
      </el-form-item>
      <div v-if="formData.aptitude === '1'">
        <el-form-item label="类型" prop="aptitude_type">
          <el-input v-model="formData.aptitude_type" class="form-item-w" :disabled="type === 'detail'"></el-input>
        </el-form-item>
        <el-form-item label="法定代表人" prop="legal_representative">
          <el-input
            v-model="formData.legal_representative"
            class="form-item-w"
            maxlength="20"
            :disabled="type === 'detail'"
          />
        </el-form-item>
        <el-form-item label="经营期限" prop="aptitude_date">
          <delivery-order>
            <el-date-picker
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              v-model="formData.aptitude_date"
              class="form-item-w"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :disabled="type === 'detail'">
            </el-date-picker>
          </delivery-order>
        </el-form-item>
        <el-form-item label="经营范围" prop="business_scope">
          <el-input v-model="formData.business_scope" type="textarea" :rows="2" class="form-item-w" :disabled="type === 'detail'"></el-input>
        </el-form-item>
      </div>
      <div v-if="formData.aptitude === '2'">
        <el-form-item label="法人代表" prop="legalRepresentative">
          <el-input v-model="formData.legalRepresentative" class="form-item-w" :disabled="type === 'detail'"></el-input>
        </el-form-item>
        <el-form-item label="住所" prop="domicile">
          <el-input v-model="formData.domicile" class="form-item-w" :disabled="type === 'detail'"></el-input>
        </el-form-item>
        <el-form-item label="经营场所" prop="businessPremises">
          <el-input v-model="formData.businessPremises" class="form-item-w" :disabled="type === 'detail'"></el-input>
        </el-form-item>
        <el-form-item label="主体业态" prop="mainFormOfBusiness">
          <el-input v-model="formData.mainFormOfBusiness" class="form-item-w" :disabled="type === 'detail'"></el-input>
        </el-form-item>
        <el-form-item label="经营项目" prop="businessProject">
          <el-input v-model="formData.businessProject" class="form-item-w" :disabled="type === 'detail'"></el-input>
        </el-form-item>
        <el-form-item label="许可证编号" prop="licenseNumber">
          <el-input v-model="formData.licenseNumber" class="form-item-w" :disabled="type === 'detail'"></el-input>
        </el-form-item>
        <el-form-item label="举报电话" prop="tipOffTelephone">
          <el-input v-model="formData.tipOffTelephone" class="form-item-w" :disabled="type === 'detail'"></el-input>
        </el-form-item>
        <el-form-item label="签发机关" prop="issuingAuthority">
          <el-input v-model="formData.issuingAuthority" class="form-item-w" :disabled="type === 'detail'"></el-input>
        </el-form-item>
        <el-form-item label="食堂最大供餐人数" prop="maxMealNum">
          <el-input v-model="formData.maxMealNum" class="form-item-w" :disabled="type === 'detail'"></el-input>
        </el-form-item>
      </div>
      <div v-if="formData.aptitude === '2' || formData.aptitude === '3'">
        <el-form-item label="有效期限" prop="expiry_date">
          <el-date-picker
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            v-model="formData.expiry_date"
            class="form-item-w"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :disabled="type === 'detail'">
          </el-date-picker>
        </el-form-item>
      </div>
      <div v-if="formData.aptitude === '1'">
        <el-form-item label="注册日期" prop="register_date">
          <div style="width: 400px">
            <el-date-picker
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              v-model="formData.register_date"
              type="date"
              class="form-item-w"
              :disabled="type === 'detail'"
            />
          </div>
        </el-form-item>
      </div>
      <el-form-item label="资质照片" prop="imageFileList">
        <div class="red m-b-10">仅支持jpg、png、bmp格式，大小不超过2M</div>
        <el-upload
          :class="{'upload-img-wrapper': true, 'hide-upload':formData.imageFileList.length>0}"
          drag
          :data="uploadParams"
          :action="actionUrl"
          :multiple="false"
          :file-list="formData.imageFileList"
          list-type="picture-card"
          :on-change="handelChange"
          :on-success="handleImgSuccess"
          :before-upload="beforeImgUpload"
          :limit="1"
          :headers="headersOpts"
          :disabled="type === 'detail'"
        >
          <i v-if="formData.imageFileList.length<1" class="el-icon-plus"></i>
          <div slot="file" slot-scope="{file}" v-loading="file.status==='uploading'" element-loading-text="上传中">
            <!-- <img :src="formData.imageFile" alt=""> -->
            <el-image
              v-if="formData.imageFileList[0] && formData.imageFileList[0].public_url"
              :src="formData.imageFileList[0].public_url"
              :fit="'fill'"></el-image>
            <span v-if="formData.imageFileList[0] && formData.imageFileList[0].public_url" class="el-upload-list__item-actions">
              <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(formData.imageFileList[0].public_url)">
                <i class="el-icon-zoom-in"></i>
              </span>
              <span class="el-upload-list__item-delete" @click="handleImgRemove(file)">
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
  </custom-drawer>
  <!-- end -->
</template>

<script>
import { deepClone, getSuffix, getToken } from '@/utils/index'
import { mapGetters } from 'vuex'
import { integer } from '@/utils/validata'
import dayjs from 'dayjs'

export default {
  name: 'addAptitudeDialog',
  props: {
    showdialog: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    wrapperClosable: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '新建资质'
    },
    size: {
      type: [String, Number],
      default: 720
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  // mixins: [activatedLoadData],
  data() {
    var validateNumber = (rule, value, callback) => {
      if (value && !/^(0|[1-9]\d*)$/.test(value)) {
        callback(new Error('请输入数字'))
      } else {
        if (value === "") {
          callback()
        } else if (Number(value) === 0) {
          callback(new Error("请输入大于0的数字"))
        } else {
          callback()
        }
      }
    }
    return {
      isLoading: false,
      formData: {
        aptitude: '', // 资质类型
        // code: '', // 社会统一信用代码
        aptitude_type: '', // 类型,
        legal_representative: "", // 法定代表人
        aptitude_date: [], // 经营期限
        business_scope: '', // 经营范围
        // imageFile: [],
        imageFileList: [], // 资质图片
        expiry_date: [],
        register_date: null, // 注册日期
        legalRepresentative: '',
        domicile: '',
        businessPremises: '',
        mainFormOfBusiness: '',
        businessProject: '',
        licenseNumber: '',
        tipOffTelephone: '',
        issuingAuthority: '',
        maxMealNum: ''
      },
      // defaultFormDataRules: {
      //   name: [{ required: true, message: '请输入分类名称', trigger: 'change' }],
      //   expiry_date: [{ required: true, message: '请选择有效期限', trigger: 'change' }],
      //   imageFileList: [{ required: true, message: '请选择资质照片', trigger: 'change' }]
      // },
      formDataRules: {
        name: [{ required: true, message: '请输入分类名称', trigger: 'change' }],
        expiry_date: [{ required: true, message: '请选择有效期限', trigger: 'change' }],
        imageFileList: [{ required: true, message: '请选择资质照片', trigger: 'change' }],
        aptitude_type: [{ required: true, message: '请输入', trigger: ['change', 'blur'] }],
        business_scope: [{ required: true, message: '请输入', trigger: ['change', 'blur'] }],
        legalRepresentative: [{ required: true, message: '请输入', trigger: 'change' }],
        domicile: [{ required: true, message: '请输入', trigger: 'change' }],
        businessPremises: [{ required: true, message: '请输入', trigger: 'change' }],
        mainFormOfBusiness: [{ required: true, message: '请输入', trigger: 'change' }],
        businessProject: [{ required: true, message: '请输入', trigger: 'change' }],
        licenseNumber: [{ required: true, message: '请输入', trigger: 'change' }],
        tipOffTelephone: [{ required: true, message: '请输入', trigger: 'change' }],
        issuingAuthority: [{ required: true, message: '请输入', trigger: 'change' }],
        maxMealNum: [{ validator: validateNumber, trigger: 'change' }]
      },
      aptitudeList: [
        { label: '营业执照', value: '1' },
        { label: '食品经营许可证', value: '2' },
        { label: '食品生产许可证', value: '3' }
      ],
      uploadParams: {
        prefix: 'inventoryImage'
      },
      actionUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    visible: {
      get() {
        return this.showdialog
      },
      set(val) {
        this.$emit('update:showdialog', val)
      }
    }
  },
  watch: {
    showdialog(val) {
      if (val) {
        this.initLoad()
      }
    }
  },
  created() {
  },
  mounted() {},
  methods: {
    async initLoad() {
      if (this.type === 'modify' || this.type === 'detail') {
        let data = deepClone(this.infoData)
        if (this.infoData.imgUrl && this.infoData.imgUrl.length > 0) {
          data.imageFileList = this.infoData.imgUrl.map(v => {
            return {
              url: v,
              name: v,
              status: "success",
              uid: v,
              public_url: v
            }
          })
        } else {
          data.imageFileList = []
        }
        this.formData = data
      } else {
        this.formData = {
          aptitude: '', // 资质类型
          code: '', // 社会统一信用代码
          aptitude_type: '', // 类型,
          legal_representative: "", // 法定代表人
          aptitude_date: [], // 经营期限
          business_scope: '', // 经营范围
          // imageFile: [],
          imageFileList: [], // 资质图片
          expiry_date: [],
          legalRepresentative: '',
          domicile: '',
          businessPremises: '',
          mainFormOfBusiness: '',
          businessProject: '',
          licenseNumber: '',
          tipOffTelephone: '',
          issuingAuthority: '',
          maxMealNum: ''
        }
      }
    },
    // 设置文件名
    handelChange(file, fileList) {
      this.uploadParams.key =
        this.uploadParams.prefix + '_' + new Date().getTime() + Math.floor(Math.random() * 150) + '.png'
    },
    // 上传成功数据处理
    handleImgSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.formData.imageFileList = fileList.map(v => {
          if (v.uid === file.uid) {
            v.public_url = res.data.public_url
          }
          return v
        })
        // this.$nextTick(() => {
        //   this.$refs.formRef.clearValidate('imageFile')
        // })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除图片
    handleImgRemove(file, type) {
      if (this.type === 'detail') {
        return
      }
      let index = this.formData.imageFileList.findIndex(item => item.url === file.url)
      this.formData.imageFileList.splice(index, 1)
    },
    // 上传图片前校验文件类型
    beforeImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 > 2
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是PNG/JPG/bmp格式!')
        return false
      }

      if (isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
    },

    clickConfirmHandle() {
      let params = {
        id: new Date().getTime(),
        name: this.formData.name,
        fileType: this.formData.aptitude,
        upLoadTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        editTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        operator: this.userInfo.member_name,
        ...this.formData
      }
      delete params.imageFileList
      console.log(111, params)
      let imgUrl = []
      this.formData.imageFileList.forEach(v => {
        if (v.public_url) {
          imgUrl.push(v.public_url)
        }
      })
      params.imgUrl = imgUrl

      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (this.isLoading) return
          this.isLoading = true
          this.$emit('clickConfirm', params, this.type)
          this.visible = false
        } else {
          this.$message.error('添加失败')
        }
      })
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handlerClose(e) {
      this.formData = {
        aptitude: '', // 资质类型
        code: '', // 社会统一信用代码
        aptitude_type: '', // 类型,
        legal_representative: "", // 法定代表人
        aptitude_date: [], // 经营期限
        business_scope: '', // 经营范围
        // imageFile: [],
        imageFileList: [], // 资质图片
        expiry_date: [],
        legalRepresentative: '',
        domicile: '',
        businessPremises: '',
        mainFormOfBusiness: '',
        businessProject: '',
        licenseNumber: '',
        tipOffTelephone: '',
        issuingAuthority: '',
        maxMealNum: ''
      }
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
      this.isLoading = false
    },
    handlePictureCardPreview(file) {
      console.log(file)
      this.$emit('showImgViewer', file)
    },
    setRules() {
      // let rules = deepClone(this.defaultFormDataRules)
      // switch (this.formData.aptitude) {
      //   case '1':
      //     rules = {
      //       ...rules,
      //       aptitude_type: [{ required: true, message: '请输入', trigger: ['change', 'blur'] }],
      //       business_scope: [{ required: true, message: '请输入', trigger: ['change', 'blur'] }]
      //     }
      //     break
      //   case '2':
      //     rules = {
      //       ...rules,
      //       legalRepresentative: [{ required: true, message: '请输入', trigger: 'change' }],
      //       domicile: [{ required: true, message: '请输入', trigger: 'change' }],
      //       businessPremises: [{ required: true, message: '请输入', trigger: 'change' }],
      //       mainFormOfBusiness: [{ required: true, message: '请输入', trigger: 'change' }],
      //       businessProject: [{ required: true, message: '请输入', trigger: 'change' }],
      //       licenseNumber: [{ required: true, message: '请输入', trigger: 'change' }],
      //       tipOffTelephone: [{ required: true, message: '请输入', trigger: 'change' }],
      //       maxMealNum: [{ required: true, message: '请输入', trigger: 'change' }],
      //       issuingAuthority: [{ required: true, message: '请输入', trigger: 'change' }]
      //     }
      //     break
      // }
      // this.formDataRules = deepClone(rules)
      // if (this.type === 'add') {
      setTimeout(() => {
        this.$refs.formRef.clearValidate()
      }, 10)
      // }
    }
  }
}
</script>

<style lang="scss">
.aptitude-drawer-wrapper {
  font-size: 14px;
  .el-drawer__header {
    margin-bottom: 0;
    padding: 23px 20px;
    background: #e7e9ef;
  }
  .form-item-w {
    width: 300px;
  }
  .upload-img-wrapper{
    overflow: hidden;
    max-height: 830px;
    &.hide-upload{
      .el-upload--picture-card{
        display: none;
      }
    }
    .el-upload--picture-card{
      border: none;
    }
    .el-upload-dragger{
      width: 194px;
      height: 114px;
    }
    .upload-img{
      // display: flex;
      // align-items: center;
      // justify-content: center;
      width: 194px;
      height: 114px;
      img{
        max-width: 194px;
        max-height: 114px;
      }
    }
    .el-upload--picture-card {
      width: 194px;
      height: 114px;
      line-height: 114px;
    }
  }
}
</style>
