<template>
  <div>
    <el-option :key="source[value]" :label="source[label]" :value="source[value]" :disabled="source.disabled">
      <span>{{ source[label] }}</span>
      <span v-if="isRight" style="float: right; color: #939393">{{ source[value] }}</span>
    </el-option>
  </div>
</template>

<script>
export default {
  name: 'VirtualItem',
  props: {
    // index of current item
    // 每一行的索引
    index: {
      type: Number
    },
    // 每一行的内容
    source: {
      type: Object,
      default() {
        return {}
      }
    },
    // 需要显示的名称
    label: {
      type: String
    },
    // 绑定的值
    value: {
      type: String
    },
    disabled: {
      type: String
    },
    // 右侧是否显示绑定的值
    isRight: {
      type: <PERSON>olean,
      default() {
        return false
      }
    }
  },
  mounted() {},
  methods: {
    dispatch(componentName, eventName, ...rest) {
      let parent = this.$parent || this.$root;
      let name = parent.$options.name;
      while (parent && (!name || name !== componentName)) {
        parent = parent.$parent;
        if (parent) {
          name = parent.$options.name;
        }
      }
      if (parent) {
        parent.$emit.apply(parent, [eventName].concat(rest));
      }
    },
    // 不能通过事件派发的方式通知，当存在多个寻滚动列表时会出问题，具体是哪出问题呢（不清楚，可能是有啥东西影响了）
    // @click.native="handleClick"
    handleClick() {
      if (this.source.disabled) return
      // 通知 SelectVirtualList 组件，点击了项目
      this.dispatch("VirtualListSelect", "clickVirtualItem", this.source);
    }
  }
}
</script>
