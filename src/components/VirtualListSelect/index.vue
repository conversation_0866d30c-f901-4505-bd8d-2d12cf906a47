<template>
  <el-select
    :value="defaultValue"
    popper-class="virtualselect"
    :filter-method="filterMethod"
    :filterable="filterable"
    @visible-change="visibleChange"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <virtual-list
      ref="virtualListRef"
      class="virtualselect-list"
      :data-key="selectOption.value"
      :data-sources="selectArr"
      :data-component="itemComponent"
      :keeps="20"
      :extra-props="{
        label: selectOption.label,
        value: selectOption.value,
        isRight: selectOption.isRight
      }"
      @scroll="scrollVirtualHandle"
    ></virtual-list>
  </el-select>
</template>

<script>
import VirtualList from 'vue-virtual-scroll-list'
import itemComponent from './itemComponent'
import { debounce } from '@/utils'

export default {
  inheritAttrs: false,
  name: 'VirtualListSelect',
  components: {
    'virtual-list': VirtualList
  },
  // 自定义，为什么要用自定义呢，是为了防止组件emit触发相同的事件相互间影响啊
  model: {
    prop: 'defaultValue',
    event: 'change'
  },
  props: {
    // 列表数据
    selectData: {
      type: Array,
      default() {
        return []
      }
    },
    // 展示的配置
    selectOption: {
      type: Object,
      default() {
        return {
          label: 'name', // 下拉框需要显示的名称
          value: 'id', // 下拉框绑定的值
          isRight: false // 右侧是否显示
        }
      }
    },
    // 默认值
    defaultValue: {
      type: [String, Number, Array],
      default: () => {
        return []
      }
    },
    filterable: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    selectData: {
      handler(newValue) {
        console.log(123131231)
        // 当下拉显示时并且数据有更新时需要更新数据
        if (this.visible) {
          this.init()
        }
      },
      deep: true
    }
  },
  data() {
    return {
      visible: false,
      itemComponent: itemComponent,
      selectArr: [],
      offsetTop: 0,
      scrollIndex: 0
    }
  },
  created() {
    // 监听点击子组件
    // this.$on("clickVirtualItem", (item) => {
    //   // this.scrollIndex = item.index
    //   console.log(item.index)
    // });
  },
  mounted() {
    this.init()
  },
  methods: {
    scrollVirtualHandle: debounce(function(event, range) {
      // console.log(event, range)
      // 记录下滚动的高度，后续得还原
      console.log(this.$refs.virtualListRef.getOffset())
      this.offsetTop = this.$refs.virtualListRef.getOffset()
    }, 300),
    // 初始化
    init() {
      if (!this.value || this.value.length === 0) {
        this.selectArr = JSON.parse(JSON.stringify(this.selectData));
        // .map((v, index) => {
        //   v.index = index
        //   return v
        // });
      } else { // 不用这种，显示很奇怪
        /**  回显问题
          由于只渲染20条数据,当默认数据处于20条之外,在回显的时候会显示异常
          解决方法:遍历所有数据,将对应回显的那一条数据放在第一条即可 */
        this.selectArr = JSON.parse(JSON.stringify(this.selectData));
        if (!this.multiple) {
          // 1.单选
          let obj = {};
          for (let i = 0; i < this.selectArr.length; i++) {
            const element = this.selectArr[i];
            if (
              element[this.selectOption.value]?.toLowerCase() ===
              this.value?.toLowerCase()
            ) {
              obj = element;
              this.selectArr.splice(i, 1);
              break;
            }
          }
          this.selectArr.unshift(obj);
        } else {
          // 2.多选
          const selectedArr = [];
          for (let i = 0; i < this.selectArr.length; i++) {
            const element = this.selectArr[i];
            for (let j = 0; j < this.value.length; j++) {
              const item = this.value[j];
              if (
                element[this.selectOption.value]?.toLowerCase() ===
                item?.toLowerCase()
              ) {
                selectedArr.push(element);
                this.selectArr.splice(i, 1);
                break;
              }
            }
          }
          this.selectArr.unshift(...selectedArr);
        }
      }
    },
    // 搜索
    filterMethod(query) {
      if (query !== '' && !this.remote) {
        this.$refs.virtualListRef.scrollToIndex(0); // 滚动到顶部
        this.offsetTop = 0
        setTimeout(() => {
          this.selectArr = this.selectData.filter((item) => {
            return this.selectOption.isRight
              ? item[this.selectOption.label]
                .toLowerCase()
                .indexOf(query.toLowerCase()) > -1 ||
                item[this.selectOption.value]
                  .toLowerCase()
                  .indexOf(query.toLowerCase()) > -1 : item[this.selectOption.label]
                .toLowerCase()
                .indexOf(query.toLowerCase()) > -1;
          });
        }, 100);
      } else {
        this.init();
      }
    },
    // select显示和隐藏事件
    visibleChange(bool) {
      this.visible = bool
      if (!bool) {
        // this.$refs.virtualListRef.reset();
        // this.init();
      } else {
        this.init();
        this.$nextTick(() => {
          console.log(1111, this.$refs.virtualListRef)
          if (this.$refs.virtualListRef) {
            if (this.offsetTop) {
              console.log(2222, this.offsetTop)
              // setTimeout(() => {
                this.$refs.virtualListRef.scrollToOffset(this.offsetTop);
              // }, 1000)
            } else if (this.scrollIndex) {
              console.log(3333, this.scrollIndex)
              this.$refs.virtualListRef.scrollToIndex(this.scrollIndex - 1);
            }
          }
        })
      }
    }

  }
}
</script>
<style lang="scss" scope>
.virtualselect {
  // 设置最大高度
  &-list {
    max-height: 245px;
    overflow-y: auto;
  }
  // .el-scrollbar .el-scrollbar__bar.is-vertical {
  //   width: 0 !important;
  // }
}
.virtualselect-list{
  position: relative;
  z-index: 2;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: transparent;
    cursor: pointer;
    margin-right: 5px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(144,147,153,.3) !important;
    border-radius: 3px !important;
  }
  &::-webkit-scrollbar-thumb:hover{
    background-color: rgba(144,147,153,.5) !important;
  }
  &::-webkit-scrollbar-track {
    background-color: transparent !important;
    border-radius: 3px !important;
    -webkit-box-shadow: none !important;
  }
}

</style>
