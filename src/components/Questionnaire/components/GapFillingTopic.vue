<template>
  <div class="gap-filling-topic">
    <div class="delete-icon pointer" @click="deleteTopic">
      <i class="el-icon-delete-solid"></i>
    </div>
    <el-form :model="topic" ref="topic">
      <div class="gap-filling-topic-title">
        <div class="gap-filling-topic-title-left">
          <span class="m-b-5">{{ index + 1 }}.</span>
            <el-form-item prop="caption" :rules="[{ required: true, message: '请输入标题', trigger: ['blur', 'change'] }, { pattern: /^$|^.*\S.*$/, message: '请输入标题', trigger: ['blur', 'change'] }]">
              <el-input
                v-model="topic.caption"
                type="textarea"
                class="m-l-10 w-350"
                autosize
                placeholder="请输入标题"
                resize="vertical"
                :disabled="disabled"
                maxlength="30"
                @keydown.enter.native.prevent
                @input="filterNewlines">
              </el-input>
            </el-form-item>
        </div>
      </div>
      <div class="gap-filling-topic-content">
        <el-input type="textarea" class="m-l-10 w-350" :autosize="{ minRows: 4}" placeholder="请输入内容" resize="vertical" :disabled="disabled" maxlength="30"></el-input>
      </div>
      <div class="gap-filling-topic-footer">
        <el-checkbox v-model="topic.required" :disabled="disabled">必填</el-checkbox>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  props: ['topic', 'index', 'disabled'],
  data() {
    return {}
  },
  methods: {
    filterNewlines(value) {
      this.inputText = value.replace(/\n/g, ''); // 正则替换所有换行符
    },
    deleteTopic() {
      this.$emit('delete', this.index)
    },
    handleValidate() {
      return new Promise((resolve) => {
        this.$refs.topic.validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.gap-filling-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  .delete-icon {
    position: absolute;
    top: 10px;
    right: 10px;
  }
  &-title {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    &-left {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
    &-right {
      margin-left: 10px;
      color: #E7ECF2;
    }
  }
  &-content {
    margin-left: 14px;
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
  }
  &-footer {
    position: absolute;
    right: 10px;
    bottom: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
