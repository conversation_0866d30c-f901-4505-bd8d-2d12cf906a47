<template>
  <div class="choice-topic">
    <div class="delete-icon pointer" @click="deleteTopic">
      <i class="el-icon-delete-solid"></i>
    </div>
    <el-form :model="topic" ref="topic">
      <div class="choice-topic-title">
        <div class="choice-topic-title-left">
          <span class="m-b-5">{{ index + 1 }}.</span>
            <el-form-item prop="caption" :rules="[{ required: true, message: '请输入标题', trigger: ['blur', 'change'] }, { pattern: /^$|^.*\S.*$/, message: '请输入标题', trigger: ['blur', 'change'] }]">
              <el-input
                v-model="topic.caption"
                type="textarea"
                class="m-l-10 w-350"
                autosize
                placeholder="请输入标题"
                resize="vertical"
                :disabled="disabled"
                maxlength="30"
                @keydown.enter.native.prevent
                @input="filterNewlines">
              </el-input>
            </el-form-item>
        </div>
      </div>
      <div class="choice-topic-content">
        <div v-if="topic.question_type === 6" class="upload">
          <div style="color: #FF9B45; font-size: 32px;">
            <i class="el-icon-upload"></i>
          </div>
          <div class="upload-text">
            <div style="line-height: 14px;">选择文件</div>
            <div style="line-height: 14px;">[不超过10M]</div>
          </div>
        </div>
        <div v-else class="upload">
          <div style="color: #FF9B45; font-size: 32px;">
            <i class="el-icon-circle-plus"></i>
          </div>
          <div class="upload-text">
            <div style="line-height: 14px;">上传图片</div>
            <div style="line-height: 14px;">[不超过5M]</div>
          </div>
        </div>
      </div>
      <div class="choice-topic-footer">
        <div class="m-r-10">最多可上传</div>
        <el-select class="w-60 m-r-10" size="mini" v-model="topic.upload_max_num">
          <el-option
            v-for="item in 10"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
        <el-checkbox v-model="topic.required" :disabled="disabled">必填</el-checkbox>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  props: ['topic', 'index', 'disabled'],
  data() {
    return {
      selectList: [
        {
          label: '至少选1项',
          value: 1
        }
      ]
    }
  },
  computed: {
    computedPlaceholder() {
      return d => {
        if (d.type !== 'default') {
          return '其他'
        } else {
          return '请输入选项内容'
        }
      }
    }
  },
  methods: {
    filterNewlines(value) {
      this.inputText = value.replace(/\n/g, ''); // 正则替换所有换行符
    },
    deleteTopic() {
      this.$emit('delete', this.index)
    },
    handleValidate() {
      return new Promise((resolve) => {
        this.$refs.topic.validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.choice-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  .delete-icon {
    position: absolute;
    top: 10px;
    right: 10px;
  }
  &-title {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    &-left {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
    &-right {
      margin-left: 10px;
      color: #E7ECF2;
    }
  }
  &-content {
    margin-left: 23px;
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    .upload {
      width: 120px;
      height: 120px;
      border: 1px solid #E7ECF2;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;
      position: relative;
      &-text {
        font-size: 12px;
        position: absolute;
        bottom: 5px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
    }
  }
  &-footer {
    position: absolute;
    right: 10px;
    bottom: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
