<template>
  <div class="evaluate-topic">
    <div class="delete-icon pointer" @click="deleteTopic">
      <i class="el-icon-delete-solid"></i>
    </div>
    <el-form :model="topic" ref="topic">
      <div class="evaluate-topic-title">
        <div class="evaluate-topic-title-left">
          <span class="m-b-5">{{ index + 1 }}.</span>
          <el-form-item prop="caption" :rules="[{ required: true, message: '请输入标题', trigger: ['blur', 'change'] }, { pattern: /^$|^.*\S.*$/, message: '请输入标题', trigger: ['blur', 'change'] }]">
            <el-input
              v-model="topic.caption"
              type="textarea"
              class="m-l-10 w-350"
              autosize
              placeholder="请输入标题"
              resize="vertical"
              :disabled="disabled"
              maxlength="30"
              @keydown.enter.native.prevent
              @input="filterNewlines">
            </el-input>
          </el-form-item>
        </div>
      </div>
      <div class="evaluate-topic-content">
        <div class="evaluate-topic-content-item" v-for="(item, index) in topic.choices" :key="index">
          <div class="evaluate-topic-content-item-input">
            <el-form-item :prop="`choices.`+ index + `.description`" :rules="[{ required: true, message: '请输入选项内容', trigger: ['blur', 'change'] }, { pattern: /^$|^.*\S.*$/, message: '请输入选项内容', trigger: ['blur', 'change'] }]">
              <el-input
                v-model="item.description"
                size="mini"
                class="w-110 m-r-10"
                :placeholder="`选项${index + 1}`"
                type="textarea"
                autosize
                resize="vertical"
                :disabled="disabled"
                maxlength="15"
                @keydown.enter.native.prevent
                @input="filterNewlines">
              </el-input>
            </el-form-item>
            <el-rate
              class="m-b-15"
              v-model="score"
              disabled
              text-color="#FF9B45">
            </el-rate>
          </div>
          <div class="evaluate-topic-content-item-icon m-t-5">
            <div class="icon-style pointer" @click="addSelect(index)">
              <i class="el-icon-circle-plus"></i>
            </div>
            <div class="icon-style pointer" v-if="topic.choices.length > 1" @click="deleteSelect(index)">
              <i class="el-icon-remove"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="evaluate-topic-footer">
        <el-checkbox v-model="topic.required" :disabled="disabled">必填</el-checkbox>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  props: ['topic', 'index', 'disabled'],
  data() {
    return {
      score: 1
    }
  },
  methods: {
    filterNewlines(value) {
      this.inputText = value.replace(/\n/g, ''); // 正则替换所有换行符
    },
    deleteTopic() {
      this.$emit('delete', this.index)
    },
    addSelect(index) {
      if (this.disabled) {
        return
      }
      this.topic.choices.splice(index + 1, 0, {
        description: '',
        order_in_list: index + 1
      })
      this.$emit('update: topic', this.topic)
    },
    deleteSelect(index) {
      if (this.disabled) {
        return
      }
      this.topic.choices.splice(index, 1)
      this.$emit('update: topic', this.topic)
    },
    handleValidate() {
      return new Promise((resolve) => {
        this.$refs.topic.validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.evaluate-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  .delete-icon {
    position: absolute;
    top: 10px;
    right: 10px;
  }
  &-title {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    &-left {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
    &-right {
      margin-left: 10px;
      color: #E7ECF2;
    }
  }
  &-content {
    margin-left: 23px;
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    &-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      &-input {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-right: 10px;
        ::v-deep .el-form-item__content {
          line-height: 30px;
        }
      }
      &-icon {
        width: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .icon-style {
          font-size: 22px;
        }
      }
    }
  }
  &-footer {
    position: absolute;
    right: 10px;
    bottom: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  ::v-deep .el-rate__icon {
    font-size: 32px;
    margin-right: 10px;
  }
}
</style>
