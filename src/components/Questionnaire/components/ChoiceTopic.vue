<template>
  <div class="choice-topic">
    <div class="delete-icon pointer" @click="deleteTopic">
      <i class="el-icon-delete-solid"></i>
    </div>
    <el-form :model="topic" ref="topic">
      <div class="choice-topic-title">
        <div class="choice-topic-title-left">
          <span class="m-b-5">{{ index + 1 }}.</span>
          <el-form-item prop="caption" :rules="[{ required: true, message: '请输入标题', trigger: ['blur', 'change'] }, { pattern: /^$|^.*\S.*$/, message: '请输入标题', trigger: ['blur', 'change'] }]">
            <el-input
              v-model="topic.caption"
              type="textarea"
              class="m-l-10 w-350"
              autosize
              placeholder="请输入标题"
              resize="vertical"
              :disabled="disabled"
              maxlength="30"
              @keydown.enter.native.prevent
              @input="filterNewlines">
            </el-input>
          </el-form-item>
        </div>
      </div>
      <div class="choice-topic-content">
        <div class="choice-topic-content-item" v-for="(item, index) in topic.choices" :key="index">
          <div :class="[topic.question_type === 0 ? 'singleStyle' : 'multipleStyle', 'm-r-5', 'm-t-15']"></div>
          <div class="choice-topic-content-item-input">
            <el-form-item :prop="`choices.`+ index + `.description`" :rules="[{ required: true, message: '请输入选项内容', trigger: ['blur', 'change'] }, { pattern: /^$|^.*\S.*$/, message: '请输入选项内容', trigger: ['blur', 'change'] }]">
              <el-input
                v-model="item.description"
                size="mini"
                class="w-350"
                :placeholder="computedPlaceholder(item)"
                type="textarea"
                autosize
                resize="vertical"
                :disabled="disabled"
                maxlength="15"
                @keydown.enter.native.prevent
                @input="filterNewlines">
              </el-input>
            </el-form-item>
            <el-form-item :prop="`choices.`+ index + `.other_content`" v-if="item.type !== 'default'" :rules="[{ required: true, message: '请输入选项内容', trigger: ['blur', 'change'] }, { pattern: /^$|^.*\S.*$/, message: '请输入填空内容', trigger: ['blur', 'change'] }]">
              <el-input
                size="mini"
                class="w-350
                m-t-5"
                v-model="item.other_content"
                placeholder="请输入内容"
                type="textarea"
                autosize
                resize="vertical"
                :disabled="disabled"
                maxlength="30"
                @keydown.enter.native.prevent
                @input="filterNewlines">
              </el-input>
            </el-form-item>
          </div>
          <div class="choice-topic-content-item-icon m-t-5">
            <div class="icon-style pointer" @click="addSelect(index)">
              <i class="el-icon-circle-plus"></i>
            </div>
            <div class="icon-style pointer" v-if="topic.choices.length > 1" @click="deleteSelect(index)">
              <i class="el-icon-remove"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="choice-topic-footer">
        <el-button type="text" size="small" class="ps-text m-r-10" @click="changeItemType">添加填空选项</el-button>
        <el-select class="w-110 m-r-10" size="mini" v-model="topic.least_choose_count" placeholder="至少选" v-if="topic.question_type === 1" :disabled="disabled">
          <el-option
            v-for="(item, index) in computedSelectList(topic.choices)"
            :key="index"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <el-checkbox v-model="topic.required" :disabled="disabled">必填</el-checkbox>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  props: ['topic', 'index', 'disabled'],
  data() {
    return {
      rules: [{ required: true, message: '请输入选项内容', trigger: ['blur', 'change'] }, { pattern: /^$|^.*\S.*$/, message: '请输入选项内容', trigger: ['blur', 'change'] }]
    }
  },
  computed: {
    computedPlaceholder() {
      return d => {
        if (d.type !== 'default') {
          return '其他'
        } else {
          return '请输入选项内容'
        }
      }
    },
    computedSelectList() {
      return d => {
        if (d.length === 0) {
          return [
            {
              label: '至少选1项',
              value: 1
            }
          ]
        } else {
          return d.map((item, index) => {
            return {
              label: `至少选${index + 1}项`,
              value: index + 1
            }
          })
        }
      }
    }
  },
  methods: {
    filterNewlines(value) {
      this.inputText = value.replace(/\n/g, ''); // 正则替换所有换行符
    },
    addSelect(index) {
      if (this.disabled) {
        return
      }
      this.topic.choices.splice(index + 1, 0, {
        type: 'default',
        description: ``,
        other_content: '',
        multi_choice: this.topic.question_type === 1,
        order_in_list: index + 1
      })
      if (this.topic.question_type === 1) {
        this.selectList.push({
          label: `至少选${this.selectList.length + 1}项`,
          value: this.selectList.length + 1
        })
      }
      this.$emit('update: topic', this.topic)
    },
    deleteSelect(index) {
      if (this.disabled) {
        return
      }
      this.topic.choices.splice(index, 1)
      if (this.topic.question_type === 1) {
        if (this.selectList.length > 1) {
          this.selectList.pop()
        }
      }
      this.$emit('update: topic', this.topic)
    },
    changeItemType() {
      if (this.disabled) {
        return
      }
      // this.topic.choices[this.topic.choices.length - 1].type = 'other'
      this.topic.choices.splice(this.topic.choices.length, 0, {
        type: 'other',
        description: '其他',
        other_content: '请输入内容',
        multi_choice: this.topic.question_type === 1,
        order_in_list: this.topic.choices.length
      })
      if (this.topic.question_type === 1) {
        this.selectList.push({
          label: `至少选${this.selectList.length + 1}项`,
          value: this.selectList.length + 1
        })
      }
      this.$emit('update: topic', this.topic)
    },
    deleteTopic() {
      this.$emit('delete', this.index)
    },
    handleValidate() {
      return new Promise((resolve) => {
        this.$refs.topic.validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.choice-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  .delete-icon {
    position: absolute;
    top: 10px;
    right: 10px;
  }
  &-title {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    &-left {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
    &-right {
      margin-left: 10px;
      color: #E7ECF2;
    }
  }
  &-content {
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    &-item {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      .singleStyle {
        width: 18px;
        height: 18px;
        border: 1px solid #E7ECF2;
        border-radius: 50%;
      }
      .multipleStyle {
        width: 18px;
        height: 18px;
        border: 1px solid #E7ECF2;
      }
      &-input {
        display: flex;
        flex-direction: column;
        margin: 10px 0px;
        margin-right: 10px;
        ::v-deep .el-form-item__content {
          line-height: 30px;
        }
      }
      &-icon {
        width: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .icon-style {
          font-size: 22px;
        }
      }
    }
  }
  &-footer {
    position: absolute;
    right: 10px;
    bottom: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
