<template>
  <div class="mark-topic">
    <div class="delete-icon pointer" @click="deleteTopic">
      <i class="el-icon-delete-solid"></i>
    </div>
    <el-form :model="topic" ref="topic">
      <div class="mark-topic-title">
        <div class="mark-topic-title-left">
          <span class="m-b-5">{{ index + 1 }}.</span>
            <el-form-item prop="caption" :rules="[{ required: true, message: '请输入标题', trigger: ['blur', 'change'] }, { pattern: /^$|^.*\S.*$/, message: '请输入标题', trigger: ['blur', 'change'] }]">
              <el-input
                v-model="topic.caption"
                type="textarea"
                class="m-l-10 w-350"
                autosize
                placeholder="请输入标题"
                resize="vertical"
                :disabled="disabled"
                maxlength="30"
                @keydown.enter.native.prevent
                @input="filterNewlines">
              </el-input>
            </el-form-item>
        </div>
      </div>
      <div class="mark-topic-content">
        <div class="mark-topic-content-item">
          <div class="mark-topic-content-item-top">
            <div class="point">1</div>
            <div class="point">{{ topic.top_score }}</div>
          </div>
          <div class="mark-topic-content-item-bottom">
            <div v-for="item in topic.top_score" :key="item" class="mark">
              {{ item }}
            </div>
          </div>
        </div>
      </div>
      <div class="mark-topic-footer">
        <div class="m-r-10">最高分</div>
        <el-select class="w-60 m-r-10" size="mini" v-model="topic.top_score">
          <el-option
            v-for="item in [2, 3, 4, 5, 6, 7, 8, 9, 10]"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
        <el-checkbox v-model="topic.required" :disabled="disabled">必填</el-checkbox>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  props: ['topic', 'index', 'disabled'],
  data() {
    return {}
  },
  methods: {
    filterNewlines(value) {
      this.inputText = value.replace(/\n/g, ''); // 正则替换所有换行符
    },
    deleteTopic() {
      this.$emit('delete', this.index)
    },
    handleValidate() {
      return new Promise((resolve) => {
        this.$refs.topic.validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mark-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  .delete-icon {
    position: absolute;
    top: 10px;
    right: 10px;
  }
  &-title {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    &-left {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
    &-right {
      margin-left: 10px;
      color: #E7ECF2;
    }
  }
  &-content {
    margin-left: 23px;
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    &-item {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      border: 1px solid #E7ECF2;
      border-radius: 6px;
      padding: 5px;
      &-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 5px;
        border-bottom: 1px solid #E7ECF2;
        .point {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      &-bottom {
        margin-top: 5px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .mark {
          width: 32px;
          height: 32px;
          margin: 0 5px;
          border: 1px solid #E7ECF2;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
  &-footer {
    position: absolute;
    right: 10px;
    bottom: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
