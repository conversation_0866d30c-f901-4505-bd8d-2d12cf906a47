<template>
  <div class="questionnaire">
    <!-- 左边选择 -->
    <div class="questionnaire-left">
      <div class="questionnaire-left-title">
        <span>题型</span>
      </div>
      <div class="questionnaire-left-content">
        <div class="questionnaire-left-content-item pointer" v-for="(item, index) in questionType" :key="index" @click="selectThisTopic(item)">
          <span>{{ item.name }}</span>
        </div>
      </div>
    </div>
    <!-- 右边预览 -->
    <div class="questionnaire-right">
      <draggable v-model="localQuestionsList" :options="{ animation: 300 }" v-if="localQuestionsList.length" @change="updateQuestionOrder">
        <transition-group>
          <div class="questionnaire-right-item" v-for="(item, index) in localQuestionsList" :key="index">
            <choice-topic :ref="`choiceTopicForm${index}`" v-if="item.question_type === 0 || item.question_type === 1" :topic.sync="item" :index.sync="index" @delete="deleteTopic" :disabled="disabled"></choice-topic>
            <evaluate-topic :ref="`evaluateTopicForm${index}`" v-if="item.question_type === 3" :topic.sync="item" :index.sync="index" @delete="deleteTopic" :disabled="disabled"></evaluate-topic>
            <file-upload-topic :ref="`fileUploadTopicForm${index}`" v-if="item.question_type === 5 || item.question_type === 6" :topic.sync="item" :index.sync="index" @delete="deleteTopic" :disabled="disabled"></file-upload-topic>
            <gap-filling-topic :ref="`gapFillingTopicForm${index}`" v-if="item.question_type === 4" :topic.sync="item" :index.sync="index" @delete="deleteTopic" :disabled="disabled"></gap-filling-topic>
            <mark-topic :ref="`markTopicForm${index}`" v-if="item.question_type === 2" :topic.sync="item" :index.sync="index" @delete="deleteTopic" :disabled="disabled"></mark-topic>
          </div>
        </transition-group>
      </draggable>
      <div v-else>
        <el-empty description="请点击左侧新增题型"></el-empty>
      </div>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import ChoiceTopic from './components/ChoiceTopic.vue'
import EvaluateTopic from './components/EvaluateTopic.vue'
import FileUploadTopic from './components/FileUploadTopic.vue'
import GapFillingTopic from './components/GapFillingTopic.vue'
import MarkTopic from './components/MarkTopic.vue'
import _ from 'lodash'

export default {
  name: 'Questionnaire',
  components: {
    draggable,
    ChoiceTopic,
    EvaluateTopic,
    FileUploadTopic,
    GapFillingTopic,
    MarkTopic
  },
  props: ['drawerShow', 'questionsList', 'disabled'],
  data() {
    return {
      questionType: [
        {
          type: 0,
          name: '单选题'
        },
        {
          type: 1,
          name: '多选题'
        },
        {
          type: 2,
          name: '分数题'
        },
        {
          type: 3,
          name: '评价题'
        },
        {
          type: 4,
          name: '填空题'
        },
        {
          type: 5,
          name: '图片上传'
        },
        {
          type: 6,
          name: '文件上传'
        }
      ],
      result: [], // 接一下每个问题类型的返回结果
      localQuestionsList: [] // 添加本地数据属性
    }
  },
  watch: {
    questionsList: {
      handler(newVal, oldVal) {
        if (!_.isEqual(newVal, oldVal)) {
          this.localQuestionsList = [...newVal]
        }
      },
      immediate: true
    },
    localQuestionsList: {
      handler(newVal) {
        this.$emit('update:questionsList', [...newVal])
      },
      deep: true
    }
  },
  methods: {
    // 更新问题序号
    updateQuestionOrder() {
      this.localQuestionsList.forEach((item, index) => {
        item.order_in_list = index + 1
      })
    },
    selectThisTopic(item) {
      if (this.disabled) {
        return
      }
      let obj = {
        question_type: item.type,
        order_in_list: this.localQuestionsList.length + 1
      }
      switch (item.type) {
        case 0: {
          Object.assign(obj, {
            caption: '',
            choices: [
              {
                type: 'default',
                description: ``,
                other_content: '',
                multi_choice: false,
                order_in_list: 0
              }
            ],
            required: true
          })
          break
        }
        case 1: {
          Object.assign(obj, {
            caption: '',
            choices: [
              {
                type: 'default',
                description: '',
                other_content: '',
                multi_choice: true,
                order_in_list: 0
              }
            ],
            least_choose_count: 1,
            required: true
          })
          break
        }
        case 2: {
          Object.assign(obj, {
            caption: '',
            top_score: 10,
            required: true
          })
          break
        }
        case 3: {
          Object.assign(obj, {
            caption: '',
            choices: [
              {
                description: '',
                order_in_list: 0
              }
            ],
            top_score: 5,
            required: true
          })
          break
        }
        case 4: {
          Object.assign(obj, {
            caption: '',
            required: true
          })
          break
        }
        case 5: {
          Object.assign(obj, {
            caption: '图片上传',
            upload_max_num: 1,
            required: true
          })
          break
        }
        case 6: {
          Object.assign(obj, {
            caption: '文件上传',
            upload_max_num: 1,
            required: true
          })
          break
        }
      }
      if (this.localQuestionsList.length >= 50) {
        return this.$message.error('最多添加50道题')
      } else {
        this.localQuestionsList.push(obj)
      }
    },
    deleteTopic(index) {
      if (this.disabled) {
        return
      }
      this.localQuestionsList.splice(index, 1)
    },
    async checkQuestionnaire() {
      this.result = []
      await this.localQuestionsList.forEach(async (item, index) => {
        let flag = false
        switch (item.question_type) {
          case 0:
            flag = await this.$refs[`choiceTopicForm${index}`][0].handleValidate()
            break
          case 1:
            flag = await this.$refs[`choiceTopicForm${index}`][0].handleValidate()
            break
          case 2:
            flag = await this.$refs[`markTopicForm${index}`][0].handleValidate()
            break
          case 3:
            flag = await this.$refs[`evaluateTopicForm${index}`][0].handleValidate()
            break
          case 4:
            flag = await this.$refs[`gapFillingTopicForm${index}`][0].handleValidate()
            break
          case 5:
            flag = await this.$refs[`fileUploadTopicForm${index}`][0].handleValidate()
            break
          case 6:
            flag = await this.$refs[`fileUploadTopicForm${index}`][0].handleValidate()
            break
        }
        this.result.push(flag)
      })
      return this.result
    }
  }
}
</script>

<style lang="scss" scoped>
.questionnaire {
  border-top: 1px solid #E7ECF2;
  display: flex;
  &-left {
    border-right: 1px solid #E7ECF2;
    padding: 15px 15px 15px 0px;
    width: 20%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    &-title {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #F2F2F2;
      border-radius: 6px;
    }
    &-content {
      width: 100%;
      padding: 10px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      &-item {
        width: 100%;
        padding: 10px;
        margin: 5px;
        display: flex;
        align-items: stretch;
        justify-content: center;
        border-radius: 16px;
        font-size: 14px;
        border: 1px solid #F2F2F2;
      }
      &-item:hover {
        background-color: #F2F2F2;
      }
    }
  }
  &-right {
    padding: 15px;
    width: 80%;
    display: flex;
    flex-direction: column;
    &-item {
      border: 1px solid #E7ECF2;
      border-radius: 6px;
      margin: 10px;
    }
  }
}
</style>
