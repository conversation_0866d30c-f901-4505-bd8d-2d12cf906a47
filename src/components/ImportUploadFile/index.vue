<template>
  <div>
    <el-form class="import-face-form-wrapper" label-width="140px">
      <slot name="perv"></slot>
      <slot>
        <el-form-item label="导入模板">
          <el-link type="primary" :href="link">点击下载</el-link>
        </el-form-item>
        <el-form-item :label="uploadFormItemLabel">
          <el-upload
            drag
            :data="uploadParams"
            :limit="limit"
            :on-success="getSuccessUploadRes"
            :before-upload="beforeUpload"
            :action="actionUrl"
            :on-remove="remove"
            :headers="headersOpts"
          >
            <div class="">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
            </div>
            <div class="el-upload__tip" slot="tip">只能上传{{fileType}}文件</div>
          </el-upload>
        </el-form-item>
      </slot>
    </el-form>
  </div>
</template>

<script>
import { getToken } from '@/utils'
export default {
  props: {
    link: String,
    fileType: {
      type: String,
      default: 'excel'
    },
    uploadParams: {
      type: Object,
      default: () => {
        return {}
      }
    },
    uploadFormItemLabel: String
  },
  data() {
    return {
      limit: 1,
      actionUrl: '/api/background/file/upload',
      uploadUrl: '',
      headersOpts: {
        TOKEN: getToken()
      }
    }
  },
  created() {
    // this.getUploadToken()
  },
  mounted() {},
  methods: {
    async getUploadToken() {
      const res = await this.$apis.getUploadToken({
        prefix: this.fileType
      })
      if (res.code === 0) {
        this.actionUrl = res.data.host
        this.uploadParams = {
          key: res.data.prefix + new Date().getTime() + Math.floor(Math.random() * 150),
          prefix: res.data.prefix,
          policy: res.data.policy,
          OSSAccessKeyId: res.data.accessid,
          signature: res.data.signature,
          callback: res.data.callback,
          success_action_status: '200'
        }
        console.log('uploadParams', this.uploadParams)
      } else {
        this.$message.error(res.msg)
      }
    },
    beforeUpload(file) {
      if (this.getSuffix(file.name) !== ('.' + this.fileType)) {
        this.$message.error('请上传后缀名为.' + this.fileType + '的文件')
        return false
      }
    },
    getSuffix(filename) {
      let pos = filename.lastIndexOf('.')
      let suffix = ''
      if (pos !== -1) {
        suffix = filename.substring(pos)
      }
      return suffix
    },
    remove() {
      this.uploadUrl = ''
    },
    getSuccessUploadRes(res) {
      if (res.code === 0) {
        this.uploadUrl = res.data.public_url
        this.$emit('publicUrl', res.data.public_url)
      }
    }
  }
}
</script>

<style lang='scss' scoped>
</style>
