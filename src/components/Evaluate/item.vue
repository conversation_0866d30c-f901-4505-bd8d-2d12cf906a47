<template>
  <div class="evaluate-item m-b-30">
    <div class="evaluate-top flex-between">
      <div class="evalute-top-l">
        <div class="m-b-10">
          <span class="evalute-label blod m-r-40">类型：评价</span>
          <span class="evalute-label m-r-40">组织:{{ col.organization_name }}</span>
          <span class="evalute-label m-r-40">评价时间：{{ col.create_time }}</span>
          <span class="evalute-label m-r-40">状态：{{ col.is_reply ? '已回复' : '未回复' }}</span>
          <span class="evalute-label m-r-40" style="color: red;" v-if="col.is_anonymous">匿名评价</span>
        </div>
        <div>
          <span class="evalute-label m-r-40">总订单号：{{ col.unified_trade_no }}</span>
          <span class="evalute-label m-r-40">订单号：{{ col.trade_no }}</span>
          <span v-if="!col.is_anonymous || type==='Super'">
            <span class="evalute-label m-r-40">手机号：{{ col.phone }}</span>
            <span class="evalute-label m-r-40">用户名：{{ col.name }}</span>
            <span class="evalute-label m-r-40">用户编号: {{ col.person_no }}</span>
          </span>
        </div>
      </div>
      <div class="evalute-top-l m-l-10" v-if="type==='Super'">
        <el-button v-if="col.is_visible" type="danger" plain @click="deleteHandle(col)">删除</el-button>
        <span v-else style="color: red;">已删除</span>
      </div>
      <div class="evalute-top-l m-l-10" v-else>
        <el-button v-if="!col.is_reply" @click="replyHandle(col)" v-permission="['background_operation_management.order_evaluation.reply_order']">商家回复</el-button>
      </div>
    </div>
    <el-divider class="line"></el-divider>
    <div class="evalute-item-content">
      <div class="label evalute-text blod m-b-10">整体评价：</div>
      <div class="rate-box m-l-10">
        <div v-for="(rate, k) in col.evaluation_score" :key="'rate1'+k" class="rate-item m-r-60 m-b-10">
          <span class="rate-label">{{ rate.field_name }}</span>
          <el-rate class="rate" v-model="rate.score" disabled></el-rate>
        </div>
      </div>
      <div class="evalute m-t-10">
        <div class="label evalute-text" style="color:#999;">评价内容：</div>
        <div class="evalute-img-box m-t-15">
          <el-image v-for="(img, i) in col.evaluation_img_list" :key="img+i" :src="img" class="evalute-img m-r-20" :preview-src-list="col.evaluation_img_list">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </div>
        <div class="evalute-text m-t-15">
          {{ col.evaluation_content }}
        </div>
      </div>
      <div v-if="false && col.food_evaluation_score && col.food_evaluation_score.length">
        <div class="label evalute-text m-t-20 m-b-10 blod">菜品评价：</div>
        <div class="rate-box m-l-20" v-for="(food, index) in col.food_evaluation_score" :key="'rate2' + index">
          <div class="m-b-5">{{ food.food_name }}</div>
          <div v-for="(rate, k) in food.evaluation_score" :key="'rate' + k" class="rate-item m-r-60 m-b-10">
            <span class="rate-label">{{ rate.field_name }}</span>
            <el-rate class="rate" v-model="rate.score" disabled></el-rate>
          </div>
        </div>
      </div>
      <el-divider class="line"></el-divider>
      <div v-if="col.is_reply">
        <div class="label evalute-text" style="color:#999;">商家回复（{{col.account_alias}}）：</div>
        <div class="evalute-text  m-t-15">
          {{ col.reply_content }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Evaluate',
  components: {

  },
  props: {
    col: Object,
    deleteEvaluate: Function,
    type: String
  },
  data() {
    return {
    }
  },
  computed: {

  },
  watch: {

  },
  created() {

  },
  mounted() {

  },
  methods: {
    deleteHandle(col) {
      this.$emit('deleteevaluate', col)
    },
    replyHandle(col) {
      this.$emit('reply', col)
    }
  }
};
</script>

<style scoped lang="scss">
.evaluate-item{
  font-size: 14px;
  padding: 20px;
  border-radius: 12px;
  background-color: #ffffff;
  .evaluate-top{

  }
  .evalute-label{}
  .blod{
    font-weight: 600;
    color: #000000;
  }
  .line{
    margin: 15px 0;
  }
  .evalute-item-content{
    .rate-item{
      display: inline-block;
      .rate-label{
        vertical-align: middle;
      }
      .rate{
        display: inline-block;
        ::v-deep .el-rate__icon{
          font-size: 26px;
        }
      }
    }
    .evalute-img{
      width: 60px;
      height: 60px;
      .el-icon-picture-outline{
        font-size: 60px;
        opacity: .5;
      }
    }
  }
  .evalute-text{
    word-break: break-all;
  }
}
</style>
