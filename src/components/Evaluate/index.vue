<template>
  <div v-loading="isLoading">
    <div class="container-wrapper evaluate-wrapper">
      <refresh-tool v-if="refreshTool" @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>
      <!-- 内容 start -->
      <div class="evaluate-content-wrapper m-t-30">
        <div class="evaluate-content" v-if="evaluateData && evaluateData.length">
          <evalute-item v-for="item in evaluateData" :key="item.id" :col="item" :type="type" @deleteevaluate="deleteHandle" @reply="initReply" />
        </div>
        <div class="evaluate-content empty" v-else>
          <img class="empty-img" src="@/assets/img/table-no-data.png" alt="empty">
          <p class="empty-text">暂无数据</p>
        </div>
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
      <!-- 内容 end -->
    </div>
    <el-dialog
      title="商家回复"
      :visible.sync="showDialog"
      width="500px"
      custom-class="ps-dialog"
    >
      <div>
        <el-input
          type="textarea"
          placeholder="回复内容"
          rows="5"
          maxlength="150"
          show-word-limit
          v-model="replyContent">
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="showDialog = false">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="replyHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { to } from '@/utils'
import EvaluteItem from "./item"

export default {
  name: 'Evaluate',
  components: { EvaluteItem },
  props: {
    refreshTool: {
      type: Boolean,
      default: true
    },
    autoSearch: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: 'Merchant'
    },
    formSetting: Object,
    apiUrl: String
  },
  data() {
    return {
      isLoading: false,
      evaluateData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: {},
      replyContent: '', // 回复内容
      showDialog: false,
      replyData: null,
      isReplyLoading: false
    }
  },
  mounted() {
    this.searchFormSetting = this.formSetting
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getEvaluateList()
    },

    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.evaluateData = []
      this.currentPage = 1
      this.getEvaluateList()
    },

    async searchHandle(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getEvaluateList()
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 请求列表数据
    async getEvaluateList() {
      const params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      this.isLoading = true
      const res = await this.$apis[this.apiUrl](params)
      this.isLoading = false
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.evaluateData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getEvaluateList()
    },
    deleteHandle(col) {
      this.$confirm(`是否删除当前评价？`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            // instance.cancelButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundOperationManagementAdminOrderEvaluationDeletePost({
                ids: [col.id]
              })
            )
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.searchHandle()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    initReply(col) {
      this.replyData = col
      this.replyContent = ''
      this.showDialog = true
    },
    async replyHandle() {
      if (this.isReplyLoading) return
      this.isReplyLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOperationManagementOrderEvaluationReplyEvaluationPost({
          id: this.replyData.id,
          reply_content: this.replyContent
        })
      )
      this.isReplyLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialog = false
        this.replyData = null
        this.replyContent = ''
        this.$message.success(res.msg)
        this.searchHandle()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.evaluate-wrapper{
  .table-wrapper{
    background-color: transparent !important;
  }
  .evaluate-content{
    &.empty{
      padding: 40px;
      border-radius: 12px;
      background-color: #ffffff;
      text-align: center;
      .empty-img{
        display: inline-block;
        width: 127px;
        height: 99px;
        vertical-align: middle;
      }
      .empty-text{
        color: #b2b2b2;
      }
    }
  }
}
</style>
