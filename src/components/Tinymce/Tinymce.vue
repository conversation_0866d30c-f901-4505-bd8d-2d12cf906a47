<template>
  <div>
    <vue-tinymce
      :key="tinymceFlag"
      v-model="content"
      :setting="setting"
      @change="change"
      :setup="setup"
    />
  </div>
</template>
<script>
export default {
  name: 'tinymce',
  props: {
    value: {
      type: String,
      default: ""
    },
    disabled: {
      type: Boolean,
      default: false
    },
    itemkey: {
      // 多个富文本用于区分
      type: String,
      default: ''
    },
    custom: {
      // 添加自定义菜单按钮
      type: String,
      default: ''
    },
    height: {
      type: Number,
      default: 500
    },
    minHeight: {
      type: Number,
      default: 0
    },
    maxHeight: {
      type: Number,
      default: 0
    },
    tinymceFlag: {
      type: Number,
      default: 1
    },
    listener: String, // 监听的事件，eg: 'input blur focus undo redo execCommand'，多个时使用空格隔开
    customHandle: Function
  },
  // activated() {
  //   this.tinymceFlag++ // 组件缓存的时候用于再次加载，不然有些时候再次加载会出现只显示一个textarea的问题
  // },
  data() {
    let _this = this
    return {
      // tinymceFlag: 1,
      content: this.content,
      // 图片上传状态管理
      uploadingImages: [], // 正在上传的图片列表
      isProcessingImages: false, // 是否正在处理图片
      uploadingCount: 0, // 正在上传的图片数量
      setting: {
        readonly: this.disabled,
        menubar: true, // 上面的菜单隐藏
        selector: `#Editor${_this.itemkey}`, // 多个富文本的时候加上itemkey用于区分
        toolbar: `insertfile undo redo
          | charmap bold italic underline strikethrough subscript  superscript
          | fontsizeselect
          | inserttable table tableprops deletetable cell row
          | alignleft aligncenter alignright alignjustify
          | bullist numlist outdent indent
          | ${_this.custom} image
          `,
        plugins: 'table charmap preview image link paste autoresize',
        language_url: '/tinymce/zh_CN.js', // 配置中文的路径
        language: 'zh_CN', // 本地化设置
        statusbar: false, // 隐藏最底部的状态栏
        convert_urls: false, // 禁止转换 URL
        // relative_urls: true, // 如果该选项设为true，所有通过MCFileManager返回的URL都会与知道的document_base_url相关。如果设为false，所有URL会被转化成绝对URL，默认为true。
        // document_base_url: 'http://*************/',
        // remove_script_host: false, // 如果此选项设置为true，则document_base_url的协议和主机被排除在相对链路之外。
        height: this.height,
        autoresize_bottom_margin: 20, // 底部留白(px)
        autoresize_on_init: true, // 初始化时自动调整
        min_height: this.minHeight || this.height, // 最小高度(px)
        max_height: this.maxHeight || this.height, // 最大高度(px)
        image_advtab: true,
        image_dimensions: false,
        content_style: 'img{max-width:100%;height:auto;}p {margin: 0px;}',
        fontsize_formats: '11px 12px 14px 16px 18px 24px 36px 48px',
        // 粘贴配置
        paste_data_images: true,
        automatic_uploads: true,
        paste_preprocess: (_, args) => {
          console.log('=== 粘贴预处理 ===');
          console.log('原始内容:', args.content);

          // 处理钉钉文档的复杂HTML结构
          let content = args.content;

          // 移除复杂的data属性
          content = content.replace(/data-[^=]*="[^"]*"/g, '');

          // 移除article标签，保留内容
          content = content.replace(/<\/?article[^>]*>/g, '');

          // 提取并处理图片
          const imgRegex = /<img[^>]*src="([^"]*)"[^>]*>/g;
          let match;
          const tempImages = [];

          while ((match = imgRegex.exec(content)) !== null) {
            const imgSrc = match[1];
            console.log('发现图片:', imgSrc);

            // 检查是否是临时链接（包含tmpCode）
            if (imgSrc.includes('tmpCode=') || imgSrc.includes('alidocs.dingtalk.com')) {
              console.log('检测到临时图片链接，需要下载并上传');
              tempImages.push(imgSrc);
            }
          }

          // 简化img标签
          content = content.replace(/<img[^>]*src="([^"]*)"[^>]*>/g,
            '<img src="$1" style="max-width: 100%; height: auto;" />');

          console.log('处理后内容:', content);
          args.content = content;

          // 延迟处理临时图片
          if (tempImages.length > 0) {
            _this.isProcessingImages = true;
            _this.$emit('image-processing-change', true);
            setTimeout(() => {
              _this.downloadAndUploadImages(tempImages);
            }, 500);
          }
        },
        // 图片异步上传处理函数
        images_upload_handler: (blobInfo, success, failure) => {
          // 本地base64图片
          // var reader = new FileReader()
          // reader.onload = function(e) {
          //   // reader.result就是转换成的base64
          //   success(reader.result)
          // }
          // reader.readAsDataURL(blobInfo.blob())

          // 把图片上传到服务器
          var formData = new FormData();
          // 添加下文件
          formData.append('file', blobInfo.blob(), blobInfo.filename());
          // 文件上传的目录
          formData.append('prefix', 'upload');
          // 文件名
          formData.append('key', new Date().getTime() + Math.floor(Math.random() * 150) + blobInfo.filename());
          // _this.$api.uploadScenicFace这个是我调用后台图片上传接口的函数
          _this.$apis.apiBackgroundFileUploadPost(formData).then(res => {
            // 图片上传成功以后调用success,图片就可以在富文本编辑器中显示了
            success(res.data.public_url);
          }).catch(error => {
            failure(error)
          });
        }
      }
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(newValue) {
        // 这里是从列表页编辑时做的内容注入，没有需要可以不写
        if (newValue === undefined) {
          this.content = ''
        } else {
          this.content = newValue
        }
      }
    },
    content: {
      deep: true,
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.$emit("input", newValue);
          console.log(newValue)
        }
      }
    }
  },
  mounted() {},
  methods: {
    change() {
      this.$emit("change")
      // 这里只有输入框改变的时候才会触发，比如自定义模板选择的时候就触发不了
    },

    // 下载并上传临时图片
    async downloadAndUploadImages(imageUrls) {
      console.log('=== 开始处理临时图片 ===');
      console.log('需要处理的图片数量:', imageUrls.length);

      this.uploadingImages = [...imageUrls];
      this.isProcessingImages = true;
      this.$emit('image-processing-change', true);

      for (let i = 0; i < imageUrls.length; i++) {
        const imageUrl = imageUrls[i];
        try {
          console.log(`处理第 ${i + 1} 张图片:`, imageUrl);

          // 下载图片
          const blob = await this.downloadImage(imageUrl);
          console.log('图片下载成功，大小:', blob.size);

          // 上传图片
          const newUrl = await this.uploadImageBlob(blob, `temp_image_${Date.now()}_${i}.png`);
          console.log('图片上传成功，新URL:', newUrl);

          // 替换编辑器中的图片URL
          this.replaceImageUrl(imageUrl, newUrl);

          // 从上传列表中移除
          this.uploadingImages = this.uploadingImages.filter(url => url !== imageUrl);
        } catch (error) {
          console.error(`处理第 ${i + 1} 张图片失败:`, error);
          // 即使失败也要从列表中移除
          this.uploadingImages = this.uploadingImages.filter(url => url !== imageUrl);
        }
      }

      // 所有图片处理完成
      this.isProcessingImages = false;
      this.$emit('image-processing-change', false);
      console.log('=== 所有临时图片处理完成 ===');
    },

    // 下载图片
    async downloadImage(url) {
      try {
        // 使用代理或直接请求（根据CORS策略）
        const response = await fetch(url, {
          mode: 'cors',
          credentials: 'omit'
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.blob();
      } catch (error) {
        console.error('下载图片失败:', error);
        // 如果直接下载失败，尝试通过后端代理下载
        return await this.downloadImageViaProxy(url);
      }
    },

    // 通过Canvas下载图片
    async downloadImageViaProxy(url) {
      try {
        console.log('尝试通过Canvas下载图片:', url);

        return new Promise((resolve, reject) => {
          const img = new Image();
          img.crossOrigin = 'anonymous';

          img.onload = () => {
            try {
              // 创建canvas
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');

              canvas.width = img.width;
              canvas.height = img.height;

              // 绘制图片到canvas
              ctx.drawImage(img, 0, 0);

              // 转换为blob
              canvas.toBlob((blob) => {
                if (blob) {
                  console.log('Canvas转换成功，图片大小:', blob.size);
                  resolve(blob);
                } else {
                  reject(new Error('Canvas转换失败'));
                }
              }, 'image/png');
            } catch (error) {
              console.error('Canvas处理失败:', error);
              reject(error);
            }
          };

          img.onerror = (error) => {
            console.error('图片加载失败:', error);
            reject(new Error('图片加载失败'));
          };

          // 尝试使用代理URL
          const proxyUrl = `https://cors-anywhere.herokuapp.com/${url}`;
          img.src = proxyUrl;

          // 如果代理失败，尝试直接加载
          setTimeout(() => {
            if (!img.complete) {
              console.log('代理加载超时，尝试直接加载');
              img.src = url;
            }
          }, 3000);
        });
      } catch (error) {
        console.error('Canvas下载图片失败:', error);
        throw error;
      }
    },

    // 上传图片Blob
    async uploadImageBlob(blob, filename) {
      const formData = new FormData();
      formData.append('file', blob, filename);
      formData.append('prefix', 'upload');
      formData.append('key', `${Date.now()}_${Math.floor(Math.random() * 9999)}_${filename}`);

      try {
        const response = await this.$apis.apiBackgroundFileUploadPost(formData);
        return response.data.public_url;
      } catch (error) {
        console.error('上传图片失败:', error);
        throw error;
      }
    },

    // 替换编辑器中的图片URL
    replaceImageUrl(oldUrl, newUrl) {
      try {
        // 获取编辑器实例
        const editor = this.getEditorInstance();
        if (!editor) {
          console.error('无法获取编辑器实例');
          return;
        }

        // 获取当前内容
        let content = editor.getContent();

        // 替换URL
        const escapedOldUrl = oldUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`src="${escapedOldUrl}"`, 'g');
        content = content.replace(regex, `src="${newUrl}"`);

        // 设置新内容
        editor.setContent(content);

        console.log('图片URL替换成功');
      } catch (error) {
        console.error('替换图片URL失败:', error);
      }
    },

    // 获取编辑器实例
    getEditorInstance() {
      if (window.tinymce) {
        const editors = window.tinymce.editors;
        if (editors && editors.length > 0) {
          return editors[0];
        }

        const selector = `#Editor${this.itemkey}`;
        return window.tinymce.get(selector);
      }
      return null;
    },

    // 检查是否有临时图片链接
    hasTempImages() {
      const editor = this.getEditorInstance();
      if (!editor) return false;

      const content = editor.getContent();
      return content.includes('tmpCode=') || content.includes('alidocs.dingtalk.com');
    },

    // 获取上传状态（供外部调用）
    getUploadStatus() {
      return {
        isProcessingImages: this.isProcessingImages,
        uploadingCount: this.uploadingImages.length,
        hasTempImages: this.hasTempImages()
      };
    },
    setup(editor) {
      if (this.listener) {
        editor.on(this.listener, e => {
          if (this.customHandle) {
            this.customHandle(e)
          }
        })
      }
      // editor.on('input blur undo redo execCommand', e => {
      //   // 多个触发事件获取最新值
      //   var msg = _this.Editortext.toString() // 获取带html的值
      //   if (_this.itemkey !== undefined && _this.itemkey !== '') {
      //     // 多个富文本时返回值给父组件
      //     _this.$emit('message', {
      //       key: _this.itemkey,
      //       msg: msg
      //     })
      //   } else {
      //     // 单个富文本返回值给父组件
      //     _this.$emit('message', msg)
      //   }
      // })
      // 添加自定义的菜单按钮
      // if (_this.custom.indexOf('menuDateButton') !== -1) {
      //   editor.ui.registry.addMenuButton('menuDateButton', {
      //     // 添加菜单按钮
      //     text: '公式模板',
      //     fetch: function(callback) {
      //       var items = []
      //       let formula = [
      //         { name: '公式1', code: '1' },
      //         { name: '公式2', code: '2' },
      //         { name: '公式3', code: '3' }
      //       ]
      //       formula.map(resitem => {
      //         items.push({
      //           type: 'menuitem',
      //           text: resitem.name,
      //           onAction: function(_) {
      //             editor.insertContent(resitem.name)
      //             editor.input()
      //           }
      //         })
      //       })
      //       callback(items)
      //     }
      //   })
      // }
    }
  }
}
</script>
<style lang="scss">
// 添加下弹窗高度，用于解决抽屉弹窗中嵌套弹窗的层级问题
.tox-tinymce-aux{
  z-index: 999999 !important;
}
</style>
