<template>
  <el-select
    v-model="selectData"
    class="consume_select ps-select"
    :popper-class="popperClass"
    :role="role"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-option
      v-for="item in consumeList"
      :key="item[options.value]"
      :label="item[options.label]"
      :value="item[options.value]"
    ></el-option>
  </el-select>
</template>

<script>
export default {
  name: 'ConsumeSelect',
  props: {
    value: {
      type: [String, Array, Number]
    },
    popperClass: {
      type: String,
      default: 'ps-popper-select'
    },
    options: {
      type: Object,
      default: () => {
        return {
          label: 'name',
          value: 'id'
        }
      }
    },
    role: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      consumeList: []
    }
  },
  computed: {
    selectData: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('update:input', val)
      }
    }
  },
  watch: {
  },
  created() {
    this.getConsumeList()
  },
  mounted() {
  },
  methods: {
    // 获取消费点
    async getConsumeList() {
      let params = {
        company__single_in: this.$store.getters.userInfo.company_id,
        page: 1,
        page_size: 99999
      }
      let api
      if (this.role === 'super') {
        api = this.$apis.apiBackgroundAdminOrganizationConsumeListPost(params)
      } else {
        api = this.$apis.apiBackgroundOrganizationOrganizationConsumeListPost(params)
      }
      const res = await api;
      console.log(res)
      if (res.code === 0) {
        this.consumeList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scope>
// .consume_select_select {
// }
</style>
