<template>
  <div class="record font-size-14">
    <div class="record-content">
      <span class="record-item">
        备案号：
        <el-link href="http://beian.miit.gov.cn/" target="_blank">吉ICP备05000428号</el-link>
      </span>
      <span class="record-item m-l-20">
        <img class="record-icon" src="@/assets/img/record.png" alt="">
        <el-link href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=22010202000255">吉公网安备22010202000255号</el-link>
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  components: {

  },
  props: {

  },
  data() {
    return {

    }
  },
  computed: {

  },
  watch: {

  },
  created() {

  },
  mounted() {

  },
  methods: {

  }
};
</script>

<style scoped lang="scss">
.record{
  .record-content{
    margin: 20px auto 0;
    width: 500px;
    text-align: center;
  }
  .record-item{
    vertical-align: middle;
    .record-icon{
      display: inline-block;
      width: 20px;
      height: 22px;
      margin-right: 2px;
      vertical-align: middle;
    }
  }
}
</style>
