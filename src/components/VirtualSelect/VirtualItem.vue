<template>
  <div
    :class="['virtual-item', { 'is-selected': curId == source[option.value], 'is-disabled': source.disabled }]"
    @click="handleClick"
  >
    <!-- <span>{{ source[option.label] }}</span> -->
    <span v-if="source.ingredient_type==='system'">{{ source[option.label] }}<span style="color: #FF9B45;">（{{ source.ingredient_type_alias }}）</span></span>
    <span v-else>{{ source[option.label] }}</span>
  </div>
</template>

<script>
export default {
  name: "VirtualItem",
  props: {
    curId: {
      type: [String, Number],
      default: ""
    },
    source: {
      type: Object,
      default() {
        return {};
      }
    },
    option: {
      type: Object,
      default() {
        return {
          label: 'name',
          value: 'id'
        }
      }
    }
  },
  methods: {
    dispatch(componentName, eventName, ...rest) {
      let parent = this.$parent || this.$root;
      let name = parent.$options.name;
      while (parent && (!name || name !== componentName)) {
        parent = parent.$parent;
        if (parent) {
          name = parent.$options.name;
        }
      }
      if (parent) {
        parent.$emit.apply(parent, [eventName].concat(rest));
      }
    },
    handleClick() {
      if (this.source.disabled) return
      // 通知 SelectVirtualList 组件，点击了项目
      this.dispatch("SelectVirtualList", "clickVirtualItem", this.source);
    }
  }
};
</script>

<style lang="scss" scoped>
.virtual-item {
  font-size: 14px;
  padding: 0 20px;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #606266;
  height: 32px;
  line-height: 32px;
  box-sizing: border-box;
  cursor: pointer;
  &:hover {
    background-color: #eee;
  }
  &.is-selected {
    color: #409eff;
    background-color: #dbeeff;
  }
  &.is-disabled{
    // background-color: #f5f7fa;
    // border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
  }
}
</style>
