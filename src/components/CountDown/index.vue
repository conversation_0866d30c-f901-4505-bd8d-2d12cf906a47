<template>
  <div>
    <span class="count-down" v-if="timeDown">{{ timeDown }}</span>
  </div>
</template>

<script>
// import { replaceDate } from '@/utils'
import dayjs from 'dayjs'
export default {
  name: 'countDown',
  props: {
    time: {
      require: true,
      type: [Number, String]
    },
    format: {
      type: String,
      default: '{h}:{i}:{s}'
    }
  },
  data() {
    return {
      timeHandle: null,
      timeDown: 0
    }
  },
  mounted() {
    console.log(this.time)
    this.timeout(this.time)
  },
  methods: {
    timeout(end, date) {
      if (!end) {
        this.timeDown = 0
        return
      }
      if (typeof end === 'string') {
        // end = replaceDate(end)
        end = dayjs(end).toDate()
      }
      // end = new Date('2022/06/17 15:12:00').getTime() + (60 * 60 * 24 * 1000)
      let setTime = new Date(end).getTime() // 结束时间
      this.timeHandle = setInterval(() => {
        let nowTime = new Date();
        let timeDiff = setTime - nowTime.getTime();
        if (timeDiff <= 0) {
          this.timeDown = ''
          return clearInterval(this.timeHandle);
        }
        // let day = parseInt(timeDiff / (60 * 60 * 24 * 1000)); //  天数
        let hour = parseInt((timeDiff / (60 * 60 * 1000))); //  小时
        let minute = parseInt(((timeDiff - hour * (60 * 60 * 1000)) / (60 * 1000))); //  分钟
        let second = parseInt(((timeDiff - hour * (60 * 60 * 1000) - minute * (60 * 1000)) / 1000) % 60); //  秒数
        this.timeDown = hour.toString().padStart(2, '0') + ":" + minute.toString().padStart(2, '0') + ":" + second.toString().padStart(2, '0');
        this.timeDiff--;
        if (timeDiff === 0) {
          clearInterval(this.timeHandle);
        }
      }, 1000)
    }
  },
  beforeDestroy() {
    if (this.timeHandle) {
      clearInterval(this.timeHandle)
    }
  }
}
</script>

<style lang="scss" scope>
.count-down{
}
</style>
