<template>
  <div class="evaluate-item m-b-30">
    <div class="evaluate-top flex-between">
      <div class="evalute-top-l">
        <div class="m-b-10">
          <span class="evalute-label blod m-r-40">类型：{{ col.feedback_type_alias }}</span>
          <span class="evalute-label m-r-40">组织:{{ col.company_name }}</span>
          <span class="evalute-label m-r-40">反馈时间：{{ col.create_time }}</span>
          <span class="evalute-label m-r-40">状态：{{ col.feedback_status_alias }}</span>
          <span class="evalute-label m-r-40" style="color: red;" v-if="col.anonymous">匿名反馈</span>
        </div>
        <div v-if="!col.anonymous || type==='Super'">
          <span class="evalute-label m-r-40">手机号：{{ col.phone }}</span>
          <span class="evalute-label m-r-40">用户名：{{ col.person_name }}</span>
          <span class="evalute-label m-r-40">用户编号: {{ col.person_no }}</span>
        </div>
      </div>
      <div class="evalute-top-l m-l-10">
        <span v-if="type==='Super'">
          <el-button v-if="col.feedback_status !== 'delete'" type="danger" plain @click="deleteHandle(col)">删除</el-button>
          <span v-if="col.feedback_status === 'delete'" style="color: red;">已删除</span>
        </span>
        <el-button style="margin-left: 10px;" v-if="canReply" @click="replyHandle(col)">商家回复</el-button>
      </div>
    </div>
    <el-divider class="line"></el-divider>
    <div class="evalute-item-content">
      <div class="label evalute-text m-b-10" style="color:#999;">反馈内容：</div>
      <div class="evalute m-t-10">
        <!-- <div class="label evalute-text m-l-25"></div> -->
        <div class="evalute-img-box m-t-15">
          <el-image v-for="(img, i) in col.feedback_images" :key="img+i" :src="img" class="evalute-img m-r-20" :preview-src-list="col.feedback_images">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </div>
        <div class="evalute-text m-t-15">
          {{ col.remark }}
        </div>
      </div>
      <div v-if="col.feedback_status === 'reply'">
        <el-divider class="line"></el-divider>
        <div class="label evalute-text" style="color:#999;">商家回复（{{col.account_alias}}）：</div>
        <div class="evalute-text m-t-15">
          {{ col.merchant_remark }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Evaluate',
  components: {

  },
  props: {
    col: Object,
    deleteEvaluate: Function,
    type: String
  },
  data() {
    return {
    }
  },
  computed: {
    canReply() {
      let show = false
      console.log(111, this.type)
      if (this.type === 'Super') {
        if (this.col.feedback_type === 'system_suggest' && this.col.feedback_status === 'no_reply') {
          show = true
        }
      } else {
        if (this.col.feedback_status === 'no_reply') {
          show = true
        }
      }
      return show
    }
  },
  watch: {

  },
  created() {

  },
  mounted() {

  },
  methods: {
    deleteHandle(col) {
      this.$emit('deletefeeback', col)
    },
    replyHandle(col) {
      this.$emit('reply', col)
    }
  }
};
</script>

<style scoped lang="scss">
.evaluate-item{
  font-size: 14px;
  padding: 20px;
  border-radius: 12px;
  background-color: #ffffff;
  .evaluate-top{

  }
  .evalute-label{}
  .blod{
    font-weight: 600;
    color: #000000;
  }
  .line{
    margin: 15px 0;
  }
  .evalute-item-content{
    .rate-item{
      display: inline-block;
      .rate-label{
        vertical-align: middle;
      }
      .rate{
        display: inline-block;
        ::v-deep .el-rate__icon{
          font-size: 26px;
        }
      }
    }
    .evalute-img{
      width: 60px;
      height: 60px;
      .el-icon-picture-outline{
        font-size: 60px;
        opacity: .5;
      }
    }
  }
  .evalute-text{
    word-break: break-all;
  }
}
</style>
