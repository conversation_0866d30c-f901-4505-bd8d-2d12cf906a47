<template>
  <select-tree
    v-model="selectData"
    :treeData="addressList"
    :treeProps="treeProps"
    :loadTree="loadAddressList"
    :isLazy="isLazy"
    v-bind="$attrs"
    v-on="$listeners"
    class="search-item-w"
  >
  </select-tree>
</template>

<script>
import { to, deleteEmptyGroup } from '@/utils'
import SelectTree from '@/components/SelectTree'
export default {
  name: 'AddressSelect',
  components: {
    SelectTree
  },
  props: {
    value: {
      // required: true
      type: [String, Number, Array]
    },
    orgId: {
      // required: true
      type: [String, Number]
    },
    popperClass: {
      type: String,
      default: 'ps-popper-select'
    },
    options: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          value: 'id'
        }
      }
    },
    // 是否开启懒加载
    isLazy: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      addressList: [],
      treeProps: {
        value: 'id',
        label: 'name',
        isLeaf: 'is_leaf',
        children: 'children_list'
      }
    }
  },
  computed: {
    selectData: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('update:input', val)
      }
    }
  },
  watch: {
    isLazy(val) {
      if (val) {
        this.getAddressList()
      }
    },
    orgId() {
      if (!this.isLazy && this.orgId) {
        this.getAddressList()
      }
    }
  },
  created() {
  },
  mounted() {
    if (!this.isLazy && this.orgId) {
      this.getAddressList()
    }
  },
  methods: {
    // 获取所有数据
    async getAddressList() {
      this.isLoading = true
      const res = await this.$apis.apiAddressAddersCenterAddressTreeListPost({
        organization_id: this.orgId
      })
      this.isLoading = false
      if (res.code === 0) {
        this.addressList = deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 动态加载远程数据
    async loadAddressList(tree, resolve) {
      let params = {
        page: 1,
        page_size: 99999999,
        used_for_web: true
      }
      if (tree.level === 0) {
        params.level = 0
      } else {
        params.parent_id = [tree.data.id]
        params.level = tree.level
      }
      const [err, res] = await to(this.$apis.apiAddressAddersCenterListPost(params));
      if (err) {
        resolve([])
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        res.data.results.map(item => {
          if (item._has_children) {
            item.is_leaf = false
          } else {
            item.is_leaf = true
          }
        })
        resolve(res.data.results)
      } else {
        resolve([])
        this.$message.error(res.msg)
      }
    },
    getValue(value) {
      this.selectData = value
      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scope>
// .user-group_select {
// }
</style>
