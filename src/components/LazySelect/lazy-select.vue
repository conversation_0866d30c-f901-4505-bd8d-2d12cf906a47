<template>
  <div class="lazy_selectwrapper">
    <el-select
      v-model="lazyselect"
      :placeholder="placeholder"
      class="lazy_select ps-select"
      :multiple="multiple"
      :collapse-tags="collapseTags"
      :disabled="disabled"
      :clearable="clearable"
      :filterable="filterable"
      @change="changeSelectHandle"
      popper-class="ps-popper-select"
    >
      <!-- :filter-method="filterMethod" -->
      <div v-infinite-scroll="loadScroll" :infinite-scroll-disabled="selectOpts.disabledScroll">
        <el-option
          v-for="item in selectList"
          :key="item[selectKey]"
          :label="item[selectName]"
          :value="item[selectKey]"
        ></el-option>
        <p v-if="selectOpts.loadingScroll" class="lazy_select_p">
          加载中
          <i class="el-icon-loading"></i>
        </p>
      </div>
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'LazySelect',
  props: {
    selectName: {
      type: String,
      default() {
        return 'name'
      }
    },
    selectKey: {
      type: String,
      default() {
        return 'id'
      }
    },
    placeholder: {
      type: String,
      default: '请下拉选择'
    },
    multiple: {
      type: Boolean,
      default() {
        return false
      }
    },
    collapseTags: {
      type: Boolean,
      default() {
        return false
      }
    },
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    clearable: {
      type: Boolean,
      default: false
    },
    filterable: {
      type: Boolean,
      default() {
        return false
      }
    },
    selectval: {
      type: [String, Array]
    },
    selectList: {
      type: Array,
      default() {
        return []
      }
    },
    selectOpts: {
      type: Object,
      default() {
        return {
          loadingScroll: false,
          disabledScroll: false,
          hasmore: true, // 是否有更多数据
          pageSize: 10,
          currentPage: 1,
          totalCount: 0
        }
      }
    }
  },
  data() {
    return {
      // lazyselect: ''
    }
  },
  computed: {
    lazyselect: {
      get() {
        return this.selectval
      },
      set(val) {
        this.$emit('update:selectval', val)
      }
    }
  },
  watch: {
    // selectval: function(val, old){
    //   console.log(val)
    // }
  },
  created() {},
  mounted() {
    // this.lazyselect = this.selectVal
  },
  methods: {
    // 加载更多load事件
    loadScroll() {
      if (this.selectOpts.hasmore) {
        // this.selectOpts.loadingScroll = true;
        // this.selectOpts.disabledScroll = true;
        // this.pointSelectOpts.pageSize += 20;
        this.$emit('selectload', { type: 'loading' })
      }
    },
    // change 事件
    changeSelectHandle(val) {
      this.$emit('selectchange', val)
    },
    // 自定义搜索条件
    filterMethod(e) {
      this.$emit('filtermethod', e)
    }
  }
}
</script>

<style lang="scss">
.lazy_selectwrapper {
  .el-select {
    display: block;
  }
  .lazy_select {
    width: 180px;
  }
}
.lazy_select_p {
  text-align: center;
  color: #ff9b45;
  font-size: 14px;
  line-height: 34px;
}
</style>
