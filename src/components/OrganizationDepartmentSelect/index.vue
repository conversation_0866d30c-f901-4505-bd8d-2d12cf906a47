<template>
  <select-tree
    v-model="selectData"
    :treeData="departmentList"
    :treeProps="treeProps"
    :loadTree="loadDepartmentList"
    :isLazy="isLazy"
    v-bind="$attrs"
    v-on="$listeners"
    class="search-item-w"
  >
  </select-tree>
</template>

<script>
// 获取当前组织下的所有部门
import { to, deleteEmptyGroup } from '@/utils'
import SelectTree from '@/components/SelectTree'
export default {
  name: 'OrganizationDepartmentSelect',
  components: {
    SelectTree
  },
  props: {
    value: {
      // required: true
      type: [String, Number, Array]
    },
    popperClass: {
      type: String,
      default: 'ps-popper-select'
    },
    options: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          value: 'id'
        }
      }
    },
    // 是否开启懒加载
    isLazy: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      departmentList: [],
      treeProps: {
        value: 'id',
        label: 'group_name',
        isLeaf: 'is_leaf',
        children: 'children_list'
      }
    }
  },
  computed: {
    selectData: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('update:input', val)
      }
    }
  },
  watch: {
  },
  created() {
  },
  mounted() {
    this.getDepartmentList()
  },
  methods: {
    // 获取所有数据
    async getDepartmentList() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardDepartmentGroupAllTreeListPost()
      this.isLoading = false
      if (res.code === 0) {
        this.departmentList = deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 动态加载远程数据
    async loadDepartmentList(tree, resolve) {
      let params = {
        status: 'enable',
        page: 1,
        page_size: 99999999
      }
      if (tree.level === 0) {
        params.level = 0
      } else {
        params.parent = tree.data.id
      }
      const [err, res] = await to(this.$apis.apiCardServiceCardDepartmentGroupListPost(params));
      if (err) {
        resolve([])
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        res.data.results.map(item => {
          if (item.has_children) {
            item.is_leaf = false
          } else {
            item.is_leaf = true
          }
        })
        resolve(res.data.results)
      } else {
        resolve([])
        this.$message.error(res.msg)
      }
    },
    getValue(value) {
      this.selectData = value
      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scope>
</style>
