<template>
  <el-select
    v-model="selectData"
    class="user-group_select ps-select"
    :popper-class="popperClass"
    :filterable="filterable"
    :disabled="disabled"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-option
      v-for="item in groupList"
      :key="item[options.value]"
      :label="item[options.label]"
      :value="item[options.value]"
      :disabled="autoGroupType ? item.is_auto_group : false"
    ></el-option>
  </el-select>
</template>

<script>
import { deepClone } from '@/utils'

export default {
  name: 'UserGroupSelect',
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: [String, Array, Number]
    },
    popperClass: {
      type: String,
      default: 'ps-popper-select'
    },
    filterable: {
      type: Boolean,
      default: true
    },
    options: {
      type: Object,
      default: () => {
        return {
          label: 'group_name',
          value: 'id'
        }
      }
    },
    optionData: Array,
    showOther: { // 是否显示非本组织创建的
      type: Boolean,
      default: false
    },
    autoGroupType: { // 是否禁用自动分组 目前只在用户管理-用户列表-新增和编辑用到
      type: Boolean,
      default: false
    },
    needLinkage: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      groupList: []
    }
  },
  computed: {
    selectData: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('update:input', val)
      }
    }
  },
  watch: {
    optionData(val) {
      this.groupList = this.optionData
    }
  },
  created() {
    if (!this.optionData) {
      this.userGroupList()
    } else {
      this.groupList = this.optionData
    }
  },
  mounted() {
  },
  methods: {
    async userGroupList() {
      let parms = {}
      const res = await this.$apis.apiCardServiceCardUserGroupListPost({
        is_show_other: this.showOther,
        org_ids: this.needLinkage ? this.selectData : undefined,
        status: 'enable',
        page: 1,
        page_size: 99999,
        ...parms
      })
      if (res.code === 0) {
        this.groupList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 根据标识设置全选或者非全选
    getSelectAllIds() {
      let ids = []
      this.groupList.forEach(item => {
        ids.push(item.id)
      })
      console.log("ids", ids);
      return ids
    }
  }
}
</script>

<style lang="scss" scope>
// .user-group_select {
// }
</style>
